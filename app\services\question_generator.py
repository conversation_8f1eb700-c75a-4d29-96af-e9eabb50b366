from app.services.llm_client import send_messages, LLMServiceConfig
from typing import List, Dict, Any
import asyncio
import json
from app.utils.logger import log_exception
from app.config import Env
from app.utils.token_utils import TokenCalculator
from app.services.mysql_service import get_llm_config
from app.services import *

# 在文件顶部添加常量定义
QUESTION_GENERATOR_DEVICE_ID = "question_generator"


# 定义支持的真值格式常量，便于维护和复用
TRUTHY_VALUES = (True, 1, "true", "True", "TRUE", "1")


class BackgroundExtractor:
    TOKEN_LIMIT = 50000
    llm_service_config_list = {} # 存储不同组织的llm_service_config
    
    @staticmethod
    async def extract(chunks: List[str], language: str = "zh-CN", organization_id: str = "") -> str:
        try:
            total_tokens = TokenCalculator.calculate_tokens(chunks)
            if total_tokens > BackgroundExtractor.TOKEN_LIMIT:
                filtered_chunks = []
                current_tokens = 0
                for chunk in chunks:
                    chunk_tokens = TokenCalculator.calculate_tokens([chunk])
                    if current_tokens + chunk_tokens <= BackgroundExtractor.TOKEN_LIMIT:
                        filtered_chunks.append(chunk)
                        current_tokens += chunk_tokens
                    else:
                        break
                merged_text = "".join(filtered_chunks)
            else:
                merged_text = "".join(chunks)

            language_prompts = {
                "zh-CN": "请用简体中文回答：",
                "zh-HK": "請用香港粵語回答（使用繁體字）：",
                "en": "Please answer in English:",
                "ms": "Sila jawab dalam Bahasa Melayu:",
                "ar": "الرجاء الإجابة باللغة العربية:",
            }

            system_prompt = f"""
            作为一名信息提取专家，你需要帮我完成文档背景的梳理，以便我们的前台工作人员能够方便的从文本里面整理出用户可能会问的问题。{language_prompts[language]}

            1. 文档类型：简要说明这是什么类型的文档（如报告、文章、新闻稿等）。
            2. 核心主题：用1-2句话说明文档的主要内容。
            3. 背景信息：列出若干个与文档内容相关的重要背景信息或上下文。

            请使用以下格式回答：

            文档类型：[您的回答]
            核心主题：[您的回答]
            背景信息：
            - [要点1]
            - [要点2]
            - [要点3]
            - [要点N]

            注意：请仅基于提供的文本内容回答，不要添加推测或额外信息。
            """
            llm_config = get_llm_config(organization_id)
            BackgroundExtractor.llm_service_config_list[organization_id] = llm_config.get(LLM_QUESTION_GENERATOR_CONFIG) or {}
            BackgroundExtractor.llm_service_config_list[organization_id]["model"] = BackgroundExtractor.llm_service_config_list[organization_id].get("model") or Env.DEFAULT_QUESTION_GENERATOR_MODEL

            response = await send_messages(
                llm_service_config=LLMServiceConfig(**BackgroundExtractor.llm_service_config_list[organization_id]),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"<文本>{merged_text}</文本>"},
                ],
                temperature=0.7,
                timeout=60,
                deviceId=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
            )
            return response.content
        except Exception as e:
            log_exception(
                device_id=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
                e=e,
                context="BackgroundExtractor.extract 提取背景信息失败"
            )
            return ""


class QuestionGenerator:
    llm_service_config_list = {} # 存储不同组织的llm_service_config

    @staticmethod
    async def generate(chunk: str, context: str, language: str = "zh-CN", organization_id: str = "") -> str:
        try:
            language_prompts = {
                "zh-CN": "请用简体中文生成问题",
                "zh-HK": "請用香港粵語生成問題（使用繁體字）",
                "en": "Please generate questions in English",
                "ms": "Sila hasilkan soalan dalam Bahasa Melayu",
                "ar": "يرجى إنشاء الأسئلة باللغة العربية",
            }

            system_role = f"""作为一名专业的客服问题生成器，你的任务是基于给定的文本片段创建相关的常见问题。{language_prompts[language]}

            1. 仅基于提供的文本片段生成问题，确保问题可以通过该片段回答。
            2. 生成5个独特、相关的问题。
            3. 问题应该涵盖文本片段中的主要信息点。
            4. 采用口语化的询问，因为你要尽可能的模拟出用户可能会直接询问的问题。我们的业务场景就是前台问询。
            5. 问题应该能引导出有意义的回答，避免过于宽泛或无法回答的问题。

            输出格式：
            - 直接列出问题，每个问题占一行
            - 不要添加编号或其他标记
            - 不要包含任何额外的解释或评论
            """

            user_role = f"""
            文本片段：
            ```
            {chunk}
            ```

            整个文档背景信息（仅供参考，不要直接基于此生成问题）：
            ```
            {context}
            ```
            """

            llm_config = get_llm_config(organization_id)
            QuestionGenerator.llm_service_config_list[organization_id] = llm_config.get(LLM_QUESTION_GENERATOR_CONFIG) or {}
            QuestionGenerator.llm_service_config_list[organization_id]["model"] = QuestionGenerator.llm_service_config_list[organization_id].get("model") or Env.DEFAULT_QUESTION_GENERATOR_MODEL

            response = await send_messages(
                llm_service_config=LLMServiceConfig(**QuestionGenerator.llm_service_config_list[organization_id]),
                messages=[
                    {"role": "system", "content": system_role},
                    {"role": "user", "content": user_role},
                ],
                temperature=0.7,
                timeout=60,
                deviceId=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
            )
            return response.content
        except Exception as e:
            log_exception(
                device_id=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
                e=e,
                context="QuestionGenerator.generate 生成问题失败"
            )
            return ""


class AnswerGenerator:
    llm_service_config_list = {} # 存储不同组织的llm_service_config

    @staticmethod
    async def generate(chunk: str, question: str, language: str = "zh-CN", organization_id: str = "") -> str:
        try:
            language_prompts = {
                "zh-CN": "请用简体中文回答",
                "zh-HK": "請用香港粵語回答（使用繁體字）",
                "en": "Please answer in English",
                "ms": "Sila jawab dalam Bahasa Melayu",
                "ar": "الرجاء الإجابة باللغة العربية",
            }

            system_role = f"""你是一位专业、友善的客服代表。{language_prompts[language]}。请仔细阅读以下上下文信息，并用它来回答用户的问题。

            回答指南：
            1. 如果上下文信息足够回答问题，请提供准确的回答。
            2. 使用友好、口语化的语气，采用清晰、简洁的表述，这个回答将由TTS系统播报。
            3. 如果上下文的信息不足以支持你回答问题，请直接回答"我不知道"。
            4. 不要在回答中提及或引用上下文信息，因为用户并不知道上下文信息这件事情。因此你不要说"基于上下文"之类的内容。
            5. 回答中不要有markdown格式。特别是 ** 号。

            请直接给出回答，无需重复问题或添加额外解释。
            """

            user_role = f"""
            上下文信息：
            ```
            {chunk}
            ```

            用户问题：{question}
            """

            llm_config = get_llm_config(organization_id)
            AnswerGenerator.llm_service_config_list[organization_id] = llm_config.get(LLM_QUESTION_GENERATOR_CONFIG) or {}
            AnswerGenerator.llm_service_config_list[organization_id]["model"] = AnswerGenerator.llm_service_config_list[organization_id].get("model") or Env.DEFAULT_QUESTION_GENERATOR_MODEL

            response = await send_messages(
                llm_service_config=LLMServiceConfig(**AnswerGenerator.llm_service_config_list[organization_id]),
                messages=[
                    {
                        "role": "system",
                        "content": system_role,
                    },
                    {"role": "user", "content": user_role},
                ],
                temperature=0.7,
                timeout=20,
                deviceId=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
            )
            return response.content
        except Exception as e:
            log_exception(
                device_id=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
                e=e,
                context=f"AnswerGenerator.generate 生成答案失败，问题: {question[:100]}"
            )
            return "我不知道"


class QualityChecker:
    llm_service_config_list = {} # 存储不同组织的llm_service_config
    
    @staticmethod
    async def check(chunk: str, question: str, answer: str, context: str = "", language: str = "zh-CN", organization_id: str = "") -> Dict[str, Any]:
        try:
            language_prompts = {
                "zh-CN": "请用简体中文评估",
                "zh-HK": "請用香港粵語評估（使用繁體字）",
                "en": "Please evaluate in English",
                "ms": "Sila nilaikan dalam Bahasa Melayu",
                "ar": "يرجى التقييم باللغة العربية",
            }

            system_role = f"""作为QA质量评估专家，{language_prompts[language]}。
            评估问题和答案的质量，确保它们满足以下标准：

            1. 答案应该主要基于给定的文本内容，但如果答案包含了符合背景信息和常识的合理扩展，也可以接受
            2. 问题必须清晰且有意义，对于一些用户不太可能会问，或者特别奇怪或者弱智的问题，我们也是不予通过的
            3. 答案必须完整地回答了问题
            4. 如果答案超出了文本范围，但符合背景信息和常识认知，可以酌情通过
            5. 如果答案是"我不知道"，则判定为不通过
            6. 问题和回答里面都禁止含有特殊字符，因为例如 - 和 **，因为我们的系统无法解析这些格式，展示出来会很奇怪。如有的话，需要在润色的问题和回答里面删除，但是这不会影响问题是否通过质量检查
            7. 问题的描述应该是口语话的，同样的回答的内容应该也是口语化，因为回答的内容需要在TTS系统里面播报，但是如果回答是分点回答的话还是可以保留原来的格式。

            如果需要，对问题和答案进行适当的润色，使其更加清晰和准确。

            请以JSON格式回复，包含以下字段：
            - "reason": 字符串，如果通过检查，则值可以为空。如未通过检查则说明原因。
            - "pass": 布尔值，表示是否通过质量检查
            - "question": 字符串，润色后的问题（如果通过检查）
            - "answer": 字符串，润色后的答案（如果通过检查）
            """

            user_role = f"""
            文本内容：
            ```
            {chunk}
            ```

            背景信息：
            ```
            {context}
            ```

            问题：{question}
            答案：{answer}

            请确保返回有效的JSON格式。
            """

            llm_config = get_llm_config(organization_id)
            QualityChecker.llm_service_config_list[organization_id] = llm_config.get(LLM_QUESTION_GENERATOR_CONFIG) or {}
            QualityChecker.llm_service_config_list[organization_id]["model"] = QualityChecker.llm_service_config_list[organization_id].get("model") or Env.DEFAULT_QUESTION_GENERATOR_MODEL

            response = await send_messages(
                llm_service_config=LLMServiceConfig(**QualityChecker.llm_service_config_list[organization_id]),
                messages=[
                    {"role": "system", "content": system_role},
                    {"role": "user", "content": user_role},
                ],
                temperature=None,
                timeout=60,
                response_format={"type": "json_object"},
                deviceId=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
            )

            try:
                if not hasattr(response, 'content'):
                    raise ValueError("Response has no content attribute")
                
                # 解析 JSON 字符串
                content = json.loads(response.content)
                
                # 清理字典中的字符串值
                for key in content:
                    if isinstance(content[key], str):
                        content[key] = content[key].replace('**', '')

                return content
            except (json.JSONDecodeError, ValueError) as e:
                log_exception(
                    device_id=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
                    e=e,
                    context=f"JSON parsing failed in QualityChecker.check for question: {question[:100]}, raw response: {response.content if hasattr(response, 'content') else 'No content'}"
                )
                return {
                    "pass": False,
                    "reason": f"响应格式错误: {str(e)}",
                    "question": question,
                    "answer": answer
                }

        except Exception as e:
            log_exception(
                device_id=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
                e=e,
                context=f"QualityChecker.check 质量检查失败，问题: {question[:100]}"
            )
            return {"pass": False, "reason": "质量检查过程出错", "question": question, "answer": answer}


class VariantQuestionGenerator:
    llm_service_config_list = {} # 存储不同组织的llm_service_config
    
    @staticmethod
    async def generate(answer: str, question: str, language: str = "zh-CN", organization_id: str = "") -> str:
        try:
            language_prompts = {
                "zh-CN": "请用简体中文生成问题变体",
                "zh-HK": "請用香港粵語生成問題變體（使用繁體字）",
                "en": "Please generate question variants in English",
                "ms": "Sila hasilkan variasi soalan dalam Bahasa Melayu",
                "ar": "يرجى إنشاء متغيرات الأسئلة باللغة العربية",
            }

            system_role = f"""作为一个专业的问题变体生成器，{language_prompts[language]}。你的任务是基于给定的原始问题和答案，创建5个不同但相关的问题。

            要求：
            1. 所有生成的问题必须与提供的标准答案相匹配
            2. 使用自然、口语化的表达方式，符合用户实际提问习惯
            3. 如果原始问题是单个名词或短语：
               - 生成探询性问题，如"X具体是做什么用的？"
               - 生成场景类问题，如"我想要使用X，该怎么操作？"
               - 生成功能探索类问题，如"X支持哪些功能？"
            4. 如果原始问题是完整问题：
               - 使用不同的口语表达方式改写
               - 考虑用户可能的不同提问角度
               - 保持问题的核心意图不变
            5. 避免与原始问题重复

            输出格式：
            - 直接列出问题，每个问题占一行
            - 不要添加编号或其他标记
            - 不要包含任何额外的解释或评论
            - 不要有特殊的符号
            """
            
            user_role = f"""
            原始问题：{question}
            
            标准答案：{answer}
            """

            llm_config = get_llm_config(organization_id)
            VariantQuestionGenerator.llm_service_config_list[organization_id] = llm_config.get(LLM_QUESTION_GENERATOR_CONFIG) or {}
            VariantQuestionGenerator.llm_service_config_list[organization_id]["model"] = VariantQuestionGenerator.llm_service_config_list[organization_id].get("model") or Env.DEFAULT_QUESTION_GENERATOR_MODEL

            response = await send_messages(
                llm_service_config=LLMServiceConfig(**VariantQuestionGenerator.llm_service_config_list[organization_id]),
                messages=[
                    {"role": "system", "content": system_role},
                    {"role": "user", "content": user_role},
                ],
                temperature=0.7,
                timeout=60,
                deviceId=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
            )
            return response.content
        except Exception as e:
            log_exception(
                device_id=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
                e=e,
                context=f"VariantQuestionGenerator.generate 生成问题变体失败，原问题: {question[:100]}"
            )
            return ""


class ProgressEvent:
    def __init__(self, stage: str, message: str, percentage: float = 0):
        self.stage = stage
        self.message = message
        self.percentage = percentage
        self.data = None

    def to_dict(self):
        result = {
            "stage": self.stage,
            "message": self.message,
            "percentage": self.percentage,
        }
        if self.data is not None:
            result["data"] = self.data
        return result


# 问题生成器工作流，用于处理文档中的问题生成
class QuestionGeneratorWorkflow(QuestionGenerator):
    CONCURRENCY_LIMIT = 6

    @staticmethod
    def update_llm_service_config(organization_id: str, llm_service_config: dict):
        """更新模型设置"""
        # QuestionGeneratorWorkflow和AiChat的设计并不一样，AiChat是每个组织一个实例，而QuestionGeneratorWorkflow是每个组织共用一个静态方法节省内存
        # 因此需要建立一个llm_service_config_list来存储不同组织的llm_service_config
        if not organization_id or not isinstance(llm_service_config, dict):
            return
            
        # 需要维护配置的类列表
        QAHandlers = [
            BackgroundExtractor,
            QuestionGenerator,
            AnswerGenerator,
            QualityChecker,
            VariantQuestionGenerator
        ]
        
        # 初始化或更新配置
        for handler in QAHandlers:
            handler.llm_service_config_list.setdefault(organization_id, {})
            for key, value in llm_service_config.items():
                # 只检查key是否为空，如"model": "openai.gpt-4o-2024-11-20"，model为空，则不更新
                if not key:
                    continue
                    
                handler.llm_service_config_list[organization_id][key] = value


    @staticmethod
    def process_questions(chunk: str, questions: str) -> List[Dict[str, str]]:
        return [
            {"chunk": chunk, "question": q.strip()}
            for q in questions.splitlines()
            if q.strip()
        ]

    @staticmethod
    def organize_to_json(answer: str, questions: str) -> Dict[str, Any]:
        return {
            "qna_pair": {
                "answer": answer,
                "questions": [q.strip() for q in questions.splitlines() if q.strip()],
            }
        }

    @staticmethod
    async def process_chunk_batch(
        chunk: str, context_info: str, language: str = "zh-CN", organization_id: str = ""
    ) -> List[Dict[str, Any]]:
        batch_results = []
        semaphore = asyncio.Semaphore(QuestionGeneratorWorkflow.CONCURRENCY_LIMIT)

        questions = await QuestionGeneratorWorkflow.generate(
            chunk, context_info, language, organization_id
        )
        expanded_data = QuestionGeneratorWorkflow.process_questions(chunk, questions)

        async def process_with_semaphore(item):
            try:
                async with semaphore:
                    return await QuestionGeneratorWorkflow.process_single_question(
                        item,
                        context_info,
                        language,
                        organization_id
                    )
            except Exception as e:
                log_exception(
                    device_id=f"{QUESTION_GENERATOR_DEVICE_ID}_{organization_id}",
                    e=e,
                    context=f"处理问题失败: {item.get('question', '未知问题')[:100]}"
                )
                return None

        tasks = [process_with_semaphore(item) for item in expanded_data]
        for completed_task in asyncio.as_completed(tasks):
            result = await completed_task
            if result:
                batch_results.append(result)
        return batch_results

    @staticmethod
    async def process_single_question(
        item: Dict[str, str], 
        context_info: str,
        language: str = "zh-CN",
        organization_id: str = ""
    ) -> Dict[str, Any]:
        answer = await AnswerGenerator.generate(
            item["chunk"], item["question"], language, organization_id
        )
        if answer != "None":
            quality_result = await QualityChecker.check(
                item["chunk"], 
                item["question"], 
                answer,
                context_info,
                language,
                organization_id
            )

            if quality_result.get("pass") in TRUTHY_VALUES:
                variant_questions = await VariantQuestionGenerator.generate(
                    quality_result["answer"],
                    quality_result["question"],
                    language,
                    organization_id
                )
                return QuestionGeneratorWorkflow.organize_to_json(
                    quality_result["answer"], 
                    variant_questions
                )["qna_pair"]
        return None

    @staticmethod
    async def process(chunks: List[str], language: str = "zh-CN", organization_id: str = ""):
        """使用异步生成器处理文档并yield进度和结果"""
        yield ProgressEvent(stage="start", message="开始处理文档", percentage=round(20.00, 2))

        yield ProgressEvent(
            stage="extract_background", message="提取文章背景信息", percentage=round(21.00, 2)
        )

        context_info = await BackgroundExtractor.extract(chunks, language, organization_id)
        print(context_info)

        final_qna_pairs = []
        total_chunks = len(chunks)
        progress_per_chunk = 76.00 / total_chunks

        for i, chunk in enumerate(chunks):
            current_progress = 22.00 + progress_per_chunk * i
            if i == total_chunks - 1:
                current_progress = 98.00

            yield ProgressEvent(
                stage="chunk_processing",
                message=f"处理文本块（{i + 1}/{total_chunks}）",
                percentage=round(current_progress, 2),
            )

            chunk_results = await QuestionGeneratorWorkflow.process_chunk_batch(
                chunk, context_info, language, organization_id
            )
            final_qna_pairs.extend(chunk_results)

        complete_event = ProgressEvent(
            stage="complete", message="处理完成！", percentage=round(100.00, 2)
        )
        complete_event.data = final_qna_pairs
        yield complete_event

import json
import httpx
import requests
import time
import asyncio

from app.services import SOURCE_LLM_TEXT, SOURCE_STATUS
from app.utils.net_utils import (
    RESPONSE_CODE_CONTINUE,
    RESPONSE_CODE_END,
    RESPONSE_CODE_SEGMENT_END,
    wrap_chat_data,
    wrap_response,
)


# 调试配置
DEBUG = True  # 控制是否打印调试信息

# 在文件顶部添加超时常量
FIRST_RESPONSE_TIMEOUT = 10  # 首次响应超时时间（秒）
STREAM_CHUNK_TIMEOUT = 15    # 数据块之间的超时时间（秒）
TOTAL_TIMEOUT = 120         # 总连接超时时间（秒）

# 用于存储 topic_id 和对应的 pulling_url
_topic_pulling_urls = {}

async def store_pulling_url(topic_id: str, pulling_url: str):
    """存储 topic_id 对应的 pulling_url"""
    _topic_pulling_urls[topic_id] = pulling_url
    if DEBUG:
        print(f"存储topic {topic_id}的pulling URL: {pulling_url}")

async def get_pulling_url(topic_id: str) -> str:
    """获取 topic_id 对应的 pulling_url"""
    return _topic_pulling_urls.get(topic_id)

async def remove_pulling_url(topic_id: str):
    """移除 topic_id 对应的 pulling_url"""
    if topic_id in _topic_pulling_urls:
        del _topic_pulling_urls[topic_id]
        if DEBUG:
            print(f"移除topic {topic_id}的pulling URL")

def send_message_async(topic, content, flows, base_url, headers, instance_id):
    """
    发送异步消息到指定会话。
    """
    url = f"{base_url}/api/de-connect/{instance_id}/push"
    payload = {
        "topic": topic,
        "content": content,
        "params": {
            "flows": flows
        },
        "async": True
    }
    
    try:
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"发送请求到: {url}")
            print(f"请求头: {headers}")
            print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}") 
            print(f"{'='*50}\n")
            
        response = requests.post(url, headers=headers, json=payload)
        
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            try:
                print(f"响应体: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
            except:
                print(f"响应体: {response.text}")
            print(f"{'='*50}\n")
            
        response.raise_for_status()
        data = response.json()
        if data.get("code") == 200:
            pulling_url = f"{base_url}{data['data']['pulling']}"
            return pulling_url
        return None
    except requests.exceptions.RequestException as e:
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"请求异常: {str(e)}")
            print(f"{'='*50}\n")
        return None

def pull_stream(pulling_url, topic_id):
    """
    拉取流式输出内容。
    """
    headers = {"Accept": "text/event-stream"}
    all_content = []
    
    try:
        with requests.get(pulling_url, headers=headers, stream=True) as response:
            response.raise_for_status()
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith('data: '):
                        json_str = decoded_line[6:]
                        if json_str == "finished":
                            break
                        try:
                            data = json.loads(json_str)
                            if data.get("code") == 200:
                                status = data["data"].get("status")
                                content = data["data"].get("content")
                                if content:
                                    all_content.append(content)
                                    wrapped_data = wrap_chat_data(topic_id, SOURCE_LLM_TEXT, message=content)
                                    wrapped_response = wrap_response(wrapped_data, RESPONSE_CODE_CONTINUE)
                            
                            if status == "completed":
                                final_content = "".join(all_content)
                                wrapped_data = wrap_chat_data(topic_id, SOURCE_LLM_TEXT, message=final_content)
                                wrapped_response = wrap_response(wrapped_data, RESPONSE_CODE_END)
                                return wrapped_response
                        except json.JSONDecodeError:
                            continue
    except requests.exceptions.RequestException:
        pass
    return None      

def create_new_topic(base_url, headers, instance_id):
    """
    创建新的会话并获取topic id。
    """
    url = f"{base_url}/api/de-connect/{instance_id}/topics"
    payload = {
        "name": "智能问答会话"
    }
    
    try:
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"创建新会话请求: {url}")
            print(f"请求头: {headers}")
            print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            print(f"{'='*50}\n")
            
        response = requests.put(url, headers=headers, json=payload)
        
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"创建会话响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            try:
                print(f"响应体: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
            except:
                print(f"响应体: {response.text}")
            print(f"{'='*50}\n")
            
        response.raise_for_status()
        data = response.json()
        if data.get("code") == 200:
            return data["data"]["id"]
        return None
    except requests.exceptions.RequestException as e:
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"创建会话异常: {str(e)}")
            print(f"{'='*50}\n")
        return None

def get_or_create_topic(base_url, headers, instance_id, session_id=None):
    """
    获取或创建topic id。
    如果session_id存在，直接返回；否则创建新的topic。
    """
    if session_id:
        return session_id
    return create_new_topic(base_url, headers, instance_id)

async def abort_stream(pulling_url: str, topic_id: str, sk: str) -> bool:
    """
    中断正在进行的流式输出
    """
    try:
        # 移除 topic_id 验证，因为 pulling_url 使用了转换后的标识符
        abort_url = pulling_url.replace('/pull/', '/abort/')
        
        headers = {
            "Accept": "application/json, text/plain, */*",
            "sk": sk
        }
        
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"中断请求URL: {abort_url}")
            print(f"请求头: {headers}")
            print(f"{'='*50}\n")
            
        async with httpx.AsyncClient() as client:
            response = await client.delete(abort_url, headers=headers)
            
            if DEBUG:
                print(f"\n{'='*50}")
                print(f"中断响应状态码: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                try:
                    print(f"响应体: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
                except:
                    print(f"响应体: {response.text}")
                print(f"{'='*50}\n")
                
            response.raise_for_status()
            data = response.json()
            success = data.get("code") == 200 and data.get("data") == 1
            if DEBUG:
                print(f"中断topic {topic_id}的流式输出 {'成功' if success else '失败'}")
            return success
    except Exception as e:
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"中断topic {topic_id}的流式输出发生错误: {str(e)}")
            print(f"{'='*50}\n")
        return False

async def handle_chat(user_input: str, session_id: str = None, custom: dict = None):
    """
    处理聊天请求，返回异步生成器
    """
    try:
        if not custom:
            raise ValueError("必须提供custom配置参数")
            
        # 确保必要的配置参数存在
        required_params = ['base_url', 'sk', 'instance_id', 'flows']
        for param in required_params:
            if param not in custom:
                raise ValueError(f"缺少必要的配置参数: {param}")
                
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"处理聊天请求:")
            print(f"用户输入: {user_input}")
            print(f"会话ID: {session_id}")
            print(f"自定义配置: {custom}")
            print(f"{'='*50}\n")
            
        # 从custom中获取配置
        base_url = custom['base_url']
        sk = custom['sk']
        instance_id = custom['instance_id']
        flows = custom['flows']
        
        headers = {
            "Content-Type": "application/json",
            "sk": sk
        }
            
        topic_id = get_or_create_topic(base_url, headers, instance_id, session_id)
        if not topic_id:
            raise ValueError("获取topic失败")
            
        if DEBUG:
            print(f"使用的topic_id: {topic_id}")
            
        # 检查并中断当前topic的pulling url
        current_pulling_url = await get_pulling_url(topic_id)
        if current_pulling_url:
            if DEBUG:
                print(f"检测到topic {topic_id}的现有会话，尝试中断")
            abort_success = await abort_stream(current_pulling_url, topic_id, sk)
            if abort_success:
                if DEBUG:
                    print(f"成功中断topic {topic_id}的之前流式输出")
                # 确保清理之前的pulling_url
                await remove_pulling_url(topic_id)
                # 添加短暂延迟，确保系统有时间完全清理之前的会话
            else:
                if DEBUG:
                    print(f"警告：未能成功中断topic {topic_id}的之前流式输出")
                # 如果中断失败，创建新的topic以确保干净的会话环境
                topic_id = create_new_topic(base_url, headers, instance_id)
                if not topic_id:
                    raise ValueError("创建新topic失败")

        total_start_time = time.time()
        
        if DEBUG:
            print(f"\n{'='*50}")
            print(f"最终使用的配置:")
            print(json.dumps(custom, ensure_ascii=False, indent=2))
            print(f"{'='*50}\n")
        
        # 修改URL构建方式
        pulling_url = send_message_async(topic_id, user_input, flows, base_url, headers, instance_id)
        pulling_time = time.time()
        if DEBUG:
            print(f"[{pulling_time - total_start_time:.2f}秒] 获取pulling_url: {pulling_url}")
        if not pulling_url:
            raise ValueError("发送消息获取pulling_url失败")
        
        # 存储新的pulling_url
        await store_pulling_url(topic_id, pulling_url)
        
        headers = {"Accept": "text/event-stream"}
        all_content = []
        first_chunk_received = False
        
        try:
            # 设置多层次超时控制
            timeout = httpx.Timeout(
                timeout=TOTAL_TIMEOUT,          # 总超时
                connect=FIRST_RESPONSE_TIMEOUT, # 连接超时
                read=STREAM_CHUNK_TIMEOUT      # 数据块读取超时
            )
            
            if DEBUG:
                print(f"\n{'='*50}")
                print(f"开始流式请求:")
                print(f"URL: {pulling_url}")
                print(f"请求头: {headers}")
                print(f"超时设置: {timeout}")
                print(f"{'='*50}\n")
                
            async with httpx.AsyncClient(timeout=timeout) as client:
                async with client.stream('GET', pulling_url, headers=headers) as response:
                    if DEBUG:
                        print(f"\n{'='*50}")
                        print(f"流式响应初始状态码: {response.status_code}")
                        print(f"响应头: {dict(response.headers)}")
                        print(f"{'='*50}\n")
                        
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith('data: '):
                            total_time = time.time() - total_start_time
                            
                            # 打印原始数据
                            if DEBUG:
                                print(f"\n[{total_time:.2f}秒] 原始数据:")
                                print(f"{'#'*50}")
                                print(line)
                                print(f"{'#'*50}")
                            
                            json_str = line[6:]
                            if json_str == "finished":
                                print(f"[{total_time:.2f}秒] 流式响应结束")
                                break
                            
                            try:
                                data = json.loads(json_str)
                                if DEBUG:
                                    print(f"\n[{total_time:.2f}秒] 解析后的JSON数据:")
                                    print(f"{'$'*50}")
                                    print(json.dumps(data, ensure_ascii=False, indent=2))
                                    print(f"{'$'*50}")
                                    
                                if data.get("code") == 200:
                                    steps = data["data"].get("steps")
                                    
                                    # 从steps中提取content并只处理最后一条状态消息
                                    if steps:
                                        try:
                                            # 将steps按行分割，只取最后一行
                                            step_lines = steps.strip().split('\n')
                                            if step_lines:
                                                last_step = step_lines[-1].strip()
                                                if DEBUG:
                                                    print(f"\n[{total_time:.2f}秒] 处理steps:")
                                                    print(f"{'%'*50}")
                                                    print(f"原始steps: {steps}")
                                                    print(f"分割后的行数: {len(step_lines)}")
                                                    print(f"最后一行: {last_step}")
                                                    print(f"{'%'*50}")
                                                    
                                                # 首先尝试JSON解析
                                                try:
                                                    # 首先尝试JSON解析
                                                    step_data = json.loads(last_step.lstrip("- "))
                                                    if "content" in step_data:
                                                        status_content = step_data["content"]
                                                    else:
                                                        raise json.JSONDecodeError("No content field", "", 0)
                                                except json.JSONDecodeError:
                                                    # JSON解析失败，作为普通文本处理
                                                    if last_step.startswith("- "):
                                                        status_content = last_step[2:].strip()
                                                    else:
                                                        status_content = last_step.strip()

                                                # 统一处理状态消息
                                                if status_content:
                                                    if DEBUG:
                                                        print(f"\n[{total_time:.2f}秒] 提取的状态内容:")
                                                        print(f"{'&'*50}")
                                                        print(f"状态内容: {status_content}")
                                                        print(f"{'&'*50}")
                                                        
                                                    status_data = wrap_chat_data(
                                                        topic_id,
                                                        SOURCE_STATUS,
                                                        status_content,
                                                        extra={"custom": "hr_tech_china", "language": "zh-CN"}
                                                    )
                                                    response_data = wrap_response(status_data, RESPONSE_CODE_SEGMENT_END)
                                                    if DEBUG:
                                                        print(f"\n[{time.time() - total_start_time:.2f}秒] Yield状态消息:")
                                                        print(f"{'@'*50}")
                                                        print(f"Content: {status_content}")
                                                        print(f"Raw response_data: {response_data}")
                                                        print(f"{'@'*50}")
                                                    yield response_data
                                        except Exception as e:
                                            if DEBUG:
                                                print(f"\n{'!'*50}")
                                                print(f"处理steps时发生错误: {str(e)}")
                                                print(f"原始steps数据: {steps}")
                                                print(f"{'!'*50}\n")
                                    
                                    status = data["data"].get("status")
                                    content = data["data"].get("content")
                                    if content:
                                        # 删除content中的**和##符号
                                        content = content.replace('**', '').replace('#', '')
                                        
                                        if not first_chunk_received:
                                            first_chunk_time = time.time() - total_start_time
                                            print(f"\n[首次响应耗时: {first_chunk_time:.2f}秒]")
                                            first_chunk_received = True
                                        
                                        if DEBUG:
                                            print(f"\n[{total_time:.2f}秒] Yield内容消息:")
                                            print(f"{'='*50}")
                                            print(f"Content: {content}")
                                            print(f"{'='*50}")
                                        
                                        all_content.append(content)
                                        yield_data = wrap_response(
                                            wrap_chat_data(
                                                topic_id, 
                                                SOURCE_LLM_TEXT, 
                                                message=content,
                                                extra={"custom": "hr_tech_china", "language": "zh-CN"}
                                            ),
                                            RESPONSE_CODE_CONTINUE
                                        )
                                        
                                        if DEBUG:
                                            print(f"\n[{total_time:.2f}秒] 实际yield的数据:")
                                            print(f"{'*'*50}")
                                            if isinstance(yield_data, dict):
                                                print(json.dumps(yield_data, ensure_ascii=False, indent=2))
                                            else:
                                                print(f"类型: {type(yield_data)}, 内容: {str(yield_data)}")
                                            print(f"{'*'*50}")
                                            
                                        yield yield_data
                                    
                                    if status == "completed":
                                        final_content = "".join(all_content)
                                        # 确保最终内容没有**和##符号
                                        final_content = final_content.replace('**', '').replace('#', '')
                                        
                                        if DEBUG:
                                            print(f"\n[{total_time:.2f}秒] 完整响应:")
                                            print(f"{'='*50}")
                                            print(f"{final_content.strip()}")
                                            print(f"{'='*50}\n")
                                        
                                        # 在返回最终结果前移除pulling_url
                                        await remove_pulling_url(topic_id)
                                        
                                        final_data = wrap_response(
                                            wrap_chat_data(
                                                topic_id, 
                                                SOURCE_LLM_TEXT, 
                                                message=final_content,
                                                extra={"custom": "hr_tech_china", "language": "zh-CN"}
                                            ),
                                            RESPONSE_CODE_END
                                        )
                                        
                                        if DEBUG:
                                            print(f"\n[{total_time:.2f}秒] 最终yield的数据:")
                                            print(f"{'*'*50}")
                                            if isinstance(final_data, dict):
                                                print(json.dumps(final_data, ensure_ascii=False, indent=2))
                                            else:
                                                print(f"类型: {type(final_data)}, 内容: {str(final_data)}")
                                            print(f"{'*'*50}")
                                            
                                        yield final_data
                                        return
                            except json.JSONDecodeError as e:
                                if DEBUG:
                                    print(f"\n{'!'*50}")
                                    print(f"JSON解析错误: {str(e)}, 原始数据: {json_str}")
                                    print(f"{'!'*50}\n")
                                    
        except httpx.TimeoutException as e:
            # 发生超时异常时也要移除pulling_url
            await remove_pulling_url(topic_id)
            if DEBUG:
                print(f"\n{'!'*50}")
                print(f"超时异常: {str(e)}")
                print(f"{'!'*50}\n")
            if "connect" in str(e):
                raise ValueError(f"首次响应超时（超过{FIRST_RESPONSE_TIMEOUT}秒）")
            elif "read" in str(e):
                raise ValueError(f"数据流接收超时（数据块间隔超过{STREAM_CHUNK_TIMEOUT}秒）")
            else:
                raise ValueError(f"总连接超时（超过{TOTAL_TIMEOUT}秒）")
        except httpx.HTTPError as e:
            # 发生HTTP错误时也要移除pulling_url
            await remove_pulling_url(topic_id)
            if DEBUG:
                print(f"\n{'!'*50}")
                print(f"HTTP错误: {str(e)}")
                print(f"{'!'*50}\n")
            error_msg = f"流式响应处理失败: 状态码={e.response.status_code if hasattr(e, 'response') else 'unknown'}, "
            error_msg += f"URL={e.request.url if hasattr(e, 'request') else 'unknown'}, "
            error_msg += f"详细信息={str(e)}"
            raise ValueError(error_msg)
        except Exception as e:
            # 发生其他异常时也要移除pulling_url
            await remove_pulling_url(topic_id)
            if DEBUG:
                print(f"\n{'!'*50}")
                print(f"未知错误: {str(e)}")
                print(f"{'!'*50}\n")
            raise Exception(f"未知错误: {str(e)}")
                            
    except ValueError as e:
        if DEBUG:
            print(f"\n{'!'*50}")
            print(f"值错误: {str(e)}")
            print(f"{'!'*50}\n")
        raise ValueError(str(e))
    except Exception as e:
        if DEBUG:
            print(f"\n{'!'*50}")
            print(f"异常: {str(e)}")
            print(f"{'!'*50}\n")
        raise Exception(f"未知错误: {str(e)}")

async def test_chat():
    # 更新后的配置信息
    custom = {
        "base_url": "https://apps.jinrirencai.com",
        "instance_id": "dangwugov06de001",
        "sk": "ff908081965601960191460192688006",
        "flows": "chat-futian-policy-chat-extra-questions-lite-yuanling.yml",
    }
    
    # 测试问题
    test_question = "你好，请介绍一下你自己"
    
    try:
        # 调用 handle_chat 函数并处理流式响应
        async for response in handle_chat(test_question, session_id=None, custom=custom):
            # 打印每个响应
            print(response)
    except Exception as e:
        print(f"发生错误：{str(e)}")

# 运行测试
if __name__ == "__main__":
    asyncio.run(test_chat())
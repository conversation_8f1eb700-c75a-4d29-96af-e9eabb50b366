import json
import logging
import app.utils.net_utils as net_utils
from app.utils.net_utils import (
    wrap_error,
    wrap_response,
    wrap_chat_data,
    get_value,
    safe_format,
    ResponseException,
)
from typing import AsyncGenerator, Any, Tuple
from app.services import *
from app.utils.net_utils import (
    RESPONSE_CODE_END,
    RESPONSE_CODE_SEGMENT_END,
    REQUEST_TIMEOUT,
    retry_decorator,
)
from app.config import Env
from app.utils.logger import log_exception, log_text
import asyncio
import aiohttp
from fastapi_babel import _
from datetime import datetime, timedelta
from app.services.config_management import handle_get_appointment_slots

from app.services.llm_client import LLMServiceConfig, send_messages

from app.base.jinja2_formatter import templater
from app.base.store_utils import RESPONSE, get_ctx_with_bg_info


# 配置日志格式
logging.basicConfig(
    level=logging.WARNING,
    format="%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)


class BizHandler(IntentionHandler):
    def __init__(
        self,
        promptId: str,
        prompt: str,
        functions: list,
        biz_data: dict,
        llm_service_config: LLMServiceConfig = None,
    ):
        super().__init__()
        self.promptId = promptId
        self.prompt_template = prompt
        self.prompt = safe_format(prompt, biz_data)
        self.functions = functions
        if not self.prompt and not self.functions:
            raise ValueError(f"prompt and functions is empty, promptId: {promptId}")
        self.llm_service_config = dict(llm_service_config) if llm_service_config else {}
        self.llm_service_config["model"] = self.llm_service_config.get("model") or Env.DEFAULT_BIZ_MODEL

    async def request(self, path, params):
        try:
            return await self.http_request(path, params)
        except Exception as e:
            err_msg = log_exception(DEVICE_BIZ_SERVER, e, "BizHandler")
            return wrap_error(net_utils.ERROR_BIZ_REQUEST, err_msg, path)

    def log_retry(retry_state):
        exception = retry_state.outcome.exception()
        retry_message = f"准备重试: 次数 {retry_state.attempt_number}, 异常:{type(exception).__name__}"
        log_text(DEVICE_BIZ_SERVER, retry_message)
        print(retry_message)

    @retry_decorator(log_retry)
    async def http_request(self, path, params):
        json_data = {"path": path, "params": params}
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    Env.BIZ_SERVER_URL, json=json_data, timeout=REQUEST_TIMEOUT
                ) as response:
                    response.raise_for_status()
                    json_response = await response.json()
                    print(f"request json: {json_data}")
                    print(f"json_response: {json_response}")
                    
                    # 添加业务错误日志记录
                    response_obj = Response(**json_response)
                    if not response_obj.success:
                        error_detail = (
                            f"业务请求失败 - URL: {Env.BIZ_SERVER_URL}, "
                            f"路径: {path}, "
                            f"参数: {params}, "
                            f"响应: {response_obj.errorMsg}"
                        )
                        log_text(DEVICE_BIZ_SERVER, error_detail)
                    return response_obj
                    
        except (
            aiohttp.ClientConnectorError,
            aiohttp.ServerTimeoutError,
            aiohttp.ClientOSError,
            TimeoutError,
        ) as e:
            # 记录可重试的错误
            error_detail = (
                f"业务请求网络错误(可重试) - URL: {Env.BIZ_SERVER_URL}, "
                f"路径: {path}, "
                f"参数: {params}, "
                f"异常类型: {type(e).__name__}, "
                f"异常信息: {str(e)}"
            )
            log_text(DEVICE_BIZ_SERVER, error_detail)
            raise
        except aiohttp.ClientError as e:
            # 记录不可重试的错误
            error_detail = (
                f"业务请求客户端错误(不可重试) - URL: {Env.BIZ_SERVER_URL}, "
                f"路径: {path}, "
                f"参数: {params}"
            )
            err_msg = log_exception(DEVICE_BIZ_SERVER, e, error_detail)
            return wrap_error(net_utils.ERROR_BIZ_REQUEST, err_msg, path)

    def parse_tool_call(self, message):
        if message.tool_calls:
            tool = message.tool_calls[0]
            logging.info(f"参数>\t {tool.function.name} \t {tool.function.arguments}")

            # 处理工具调用
            if tool.function.name == "request":
                args = json.loads(tool.function.arguments)
                path = args.get("path")
                params = args.get("params")
                msg = args.get("message")
                return tool.id, path, params, msg
            # 添加对本地函数的支持
            elif tool.function.name == "query_department_schedule":
                args = json.loads(tool.function.arguments)
                department_id = args.get("department_id")
                return tool.id, "local_function", {"name": "query_department_schedule", "params": {"department_id": department_id}}, _("正在帮您处理请求，请稍等一下哦")
            else:
                logging.info("没找到工具")
        else:
            logging.info("没有触发工具调用")
        return None, None, None, None

    async def handle_local_function(self, function_info, organization_id):
        """
        处理本地函数调用
        
        Args:
            function_info (dict): 函数信息，包含name和params
            
        Returns:
            Response: 包含函数执行结果的Response对象
        """
        function_name = function_info.get("name")
        params = function_info.get("params", {})
        
        if function_name == "query_department_schedule":
            department_id = params.get("department_id")
            # 这里实现查询科室号源的本地函数
            schedule_data = await self.query_department_schedule(department_id, organization_id)
            return Response(code=0, data=schedule_data)  # 使用code=0表示成功
        
        #记得改
        return Response(code=2000, errorMsg=f"未知的本地函数: {function_name}")  # 使用错误码
    
    # 实现查询科室号源的方法
    async def query_department_schedule(self, department_id, organization_id):
        """
        查询科室号源
        
        Args:
            department_id: 部门ID
            
        Returns:
            str: 号源信息的字符串表示
        """
        # 从配置管理中获取号源数据
        all_departments_data = handle_get_appointment_slots(organization_id)
        
        # 获取当前日期和未来7天的日期信息
        today = datetime.now()
        weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
        date_info = f"今天是 {today.strftime('%Y年%m月%d日 %H:%M')} {weekdays[today.weekday()]}\n"
        date_info += "未来7天日期:\n"
        
        for i in range(7):
            future_date = today + timedelta(days=i)
            date_info += f"{future_date.strftime('%Y年%m月%d日')} {weekdays[future_date.weekday()]}\n"
        
        # 如果没有获取到数据，返回提示信息
        if not all_departments_data:
            return f"{date_info}\n无号源信息"
        
        # 将department_id转换为字符串，确保类型匹配
        str_department_id = str(department_id)
        
        # 根据department_id筛选科室
        department_data = None
        for dept in all_departments_data:
            # 将数据源中的departmentId也转换为字符串进行比较
            if str(dept.get("departmentId")) == str_department_id:
                department_data = dept
                break
        
        # 如果找不到指定科室，返回提示信息
        if not department_data:
            return f"{date_info}\n无此科室(ID:{department_id})号源"
        
        # 简洁格式化科室号源信息
        dept_name = department_data.get("departmentName", "未知")
        schedule_info = f"{date_info}\n{dept_name}可预约信息:"
        
        # 精简医生及号源信息（保留医生ID）
        for doctor in department_data.get("doctors", []):
            doctor_name = doctor.get("name", "未知")
            doctor_id = doctor.get("id", "未知")
            schedule_info += f"\n{doctor_name}(ID:{doctor_id}):"
            
            # 合并日期和时间段
            appointments = []
            for appt in doctor.get("appointments", []):
                date = appt.get("date", "")
                slots = ",".join(appt.get("timeSlots", []))
                appointments.append(f"{date}({slots})")
            
            schedule_info += " " + "; ".join(appointments)
        
        return schedule_info

    async def process_message(
        self, message, messages, **kwargs
    ) -> AsyncGenerator[Tuple[int, Any], None]:
        """
        处理 LLM 返回的消息，支持工具调用链和普通对话响应。

        该方法会循环处理 LLM 的响应，直到得到最终的对话内容：
        1. 检查消息中是否包含工具调用
        2. 如果有工具调用，执行工具并将结果返回给 LLM
        3. 如果没有工具调用，返回最终的对话内容

        Args:
            llm_service_config (LLMServiceConfig): LLM 配置
            message: 当前需要处理的消息
            messages (list): 完整的对话历史记录
            language: 语言
            **kwargs: 额外的参数，传递给 LLM 客户端

        Yields:
            Tuple[int, Any]:
                - (SOURCE_STATUS, str): 工具调用状态更新
                - (SOURCE_BIZ_ANSWER, str): 最终的业务回答

        Safety:
            - 限制最多 3 次工具调用，防止无限循环
            - 确保在异常情况下清理工具调用相关消息

        工具调用流程:
            1. 解析工具调用参数
            2. 执行工具调用
            3. 将工具响应添加到对话历史
            4. 继续让 LLM 处理工具响应

        Note:
            - 工具调用响应会被记录在日志中
            - 工具调用相关消息会在处理完成后被清理
            - 异常发生时会清理所有工具调用消息
        """
        tool_response = None
        tool_path = None
        organization_id = kwargs.get("organizationId")

        try:
            request_count = 0
            while True:
                messages.append(message)

                # 处理可能的工具调用
                call_id, path, params, msg = self.parse_tool_call(message)
                if path is None:
                    # 返回None，说明没有工具调用或处理完毕
                    if tool_response:
                        # 把工具调用的结果塞进message中
                        self.insertToolResp(message, tool_path, tool_response)
                    # 清理工具调用相关消息
                    self.removeToolMessages(messages)
                    yield SOURCE_BIZ_ANSWER, message.content
                    break  # 处理完毕，跳出循环
                else:
                    # 返回结果，继续处理
                    # 将function call的参数msg塞进message
                    if msg:
                        message = insertMsg2Content(message, msg)
                    yield SOURCE_STATUS, message.content  # 告知用户正在处理（工具调用中）
                    
                    # 处理本地函数调用
                    if path == "local_function":
                        tool_response = await self.handle_local_function(params, organization_id)
                    else:
                        # 原有的远程请求处理
                        tool_response = await self.request(path, params)
                    
                    # 把工具调用结果塞进role为tool的消息中
                    tool_result = {
                        "role": "tool",
                        "tool_call_id": call_id,
                        "content": str(tool_response.data),
                    }
                    logging.info(f"工具>\t {tool_result}")

                    tool_path = path

                    messages.append(tool_result)

                    request_count += 1

                    if (
                        request_count == 3
                    ):  # 当到第三次调用后，不再继续用工具，避免无限循环
                        functions = None
                    else:
                        functions = self.functions
                    message = await send_messages(
                        llm_service_config=self.llm_service_config,
                        messages=messages,
                        functions=functions,
                        response_format={"type": "json_object"},
                        **kwargs,
                    )

        except (Exception, asyncio.CancelledError):
            self.removeToolMessages(messages)
            raise

    def insertToolResp(self, message, path, tool_response):
        content = message.content.strip().replace("\n", "") if message.content else "{}"
        content = json.loads(content)
        content["tool_response"] = {
            "path": path,
            "response": tool_response.model_dump(),
        }
        message.content = json.dumps(content, ensure_ascii=False)
        return message

    def removeToolMessages(self, messages):
        # 从后向前遍历
        for i in range(len(messages) - 1, -1, -1):
            msg = messages[i]

            # 如果找到 user 消息，停止遍历
            if isinstance(msg, dict) and msg.get("role") == "user":
                break

            # 删除 tool 消息和非字典消息
            if not isinstance(msg, dict) or msg.get("role") == "tool":
                del messages[i]

    def update_prompt(self, prompt_str: str, biz_data: dict):
        self.prompt_template = prompt_str
        self.prompt = safe_format(prompt_str, biz_data)

    def update_functions(self, functions_json: list):
        self.functions = functions_json

    def update_biz_data(self, biz_data: dict):
        if self.prompt_template and isinstance(self.prompt_template, str):
            self.prompt = safe_format(self.prompt_template, biz_data)

    def update_llm_service_config(self, llm_service_config: dict):
        """更新模型设置"""
        # 只更新llm_config中非None的值
        for key, value in llm_service_config.items():
            if value is not None:
                self.llm_service_config[key] = value


    async def handle(self, messages, **kwargs) -> AsyncGenerator[Response, None]:
        language = kwargs.get("language", "zh-CN")
        sessionId = kwargs.get("sessionId")
        # 从kwargs中获取backgroundInfo并传递给format_chat_data
        backgroundInfo = kwargs.get("backgroundInfo", {})
        
        if Env.PRINT_LLM_CONFIG:
            if self.llm_service_config.get("model"):
                print(f"[BIZ] 业务服务模型: {self.llm_service_config['model']}")
            if self.llm_service_config.get("api_key"):
                print(f"[BIZ] 业务服务api_key: {self.llm_service_config['api_key']}")
            if self.llm_service_config.get("base_url"):
                print(f"[BIZ] 业务服务base_url: {self.llm_service_config['base_url']}")
        # 添加 language 参数
        data = wrap_chat_data(
            sessionId,
            SOURCE_STATUS,
            _("正在帮您处理请求，请稍等一下哦"),
        )
        yield wrap_response(data, RESPONSE_CODE_SEGMENT_END, language)

        if self.prompt:
            messages = net_utils.add_sys_prompt(messages, self.prompt)
        try:
            message = await send_messages(
                llm_service_config=self.llm_service_config,
                messages=messages,
                functions=self.functions,
                response_format={"type": "json_object"},
                temperature=Env.DEFAULT_LLM_TEMPERATURE,
                **kwargs,
            )
           
            async for source, msg in self.process_message(
                message, messages, **kwargs
            ):
                resp_code = (
                    RESPONSE_CODE_SEGMENT_END
                    if source == SOURCE_STATUS
                    else RESPONSE_CODE_END
                )
               
                data = await format_chat_data(sessionId, source, msg, language, self.promptId, backgroundInfo)
                if resp_code == RESPONSE_CODE_END and self.prompt:
                    net_utils.remove_sys_prompt(messages)
                yield wrap_response(data, resp_code, language)

        except (Exception, asyncio.CancelledError) as e:
            if self.prompt:
                net_utils.remove_sys_prompt(messages)
            if isinstance(e, ResponseException):
                yield e.response
            else:
                raise e

def get_message(msg):
    message = get_value(msg, "message")
    if not message:
        message = get_value(msg, "response")
    return message

# 格式化chat_data，要符合chat接口的response格式，message代表消息文本，extra代表额外信息
# 额外信息包含：
# 1. webview跳转
# 2. ui操作
async def format_chat_data(sessionId, source, response, language, promptId, backgroundInfo: dict = None):
        # 使用封装函数直接获取组合后的上下文
        ctx = await get_ctx_with_bg_info(sessionId, backgroundInfo)
        
        if response:
            try:
                response = json.loads(response)
            except json.JSONDecodeError:
                pass
        ctx[RESPONSE] = response
        ctx["Action"] = promptId
        template = """
        {% set message = response.message|default(response.response|default('')) %}
        {% if backgroundInfo.page|default('') %}
            {% set data = {'message': message, 'type': 'ui', 'extra': {'pageId': backgroundInfo.page, 'Action': Action|default(''), 'data': response}} %}
        {% elif response.url is defined %}
            {% set data = {'message': message, 'type': 'webview', 'extra': response} %}
        {% else %}
            {% set data = {'message': message, 'extra': response} %}
        {% endif %}
        {{ data|tojson }}
        """
        template_obj = templater(template, language=language)
        ret = template_obj.render(ctx)
        try:
            ret = json.loads(ret)
        except json.JSONDecodeError:
            ret = {
                "message": ret
            }

        ret["sessionId"] = sessionId
        ret["source"] = source
        return ret

# 将msg插入到message的content中
def insertMsg2Content(message, msg: str):
    if message:
        if message.content is None or message.content.strip() == "":
            # 如果 content 为 None 或空字符串，创建一个新的 JSON 对象
            content = {}
        else:
            try:
                content = json.loads(message.content)
            except json.JSONDecodeError:
                # 如果 content 不是有效的 JSON，创建一个新的 JSON 对象
                content = {}

        # 添加或更新 message 字段
        content["message"] = msg

        # 将更新后的内容转换回 JSON 字符串
        message.content = json.dumps(content, ensure_ascii=False)

    return message

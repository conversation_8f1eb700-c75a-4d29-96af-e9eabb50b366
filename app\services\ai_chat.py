from __future__ import annotations
import logging
from typing import AsyncGenerator, Optional, Union
import textwrap

from app.services.biz_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.services.knowledge_handler import <PERSON>Handler
from app.services.intention_detect import IntentDetector
from app.services.base_prompt import get_prompt_template
from app.utils.net_utils import *
from app.services import *
import app.services.mysql_service as db

from app.services.message_history import MessageHistory
from app.utils.logger import log_llm_request, log_llm_response, log_exception
from app.services.llm_client import stream_chat, LLMServiceConfig
from fastapi_babel import _
import json
from app.services.mysql_service import get_llm_config
import uuid

from app.config import Env
from app.services.mysql_service import get_base_prompt_setId, get_llm_config, get_embedding_config
from app.base.store_utils import session, organization, BACKGROUND_INFO, RESPONSE, ORG_CONFIG, get_ctx_with_bg_info
from app.base.jinja2_formatter import get_formatted_time, prompt_format
from app.services.question_generator import QuestionGeneratorWorkflow

# 处理器类定义
class ChatHandler(IntentionHandler):
    def __init__(
        self, organization_id: int = None, llm_service_config: LLMServiceConfig = None
    ):
        super().__init__()
        self.organization_id = organization_id
        self.llm_service_config = dict(llm_service_config) if llm_service_config else {}
        self.llm_service_config["model"] = self.llm_service_config.get("model") or Env.DEFAULT_CHAT_MODEL

    def update_llm_service_config(self, llm_service_config: dict):
        """更新模型设置"""
        # 只更新llm_config中非None的值
        for key, value in llm_service_config.items():
            if value is not None:
                self.llm_service_config[key] = value

    def replace_user_input(self, messages):
        content = messages[-1]["content"]

        try:
            json_content = json.loads(content)
            user_input = json_content.get("userInput")
            if user_input:
                messages[-1]["content"] = user_input
        except json.JSONDecodeError:
            pass

    async def handle(self, messages, **kwargs) -> AsyncGenerator[Response, None]:
        
        # 优先使用传入的语言参数，否则使用简体中文
        language = kwargs.get("language", "zh-CN") ##$$
        custom = kwargs.get("custom", None)
        # 添加响应要求
        requirements = textwrap.dedent(f"""
            Requirements:
            1. Always response in {language}. This is mandatory.
            2. Provide your response in natural spoken language only, as your output will be processed by a text-to-speech system.
            3. Do not include any formatting marks or special characters.
            """).strip()

        character_prompt = kwargs.get(EXTRA_CHARACTER_PROMPT, "")
        setId = get_base_prompt_setId(self.organization_id)
        combined_prompt = f"{get_prompt_template(setId, KEY_CHAT, language)}\n{requirements}\n{character_prompt}".strip()

        self.replace_user_input(messages)

        if Env.PRINT_LLM_CONFIG:
            if custom:
                if custom.get('model'):
                    print(f"[CHAT] 聊天服务模型: {custom.get('model')}")
            else:
                if self.llm_service_config.get("model"):
                    print(f"[CHAT] 聊天服务模型: {self.llm_service_config['model']}")
                if self.llm_service_config.get("api_key"):
                    print(f"[CHAT] 聊天服务api_key: {self.llm_service_config['api_key']}")
                if self.llm_service_config.get("base_url"):
                    print(f"[CHAT] 聊天服务base_url: {self.llm_service_config['base_url']}")

        if Env.PRINT_PROMPT:
            print(f"[Messages]\n{messages}\n")
            print(f"[CombinedPrompt]\n{combined_prompt}")

        # LLM的流式生成入口
        async for response in stream_chat(
            self.llm_service_config, messages, combined_prompt, **kwargs
        ):
            response.data["source"] = SOURCE_LLM_TEXT
            response.language = language
            yield response


class DefaultHandler(IntentionHandler):
    async def handle(self, messages, **kwargs) -> AsyncGenerator[Response, None]:
        language = kwargs.get("language", "zh-CN")
        device_id = kwargs.get("deviceId")
        session_id = kwargs.get("sessionId")
        log_llm_request(device_id, {"model": ""}, messages)
        msg = wrap_chat_data(session_id, SOURCE_REJECT, _("抱歉，我无法处理您的问题"))
        log_llm_response(device_id, msg)
        yield wrap_response(msg, language=language)


import copy


class AiChat:
    # 封装LLM生成函数的流程：self.create_handlers -> self.chat_handler -> handler.handle -> stream_chat -> client.chat.completions.create
    # 调用LLM的流程：/chat -> ai_chat.handle_chat -> ai_chat.chat -> handler.handle -> stream_chat -> client.chat.completions.create
    # chat流程： /chat -> ai_chat.chat -> message_history.add_message -> intentDetector.detect -> handler.handle -> ......
    def __init__(
        self,
        organization_id: int, 
        language: str = "zh-CN"
    ):
        self.organization_id = organization_id
        self.language = language
        self.config = load_config(organization_id) # 包含 意图配置intentionConfig 和 提示词配置promptConfig 和 业务数据biz_data
        self.llm_config = get_llm_config(organization_id)
        self.embedding_config = get_embedding_config(organization_id)
 
        # 处理器字典，如"chat": ChatHandler, "knowledge": KnowledgeHandler, "biz": BizHandler
        self.handlers: dict[str, IntentionHandler] = {} 

        self.character_prompts = {} # 人设提示词
        self.biz_data = self.config.get(KEY_BIZ_DATA, []) # 业务数据
        promptConfig = self.config[KEY_PROMPT] # 提示词配置

        try:
            intentions = self.config.get(KEY_INTENTION, {})
            formatters = self.config.get(KEY_UI_CONFIG, {}).get(KEY_FORMATTERS, {})
            self.intentDetector = IntentDetector(intentions, formatters, llm_service_config=self.llm_config.get(LLM_INTENTION_CONFIG))
        except Exception as e:
            raise ValueError(_("意图配置缺失: {}").format(str(e)))

        self.create_handlers(intentions, promptConfig)

        # 遍历promptConfig，直接使用完整的promptId作为key
        for key, value in promptConfig.items():
            if key.startswith("Character_"):
                self.update_character_prompt(key, value[KEY_TEMPLATE])
        
        self.message_history = MessageHistory()

    async def initialize(self):
        await organization(self.organization_id).set(self.config)
        return self
    
    # 保持为同步方法
    def create_handlers(self, handler_config: dict, promptConfig: dict):
        # 创建基础处理器，传入模型参数，后续再选择需要的处理器
        self.chat_handler = ChatHandler(
            organization_id=self.organization_id,
            llm_service_config=self.llm_config.get(LLM_CHAT_CONFIG),
        )
        
        self.knowledge_handler = KnowledgeHandler(
            self.organization_id,
            self.chat_handler,
            llm_service_config=self.llm_config.get(LLM_RAG_CONFIG),
            embedding_config=self.embedding_config,
        )
        
        self.default_handler = self.knowledge_handler

        # 根据配置创建不同类型的处理器
        for key in handler_config.keys():
            self._create_single_handler(key, promptConfig)

    def _create_single_handler(self, key, promptConfig):
        """创建单个处理器并添加到handlers字典中"""
        try:
            if key == KEY_INIENT_KNOWLEDGE:
                self.handlers[key] = self.knowledge_handler
            elif key == KEY_CHAT:
                self.handlers[key] = self.chat_handler
            else:
                self.handlers[key] = BizHandler(
                    key,
                    promptConfig[key][KEY_TEMPLATE],
                    promptConfig[key][KEY_TOOLS],
                    self.biz_data,
                    llm_service_config=self.llm_config.get(LLM_BIZ_CONFIG),
                )

        except Exception as e:
            error_msg = f"创建处理器 '{key}' 失败: {str(e)}"
            log_exception(f"org_{self.organization_id}", ValueError(error_msg), "AiChat")

    def update_character_prompt(self, key: str, template: str):
        if key and template:
            self.character_prompts[key] = template
        elif key and not template:
            self.character_prompts.pop(key, None)

    def update_biz_data(self, data):
        self.biz_data = data

    async def set_messages(self, messages: list = None, sessionId: str = None):
        """
        仅用于调试模式：设置完整的消息历史
        """
        if IS_DEBUG:
            return await self.message_history.set_history(messages)
        else:
            return None

    async def add_extra_messages(self, messages: list = None, sessionId: str = None):
        """
        向现有会话添加额外的消息
        
        Args:
            messages (list, optional): 要添加的消息列表
            sessionId (str, optional): 会话ID，如果为空则创建新会话
            
        Returns:
            str: 会话ID
        """
        if not messages:
            return sessionId
        
        # 将消息添加到历史记录中
        updated_session_id = sessionId
        for message in messages:
            updated_session_id, _ = await self.message_history.add_message(
                updated_session_id, message
            )
        
        return updated_session_id

    async def copy_messages(self, sessionId):
        """
        创建并返回 message_history 的深拷贝。

        返回:
        dict: message_history 的深拷贝，包含所有会话的历史记录。
        """
        history = await self.message_history.get_history(sessionId)
        ret = copy.deepcopy(history)
        return ret
    
    async def gen_direct_message(self, sessionId, response, language, backgroundInfo: dict = None):
        # 使用封装函数直接获取组合后的上下文
        ctx = await get_ctx_with_bg_info(sessionId, backgroundInfo)
        ctx[RESPONSE] = response
        template = "{{orgConfig.uiConfig.formatters[response.intention]|default('')|render(response)}}"
        ret = prompt_format(template, ctx, language)
        try:
            ret = json.loads(ret)

            ret["sessionId"] = sessionId
            ret["source"] = SOURCE_DIRECT_ANSWER
        except json.JSONDecodeError:
            pass

        return ret

    async def wrap_user_input(self, userInput: str, ctx: dict):
        try:
            template1 = """
{% set uiConfig = orgConfig.uiConfig %}
{% if uiConfig %}
    {% set current_page = uiConfig.pages[backgroundInfo.page] | default({}) %}
{% else %}
    {% set current_page = {} %}
{% endif %}
{# 页面状态信息 #}
{% if current_page %}
{{ "当前页面：{{ name }}({{ description }})" | render(current_page) }}
{{ current_page.business | default('') | render(backgroundInfo.business) }}
{% endif %}
{% if uiConfig %}
当前状态: {{ uiConfig.status[backgroundInfo.status] }}
{% endif %}
            """
            extra1 = prompt_format(template1, ctx, self.language)
            template_json = {
                'zh-CN': '当前时间是 {{ backgroundInfo.currentTime }}。请使用简体中文（普通话）回答，如果返回JSON格式，请保持key为英文',
                'en': 'Current time is {{ backgroundInfo.currentTime }}. Please answer in English. If returning JSON, keep the keys in English',
                'zh-HK': '當前時間是 {{ backgroundInfo.currentTime }}。請使用繁體中文粤语回答，如果返回JSON格式，請保持key為英文'
            }
            template_str = template_json.get(self.language, template_json.get("zh-CN"))
            extra2 = prompt_format(template_str, ctx, self.language)

            ret = {
                "backgroundInfo": extra1 + extra2,
                "userInput": userInput
            }
            return json.dumps(ret, ensure_ascii=False)
        except Exception as e:
            return userInput
    
    async def resolve_background_info(self, background_info, sessionId: str):
        """
        解析和获取背景信息
        
        Args:
            background_info: 从请求传入的背景信息
            session_id: 会话ID
            
        Returns:
            dict: 解析后的背景信息字典，如果无法解析则返回None
        """
        # 如果没有传入背景信息，尝试从session获取
        if not background_info and sessionId:
            background_info = await session(sessionId).get(BACKGROUND_INFO)
        
        # 如果背景信息存在但不是字典格式，尝试JSON解析
        if background_info and not isinstance(background_info, dict):
            try:
                background_info = json.loads(background_info)
            except (json.JSONDecodeError, TypeError):
                # 解析失败，保持原值或设为None
                pass
    
        return background_info or {}
    

    async def chat(
        self,
        userInput: str,
        backgroundInfo: str | dict | None,
        sessionId: str,
        characterId: str,
        deviceId: str,
        language: str = "zh-CN",
        llm_config: dict = None,
        custom: dict = None
    ) -> AsyncGenerator[Response, None]:
        try:
            # 更新当前实例的语言设置和模型配置
            self.update_aichat_llm_config(llm_config=llm_config, language=language)

            backgroundInfo = await self.resolve_background_info(backgroundInfo, sessionId)

            device_intention = get_value(backgroundInfo, "intention") or ""
            
            # 设置当前时间到背景信息
            currentTime = get_formatted_time(language=language)
            backgroundInfo["currentTime"] = currentTime

            ctx = await get_ctx_with_bg_info(sessionId, backgroundInfo)

            # 重写用户输入
            userInput = await self.wrap_user_input(userInput, ctx)

            sessionId, history = await self.message_history.add_message(
                sessionId, {"role": "user", "content": userInput}
            )

            messages = history.copy()

            # 意图检测
            detect_result = await self.intentDetector.detect(
                messages,
                deviceId=deviceId,
                sessionId=sessionId,
                intention=device_intention,
                language=language,
                backgroundInfo=backgroundInfo
            )

            intention = detect_result.get("intention")
            # 检查intention的类型
            if not isinstance(intention, str):
                intention = str(intention)

            rewrite = detect_result.get("rewrite")
            # 检查rewrite的类型
            if not isinstance(rewrite, str):
                rewrite = str(rewrite)

            direct_return = await self.gen_direct_message(sessionId, detect_result, language, backgroundInfo)

            print(f"[DEBUG-FLOW] 意图识别: {intention}")

            if direct_return:
                await self.message_history.add_message(
                    sessionId, gen_assistant_content(get_value(direct_return, "message"))
                )
                yield wrap_response(direct_return, RESPONSE_CODE_END, language=language)
                return

            intention_msg = wrap_chat_data(
                sessionId, SOURCE_STATUS, extra=detect_result
            )
            yield wrap_response(
                intention_msg, RESPONSE_CODE_SEGMENT_END, language=language
            )

            promptId = intention

            handler = self.handlers.get(promptId, self.default_handler)
            character_prompt = ""
            if characterId:
                # 直接构建人设键名
                character_id = f"{characterId}_{language}"
                character_prompt = self.character_prompts.get(character_id, "")

                # 如果仍然没找到，使用默认中文
                if not character_prompt and language != "zh-CN":
                    character_id = f"{characterId}_zh-CN"
                    character_prompt = self.character_prompts.get(character_id, "")


            print("[Creating] characterId: ", character_id if 'character_id' in locals() else "None")

            content = ""

            # 流式的拼接，同时LLM生成回答
            async for response in handler.handle(
                messages=messages,
                deviceId=deviceId,
                organizationId=self.organization_id,
                sessionId=sessionId,
                **{EXTRA_CHARACTER_PROMPT: character_prompt},
                rewrite=rewrite,
                language=language,
                backgroundInfo=backgroundInfo,
                custom=custom
            ):
                
                processed_response = process_stream_response(content, response)
                if isinstance(processed_response, Response):
                    content = ""
                    if processed_response.code == RESPONSE_CODE_END:
                        ct = get_value(processed_response.data, "message")
                        if not ct:
                            ct = get_value(processed_response.data, "extra")
                        # 对于RESPONSE_CODE_END的响应，将响应内容添加到message_history
                        await self.message_history.add_message(
                            sessionId, gen_assistant_content(ct)
                        )
                else:
                    content = processed_response

                yield response
        except Exception as e:
            err_msg = log_exception(deviceId, e, "AiChat")
            yield wrap_error(ERROR_AI_SERVER, err_msg, "/chat", language=language)


    def update_aichat_llm_config(self, llm_config: dict, language: str = None):
        # 更新当前实例的语言设置和模型配置

        if language:
            self.language = language

        if llm_config:
            self.llm_config = dict(llm_config)

            # 更新所有业务处理器的设置，意图检测器、chat处理器、知识库处理器等下面流程会覆盖
            for handler in self.handlers.values():
                handler.update_llm_service_config(llm_config.get(LLM_BIZ_CONFIG))

            # 更新默认处理器
            self.default_handler.update_llm_service_config(llm_config.get(LLM_RAG_CONFIG))

            # 更新意图检测器
            self.intentDetector.update_llm_service_config(llm_config.get(LLM_INTENTION_CONFIG))

            # 更新chat处理器
            self.chat_handler.update_llm_service_config(llm_config.get(LLM_CHAT_CONFIG))

            # 更新知识库处理器
            self.knowledge_handler.update_llm_service_config(llm_config.get(LLM_RAG_CONFIG))


    def update_aichat_embedding_config(self, embedding_config: dict):
        if embedding_config:
            self.embedding_config = dict(embedding_config)

        # 更新知识库处理器
        if hasattr(self.knowledge_handler, "update_embedding_config") and embedding_config:
            self.knowledge_handler.update_embedding_config(embedding_config)



@LRUCache(capacity=100)
async def get_ai_chat(organization_id: int) -> AiChat:
    # 创建AiChat实例(已包含同步初始化)
    ai_chat = AiChat(organization_id)
    
    # 如果有必要，可以调用异步初始化(目前为空操作)
    await ai_chat.initialize()
    
    return ai_chat



async def handle_chat(
    userInput: str,
    backgroundInfo: str | dict | None,
    sessionId: str,
    characterId: str,
    deviceId: str,
    organizationId: int,
    language: str = "zh-CN",
    custom: dict = None,
):
    try:
        # 根据organization_id，获取ai_chat实例，里面包含所有配置以及历史记录
        ai_chat = await get_ai_chat(organizationId)
        org_config = await organization(organizationId).get()
        
        # 先确保sessionId存在
        if not sessionId:
            sessionId = str(uuid.uuid4())
        await session(sessionId).set(ORG_CONFIG, org_config)
        llm_config = get_llm_config(organizationId)
        # 处理用户输入
        async for response in ai_chat.chat(
            userInput, backgroundInfo, sessionId, characterId, deviceId, language, llm_config, custom
        ):  
            # 检查是否需要从tool_response中提取数据并添加为用户消息
            if response.code == 0 and response.data:
                try:
                    # 获取tool_response中的data
                    extra = response.data.get("extra", {})
                    if isinstance(extra, dict):
                        tool_data = extra.get("data", {})
                        if isinstance(tool_data, dict):
                            tool_response = tool_data.get("tool_response", {})
                            if isinstance(tool_response, dict):
                                response_data = tool_response.get("response", {})
                                if isinstance(response_data, dict):
                                    data_content = response_data.get("data")
                                    
                                    if data_content:
                                        # 检查聊天历史中是否已有相同内容
                                        history = await ai_chat.message_history.get_history(sessionId)
                                        is_duplicate = False
                                        
                                        for msg in history:
                                            if (msg.get("role") == "user" and 
                                                msg.get("content") == data_content):
                                                is_duplicate = True
                                                break
                                                
                                        # 如果不是重复内容，添加到聊天历史
                                        if not is_duplicate:
                                            await ai_chat.message_history.add_message(
                                                sessionId, 
                                                {"role": "user", "content": data_content}
                                            )
                                            print(f"[Debug] Added tool response data to chat history: {data_content[:100]}...")
                                            
                except Exception as e:
                    print(f"[Error] Failed to process tool response data: {str(e)}")
            
            yield response
    except ValueError as e:
        yield wrap_error(ERROR_AI_SERVER, str(e), "/chat", language=language)
    except Exception as e:
        err_msg = log_exception(deviceId, e, "handle_chat")
        yield wrap_error(ERROR_AI_SERVER, err_msg, "/chat", language=language)


def _is_valid_value(value):
    """检查值是否有效（非空且非纯空格）"""
    if isinstance(value, str):
        return bool(value.strip())
    return bool(value)

async def handle_push_intention(organization_id: int, data):
    # 检查是否存在 AI 聊天实例，不创建新实例
    ai_chat: Optional[AiChat] = get_ai_chat.peek(organization_id)

    # 获取当前的意图配置
    try:
        current_config = db.get_intention_config(organization_id, "zh-CN") or {}
    except:
        current_config = {}

    # 处理传入的数据：更新或删除键
    for k, v in data.items():
        if _is_valid_value(k):  # 只处理有效的键
            if _is_valid_value(v):  # 值有效，更新配置
                current_config[k] = v
            else:  # 值无效（空字符串），删除键
                current_config.pop(k, None)

    # 过滤掉最终配置中键或值为空的字段
    filtered_data = {k: v for k, v in current_config.items() if _is_valid_value(k) and _is_valid_value(v)}
    
    if ai_chat:
        # 如果存在实例，更新它
        ai_chat.config[KEY_INTENTION] = filtered_data
    
    await organization(organization_id).set(KEY_INTENTION, filtered_data)
    # 无论是否存在实例，都执行数据库操作
    db.upsert_intention_config(organization_id, filtered_data)

async def handle_get_config(name: str, organization_id: int):
     # 检查是否存在 AI 聊天实例，不创建新实例
    ai_chat: Optional[AiChat] = get_ai_chat.peek(organization_id)
    if ai_chat:
        config = ai_chat.config
    else:
        config = load_config(organization_id)
    data = config.get(name, {})
    return data

async def handle_push_llm_config(organization_id: int, data):
    # 更新AiChat的模型配置hat的模型配置
    ## 检查是否存在 AI 聊天实例，不创建新实例
    ai_chat: Optional[AiChat] = get_ai_chat.peek(organization_id)

    if ai_chat:
        ## 如果存在实例，更新它
        ai_chat.update_aichat_llm_config(data)

    # 更新问题生成器工作流的模型配置
    QuestionGeneratorWorkflow.update_llm_service_config(str(organization_id), data.get(LLM_QUESTION_GENERATOR_CONFIG))

    # 无论是否存在实例，都执行数据库操作
    db.upsert_llm_config(organization_id, data)


async def handle_push_embedding_config(organization_id: int, data):
    # 检查是否存在 AI 聊天实例，不创建新实例
    ai_chat: Optional[AiChat] = get_ai_chat.peek(organization_id)

    if ai_chat:
        # 如果存在实例，更新它
        ai_chat.update_aichat_embedding_config(data)

    # 无论是否存在实例，都执行数据库操作
    db.upsert_embedding_config(organization_id, data)


async def handle_push_ui_config(organizationId: int, data: dict):
    await organization(organizationId).set(KEY_UI_CONFIG, data)
    db.batch_upsert_ui_configs(organizationId, data)


async def handle_push_prompt(organization_id: int, data: list):
    # 1.先用peek方法看实例是否存在，存在则更新实例，不存在则不需要
    ai_chat: Optional[AiChat] = get_ai_chat.peek(organization_id)

    if ai_chat:
        # 2.遍历data，根据promptId，匹配对应的处理类 更新prompt
        for item in data:
            promptId = item.get(KEY_PROMPT_ID)
            if promptId:
                if promptId.startswith("Character_"):
                    ai_chat.update_character_prompt(promptId, item.get(KEY_TEMPLATE))
                else:
                    # v1.0版，business用的是allBusiness的提示词，所以需要fix。当所有组织的提示词和意图配置都更新后，此方法可以删除
                    if promptId == KEY_ALL_BUSINESS:
                        promptId = KEY_INIENT_BIZ

                    handler = ai_chat.handlers.get(promptId)
                    if handler:  # 存在则更新
                        if isinstance(handler, BizHandler):
                            if _is_valid_value(item.get(KEY_TEMPLATE)):
                                handler.update_prompt(
                                    item.get(KEY_TEMPLATE), ai_chat.biz_data
                                )
                                handler.update_functions(item.get(KEY_TOOLS))
                            else:
                                ai_chat.handlers.pop(promptId)
                        else:
                            # 其他类型处理器比如ChatHandler、KnowledgeHandler，提示词由基础提示词控制，不受此接口控制
                            pass
                    else:
                        if promptId == KEY_INTENTION_DETECT:
                            # 意图检测处理器也由基础提示词接管，不受此接口控制
                            pass
                        else:  # 不存在则新建
                            if _is_valid_value(item.get(KEY_TEMPLATE)):
                                handler = BizHandler(
                                    promptId,
                                    item.get(KEY_TEMPLATE),
                                    item.get(KEY_TOOLS),
                                    ai_chat.biz_data,
                                )
                                ai_chat.handlers[promptId] = handler

    # 3.无论是否存在实例，都执行数据库操作
    db.batch_upsert_prompt_config(organization_id, data)


async def handle_push_biz_data(organization_id: int, data):
    ai_chat: Optional[AiChat] = get_ai_chat.peek(organization_id)
    if ai_chat:
        if isinstance(data, list):
            # 修改这里，同时检查key、value和data字段
            dict_data = {item.get("key"): item.get("value", item.get("data", "")) for item in data}
        elif isinstance(data, dict):
            dict_data = data
        ai_chat.update_biz_data(dict_data)
        # 遍历所有 handlers 并更新业务数据
        for handler in ai_chat.handlers.values():  # 注意：这里应该是values()
            if isinstance(handler, BizHandler):
                handler.update_biz_data(dict_data)
    
    # 添加日志，帮助调试
    print(f"Updating business data for org {organization_id}: {data}")
    try:
        db.batch_upsert_business_data(organization_id, data)
        print(f"Business data update completed for org {organization_id}")
    except Exception as e:
        print(f"Error updating business data: {str(e)}")
        raise

def load_config(organization_id: int, language: str = "zh-CN"):
    try:
        intention_config = db.get_intention_config(organization_id, language)
    except:
        try:
            intention_config = db.get_intention_config(organization_id, "zh-CN")
        except:
            intention_config = {}

    # 如果intention_config为空，设置默认值
    if not intention_config:
        intention_config = {"knowledge": ""}

    # 过滤掉key为空的条目
    intention_config = {k: v for k, v in intention_config.items() if _is_valid_value(k)}

    # 获取指定语言的prompt配置，如果不存在则使用默认语言
    prompt_configs = db.get_all_prompt_config(organization_id, language)
    if not prompt_configs:
        prompt_configs = db.get_all_prompt_config(organization_id, "zh-CN")

    prompt_config = {
        config.promptId: {"template": config.template, "tools": config.tools}
        for config in prompt_configs
    }

    # 获取指定语言的业务数据，如果不存在则使用默认语言
    biz_items = db.get_all_biz_data(organization_id, language)
    if not biz_items:
        biz_items = db.get_all_biz_data(organization_id, "zh-CN")

    biz_data = {item.key: item.data for item in biz_items}

    ui_config = db.get_all_ui_configs(organization_id)
    ui_config_data = {config.key: config.data for config in ui_config}
    
    return {
        KEY_INTENTION: intention_config,
        KEY_PROMPT: prompt_config,
        KEY_BIZ_DATA: biz_data,
        KEY_UI_CONFIG: ui_config_data,
    }


def check_org_config(organization_id: int):
    config = load_config(organization_id)
    try:
        check_results = []

        intention_config = config.get(KEY_INTENTION, {})

        promptConfig = config.get(KEY_PROMPT, {})

        for key in intention_config.keys():
            # 跳 knowledge 和 chat 的检查，因为它们使用系统默认提示词
            if key in [KEY_INIENT_KNOWLEDGE, KEY_INIENT_CHAT]:
                continue

            if not promptConfig.get(key, {}).get("template"):
                check_results.append(_("未配置{}提示词").format(key))

        return check_results
    except Exception:
        check_results.append(_("配置检查失败"))
        return check_results


logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

import os
from dotenv import load_dotenv
from typing import Set

# 环境配置README：
# 敏感配置：.env.secret  设置为gitignore，不提交到git，留有.env.secret.example作为示例文件
# 不同环境配置：.env.debug .env.release，受IS_DEBUG影响
# 基本配置：.env.base，与环境无关，也无敏感信息


# 创建一个函数来从环境变量中读取值，如果不存在则返回默认值
def get_env(key, default=None):
    return os.environ.get(key, default)


# 加载敏感配置
env_secret_path = os.path.join(os.path.dirname(__file__), ".env.secret")
load_dotenv(env_secret_path)

# 获取是否为调试模式（标志位在敏感配置里）
IS_DEBUG: bool = get_env("IS_DEBUG", "false").lower() == "true"


# 根据调试模式确定配置文件路径
env_path = ".env.debug" if IS_DEBUG else ".env.release"
env_path = os.path.join(os.path.dirname(__file__), env_path)
env_base_path = os.path.join(os.path.dirname(__file__), ".env.base")


_SENSITIVE_KEYS: Set[str] = set()


def load_env():
    with open(env_secret_path, "r", encoding="utf-8") as file:
        for line in file:
            line = line.strip()
            if line and not line.startswith("#"):
                key = line.split("=", 1)[0]
                _SENSITIVE_KEYS.add(key)
    load_dotenv(env_base_path, override=True)
    load_dotenv(env_path, override=True)
    load_dotenv(env_secret_path, override=True)


# 动态（热更新）配置
class Env:

    @classmethod
    def reload(cls):
        load_env()
        # 业务服务器配置
        cls.BIZ_SERVER_URL = get_env(
            "BIZ_SERVER_URL", "http://************/dev-api/business/data/ai"
        )
        # OpenAI 配置
        cls.OPENAI_API_KEY = get_env("OPENAI_API_KEY", "")
        cls.OPENAI_BASE_URL = get_env("OPENAI_BASE_URL", "https://api.openai.com/v1")
        
        # Azure OpenAI 配置
        cls.AZURE_API_KEY = get_env("AZURE_API_KEY", "")  # 从 .env.secret 加载
        cls.AZURE_BASE_URL = get_env("AZURE_BASE_URL", "")  # 从 .env.debug 加载
        cls.AZURE_API_VERSION = get_env("AZURE_API_VERSION", "")  # 从 .env.debug 加载
        
        # 智谱AI 配置
        cls.ZHIPU_API_KEY = get_env("ZHIPU_API_KEY", "")
        cls.ZHIPU_BASE_URL = get_env(
            "ZHIPU_BASE_URL", "https://open.bigmodel.cn/api/paas/v4/"
        )
        # DeepSeek 配置
        cls.DEEPSEEK_API_KEY = get_env("DEEPSEEK_API_KEY", "")
        cls.DEEPSEEK_BASE_URL = get_env("DEEPSEEK_BASE_URL", "https://api.deepseek.com/v1")
        # 火山引擎配置
        cls.VOLCANO_API_KEY = get_env("VOLCANO_API_KEY", "")
        cls.VOLCANO_BASE_URL = get_env("VOLCANO_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")
        # 阿里Qwen配置
        cls.QWEN_API_KEY = get_env("QWEN_API_KEY", "")
        cls.QWEN_BASE_URL = get_env("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
        # 搜索参数
        cls.QUESTION_TOP_K = int(get_env("QUESTION_TOP_K", 5))
        cls.CHUNK_TOP_K = int(get_env("CHUNK_TOP_K", 5))
        # 向量搜索阈值配置
        cls.QUESTION_SIMILARITY_THRESHOLD = float(
            get_env("QUESTION_SIMILARITY_THRESHOLD", 0.67)
        )
        cls.ANSWER_SIMILARITY_THRESHOLD = float(
            get_env("ANSWER_SIMILARITY_THRESHOLD", 0.4)
        )
        cls.CHUNK_SIMILARITY_THRESHOLD = float(
            get_env("CHUNK_SIMILARITY_THRESHOLD", 0.5)
        )
        cls.DIRECT_RETURN_THRESHOLD = float(get_env("DIRECT_RETURN_THRESHOLD", 0.98))
        # Reranker阈值配置
        cls.RERANKER_CHUNK_THRESHOLD = float(get_env("RERANKER_CHUNK_THRESHOLD", -3))
        # RAG使用的最大chunk数量
        cls.RAG_CHUNK_LIMIT = int(get_env("RAG_CHUNK_LIMIT", 3))
        # 模型配置
        cls.DEFAULT_BIZ_MODEL = get_env("DEFAULT_BIZ_MODEL", "openai.gpt-4o-mini")
        cls.DEFAULT_RAG_MODEL = get_env("DEFAULT_RAG_MODEL", "openai.gpt-4o-mini")
        cls.DEFAULT_CHAT_MODEL = get_env("DEFAULT_CHAT_MODEL", "openai.gpt-4o-mini")
        cls.DEFAULT_TRANSLATION_MODEL = get_env(
            "DEFAULT_TRANSLATION_MODEL", "openai.gpt-4o-mini"
        )
        cls.DEFAULT_QUESTION_GENERATOR_MODEL = get_env("DEFAULT_QUESTION_GENERATOR_MODEL", "openai.gpt-4o-mini")
        cls.DEFAULT_INTENTION_MODEL = get_env("DEFAULT_INTENTION_MODEL", "openai.gpt-4o-mini")
        cls.DEFAULT_LLM_TEMPERATURE = float(get_env("DEFAULT_LLM_TEMPERATURE", 0.3))
        cls.DEFAULT_EMBEDDING_MODEL = get_env("DEFAULT_EMBEDDING_MODEL", "openai.text-embedding-3-small")
        cls.MAX_ATTEMPTS_PER_MODEL = int(get_env("MAX_ATTEMPTS_PER_MODEL", 2))

        # Print settings
        cls.PRINT_EMBEDDING_TIME = (
            get_env("PRINT_EMBEDDING_TIME", "false").lower() == "true"
        )
        cls.PRINT_PROMPT = (
            get_env("PRINT_PROMPT", "false").lower() == "true"
        )
        cls.PRINT_LLM_CONFIG = (
            get_env("PRINT_LLM_CONFIG", "false").lower() == "true"
        )
        cls.PRINT_SEARCH_TIME = get_env("PRINT_SEARCH_TIME", "false").lower() == "true"
        cls.PRINT_GENERATION_TIME = (
            get_env("PRINT_GENERATION_TIME", "false").lower() == "true"
        )
        cls.PRINT_TOTAL_TIME = get_env("PRINT_TOTAL_TIME", "false").lower() == "true"
        cls.PRINT_INTENTION_TIME = (
            get_env("PRINT_INTENTION_TIME", "false").lower() == "true"
        )
        cls.PRINT_SESSION_ID = get_env("PRINT_SESSION_ID", "false").lower() == "true"
        cls.PRINT_INTENTION_RESULT = (
            get_env("PRINT_INTENTION_RESULT", "false").lower() == "true"
        )
        cls.PRINT_FETCH_CONFIG = (
            get_env("PRINT_FETCH_CONFIG", "false").lower() == "true"
        )
        cls.PRINT_LANGUAGE_DETECT = (
            get_env("PRINT_LANGUAGE_DETECT", "false").lower() == "true"
        )

        # QA对处理配置
        cls.QUESTION_EXPAND_COUNT = int(get_env("QUESTION_EXPAND_COUNT", 5))
        # embedding token限制
        cls.EMBEDDING_TOKEN_LIMIT = int(get_env("EMBEDDING_TOKEN_LIMIT", 8000))

    @classmethod
    def get_detail(cls):
        return {
            key: value
            for key, value in cls.__dict__.items()
            if not key.startswith("__")
            and not callable(value)
            and not isinstance(value, classmethod)
            and key not in _SENSITIVE_KEYS
        }


# 加载环境，并加载动态（热更新）配置
Env.reload()


# 静态(冷更新)配置
# 服务器标识
SERVER_NAME = get_env("SERVER_NAME", "default-server")

# MySQL 配置
MYSQL_HOST = get_env("MYSQL_HOST", "localhost")
MYSQL_PORT = int(get_env("MYSQL_PORT", 3306))
MYSQL_USER = get_env("MYSQL_USER", "root")
MYSQL_PASSWORD = get_env("MYSQL_PASSWORD", "")
MYSQL_DATABASE = get_env("MYSQL_DATABASE", "ai_chat")

# Milvus 配置
MILVUS_HOST = get_env("MILVUS_HOST", "localhost")
MILVUS_PORT = int(get_env("MILVUS_PORT", "19530"))


# 以下为硬编码配置，后续再考虑做成文件配置
MILVUS_PARAMS = {
    "index_type": "FLAT",
    "metric_type": "IP",
    "index_params": {},  # FLAT 索引不需要额外参数
    "search_params": {},  # FLAT 索引不需要搜索参数
}
# 如果将来切换到 IVF_FLAT，可以这样设置：
# MILVUS_PARAMS = {
#     "index_type": "IVF_FLAT",
#     "metric_type": "IP",
#     "index_params": {"nlist": 16},
#     "search_params": {"nprobe": 16}
# }

# 全局语言映射字典
LANGUAGE_MAP = {
    "zh-CN": "简体中文（中国大陆）",
    "zh-HK": "繁体中文（香港）",
    "en": "英语",
    "ar": "阿拉伯语",
    "ms": "马来语",
}

# 向量维度
VECTOR_DIMENSION = 1024

# 字段最大长度
MAX_LANGUAGE_LENGTH = 50
MAX_QUESTION_LENGTH = 65535
MAX_ANSWER_LENGTH = 65535
MAX_CHUNK_CONTENT_LENGTH = 65535
MAX_EMBEDDING_TOKENS = 32768

# 分块配置
CHUNK_SIZE = 1000 # 标准分块大小
CHUNK_SIZE_PARAGRAPH = 4000 # 段落级别分块大小
CHUNK_OVERLAP = 100 # 分块重叠大小

import asyncio
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import StreamingResponse, JSONResponse
from starlette.middleware.base import _StreamingResponse
from typing import AsyncIterable
from app.utils.net_utils import wrap_error
from fastapi_babel import _
from app.services.config_service import config_service

# 保留原有默认超时设置作为兜底方案
DEFAULT_TIMEOUT = 10.0


class TimeoutMiddleware(BaseHTTPMiddleware):
    """
    请求超时处理中间件

    为不同路由提供可配置的超时控制，支持普通请求和流式响应。
    根据组织ID获取超时配置，支持回退到默认配置。
    """

    # 默认流式超时（兜底方案）
    DEFAULT_STREAM_TIMEOUT = 10.0

    async def dispatch(self, request: Request, call_next):
        """
        处理请求的主方法

        Args:
            request: FastAPI 请求对象
            call_next: 下一个处理器

        Returns:
            正常响应或错误响应（超时/异常）
        """
        path = request.url.path

        # 只从请求体中获取organizationId
        organization_id = 0
        try:
            # 只处理可能包含请求体的方法
            if request.method in ["POST", "PUT", "PATCH"]:
                body = await request.json()
                org_id_body = body.get("organizationId")
                if org_id_body is not None and (
                    isinstance(org_id_body, int)
                    or (isinstance(org_id_body, str) and org_id_body.isdigit())
                ):
                    organization_id = int(org_id_body)
        except Exception:
            pass

        try:
            # 移除总体超时限制，直接调用下一个处理器
            response = await call_next(request)

            # 特殊处理流式响应
            if isinstance(response, (StreamingResponse, _StreamingResponse)):
                if not response.media_type:
                    response.media_type = "text/event-stream"
                # 传递路径和组织ID以获取正确的超时配置
                response.body_iterator = self.timeout_generator(
                    response.body_iterator, path, organization_id
                )
                return response

            return response
        except asyncio.TimeoutError:
            # 使用get_specific_config获取特定路径的超时配置
            timeout_value = config_service.get_specific_config(
                organization_id, f"timeout_config.{path}", DEFAULT_TIMEOUT
            )

            # 打印当前超时配置信息
            print(
                f"[Info] 组织ID: {organization_id}, 路径: {path}, 使用的超时值: {timeout_value}"
            )

            return JSONResponse(
                status_code=408,
                content={
                    "detail": _("接口 {} 总处理时间超出限制，已超过 {} 秒").format(
                        path, timeout_value
                    )
                },
            )
        except HTTPException as he:
            return JSONResponse(
                status_code=he.status_code,
                content={"detail": _("接口 {} 处理异常: {}").format(path, he.detail)},
            )
        except Exception as e:
            return JSONResponse(
                status_code=500,
                content={
                    "detail": _("接口 {} 服务器内部错误: {}").format(path, str(e))
                },
            )

    async def timeout_generator(
        self, iterator: AsyncIterable, path: str, organization_id: int = 0
    ):
        """
        为流式响应提供超时控制的生成器

        Args:
            iterator: 原始响应迭代器
            path: 请求路径
            organization_id: 组织ID，用于获取对应的超时配置
        """
        # 使用get_specific_config获取特定路径的流式超时配置
        stream_timeout = config_service.get_specific_config(
            organization_id,
            f"stream_timeout_config.{path}",
            self.DEFAULT_STREAM_TIMEOUT,
        )

        # 打印当前流式超时配置信息
        print(
            f"[Info] 组织ID: {organization_id}, 路径: {path}, 使用的流式超时值: {stream_timeout}"
        )

        while True:
            try:
                item = await asyncio.wait_for(iterator.__anext__(), stream_timeout)
                yield item
            except StopAsyncIteration:
                break
            except asyncio.TimeoutError:
                response = wrap_error(
                    408,
                    _("接口 {} 流式数据块响应超时，单次响应已超过 {} 秒").format(
                        path, stream_timeout
                    ),
                )
                yield response.toStream()
                break


from fastapi_babel.middleware import BabelMiddleware
from fastapi_babel import Babel


class CustomBabelMiddleware(BabelMiddleware):
    def get_language(self, babel: Babel, lang_code):
        if lang_code:
            lang_code = lang_code.replace("_", "-")
        return super().get_language(babel, lang_code)

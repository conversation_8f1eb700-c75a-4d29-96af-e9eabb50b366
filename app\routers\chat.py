from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from app.services import ai_chat, hr_tech_china
from app.utils.logger import log_exception, log_chat_request, log_chat_response, log_text
from app.utils.net_utils import *
from fastapi_babel import _
from fastapi import Request, Depends
from app.utils.language_detect import LanguageDetector
import uuid
from app.base.store_utils import session, BACKGROUND_INFO
from typing import Any

router = APIRouter()

class ValidationError(Exception):
    """自定义验证错误异常类"""
    def __init__(self, error_code: int, message: str):
        self.error_code = error_code
        self.message = message
        super().__init__(message)

class ChatRequest(BaseModel):
    userInput: str
    backgroundInfo: str | dict | None = None
    sessionId: str | None = None
    characterId: str | None = None
    deviceId: str
    organizationId: int = Field(default=0, ge=0)
    custom: dict | None = None
    
    def __str__(self) -> str:
        return f"""
User Input: {self.userInput}
Background Info: {self.backgroundInfo}
Session ID: {self.sessionId}
Character ID: {self.characterId}
Device ID: {self.deviceId}
Organization ID: {self.organizationId}
Custom Config: {self.custom}"""

class AccessRequest(BaseModel):
    deviceId: str
    organizationId: int = Field(default=0, ge=0)

class ChatHistoryUpdateRequest(BaseModel):
    extraMessage: Any
    sessionId: str | None = None
    deviceId: str
    organizationId: int = Field(default=0, ge=0)

    def __str__(self) -> str:
        return f"""
Extra Message: {self.extraMessage}
Session ID: {self.sessionId}
Device ID: {self.deviceId}
Organization ID: {self.organizationId}"""

class BackgroundInfoUpdateRequest(BaseModel):
    backgroundInfo: dict
    sessionId: str | None = None
    deviceId: str 
    organizationId: int = Field(default=0, ge=0)

    def __str__(self) -> str:
        return f"""
Background Info: {self.backgroundInfo}
Session ID: {self.sessionId}
Device ID: {self.deviceId}
Organization ID: {self.organizationId}"""

@router.post("/access")
async def access(request: AccessRequest):
    try:
        result = ai_chat.check_org_config(request.organizationId)

        if result:
            # 组织配置有问题
            return wrap_error(ERROR_ORG_CONFIG, "\n".join(result))

        await ai_chat.get_ai_chat(request.organizationId)
    except Exception as e:
        err_msg = log_exception(request.deviceId, e, "access")
        return wrap_error(ERROR_ORG_CONFIG, err_msg)

    # 组织配置无误，访问成功
    return wrap_response("success")

def get_request_language(request: Request) -> str: # 从request头获取请求的语言, Accept-Language里面
    language = request.state.babel.locale
    if language:
        language = language.replace("_", "-")
    return language or "zh-CN"

def resolve_language(text: str, language: str) -> str:
    # 检测用户输入的语言
    detector = LanguageDetector()
    detected_language = detector.detect_language(
        text, default_lang=language
    )
    
    # 语言转换逻辑
    if language == "zh-CN":
        # 如果默认语言是简体中文，结果只能是简体中文或英文
        if detected_language.startswith("zh") and detected_language != "zh-CN":
            detected_language = "zh-CN"
    elif language == "zh-HK":
        # 如果默认语言是繁体中文，结果只能是繁体中文或英文
        if detected_language.startswith("zh") and detected_language != "zh-HK":
            detected_language = "zh-HK"
    # 如果默认语言是英文，保持检测结果不变

    print(f"检测到的语言: {detected_language}, 默认语言: {language}")
    return detected_language



@router.post("/chat")
async def chat(request: ChatRequest, req: Request, language: str = Depends(get_request_language)):
    try:
        detected_language = resolve_language(request.userInput, language)
        
        # 设置请求的语言，存储下来
        set_request_language(req, detected_language)
        
        # 记录chat请求 - 路由层统一记录
        log_chat_request(request.deviceId, request, detected_language)
        
        async def response_generator():
            # 对于业务情况是hr_tech_china
            if request.custom and request.custom.get("type") == "hr_tech_china":
                async for response in hr_tech_china.handle_chat(
                    user_input=request.userInput,
                    session_id=request.sessionId,
                    custom=request.custom
                ):
                    yield response.to_sse()
            else:
                # 正常的业务回答
                async for response in ai_chat.handle_chat(
                    userInput=request.userInput,
                    backgroundInfo=request.backgroundInfo,
                    sessionId=request.sessionId,
                    characterId=request.characterId,
                    deviceId=request.deviceId,
                    organizationId=request.organizationId,
                    language=detected_language,
                    custom=request.custom
                ):
                    # 记录非连续响应的日志
                    if response.code != RESPONSE_CODE_CONTINUE:
                        log_chat_response(request.deviceId, response)
                    yield response.to_sse()

        return StreamingResponse(
            response_generator(),
            media_type="text/event-stream",
        )
    except Exception as e:
        err_msg = log_exception(request.deviceId, e, "chat_endpoint")
        raise HTTPException(
            status_code=500, detail=_("服务器处理请求时遇到错误: {}").format(err_msg)
        )
    
def set_request_language(request: Request, language: str) -> None:
    """设置请求的语言"""
    if hasattr(request.state, "babel"):
        locale = language.replace("-", "_")
        request.state.babel.locale = locale
        
        from fastapi_babel.core import _context_var
        _context_var.set(request.state.babel.gettext)

@router.post("/chat/updateChatHistory")
async def update_chat_history(request: ChatHistoryUpdateRequest, language: str = Depends(get_request_language)):
    try:
        log_text(device_id=request.deviceId, text=f"updateChatHistory request: {str(request)}")
        # 验证extraMessage字段
        if request.extraMessage is not None:
            # 1002: messages字段非法，必须为JSON Array
            if not isinstance(request.extraMessage, list):
                raise ValidationError(ERROR_MESSAGE_INVALID_ARRAY, _("messages字段非法，必须为JSON Array"))
            
            # 验证每个消息对象
            for msg in request.extraMessage:
                # 1003: role字段非法，必须为user 或 assistant
                if "role" not in msg or msg["role"] not in ["user", "assistant"]:
                    raise ValidationError(ERROR_MESSAGE_INVALID_ROLE, _("role字段非法，必须为 user 或 assistant"))
                
                # 1004: content字段非法，比如为字符串
                if "content" not in msg or not isinstance(msg["content"], str):
                    raise ValidationError(ERROR_MESSAGE_INVALID_CONTENT, _("content字段非法，必须为字符串"))
        
        ai_chat_instance = await ai_chat.get_ai_chat(request.organizationId)
        sessionId = await ai_chat_instance.add_extra_messages(
            messages=request.extraMessage,
            sessionId=request.sessionId
        )
        
        response = wrap_response({"sessionId": sessionId}, language=language)
        log_text(device_id=request.deviceId, text=f"updateChatHistory response: {str(response)}")
        return response
    except Exception as e:
        if isinstance(e, ValidationError):
            # 使用错误码来分辨不同的验证错误情况
            return wrap_error(e.error_code, e.message, language=language)
        else:
            err_msg = log_exception(request.deviceId, e, "update_chat_history")
            raise HTTPException(
                status_code=500, detail=_("服务器处理请求时遇到错误: {}").format(err_msg)
            )
        

@router.post("/chat/updateBackgroundInfo")
async def update_background_info(request: BackgroundInfoUpdateRequest, language: str = Depends(get_request_language)):
    try:
        log_text(device_id=request.deviceId, text=f"updateBackgroundInfo request: {str(request)}")
        
        # 如果没有提供sessionId，生成一个新的
        sessionId = request.sessionId
        if not sessionId:
            sessionId = str(uuid.uuid4())
            print(f"未提供sessionId，已生成新的: {sessionId}")
        else:
            print(f"使用现有sessionId: {sessionId}")
        
        # 存储背景信息
        info_preview = str(request.backgroundInfo)[:100] + "..." if len(str(request.backgroundInfo)) > 100 else str(request.backgroundInfo)
        print(f"背景信息内容预览: {info_preview}")
        
        # 保存背景信息到会话存储中
        await session(sessionId).set(BACKGROUND_INFO, request.backgroundInfo)
        
        print(f"已成功存储背景信息，sessionId: {sessionId}")

        response = wrap_response({"sessionId": sessionId}, language=language)
        log_text(device_id=request.deviceId, text=f"updateBackgroundInfo response: {str(response)}")
        return response
    except Exception as e:
        err_msg = log_exception(request.deviceId, e, "update_background_info")
        print(f"背景信息更新失败: {err_msg}")
        raise HTTPException(
            status_code=500, detail=_("服务器处理请求时遇到错误: {}").format(err_msg)
        )
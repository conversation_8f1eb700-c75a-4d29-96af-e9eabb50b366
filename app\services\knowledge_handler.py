# 标准库导入
import logging
import json
import time
from typing import List, Any
import textwrap

# 第三方库导入
from fastapi_babel import _

# 本地应用导入
from app.services import (
    IntentionHandler,
    EXTRA_CHARACTER_PROMPT,
    SOURCE_QA_VECTOR,
    SOURCE_QA_RAG,
    SOURCE_QA_FALLBACK,
    SOURCE_STATUS,
    SOURCE_QA_VECTOR_REWRITE,
    SOURCE_QA_VECTOR_LLM,
    SOURCE_REJECT,
    KEY_QA_PAIR_ID,
    get_source_name,
    KEY_KNOWLEDGE_RAG,
    KEY_KNOWLEDGE_QA,
)
from app.services.milvus_service import (
    search_question_collection,
    search_chunk_collection,
)
from app.services.llm_client import LLMServiceConfig, send_messages, stream_chat, get_embedding
from app.services import llm_client
from app.services.base_prompt import get_prompt_template
from app.services.mysql_service import get_base_prompt_setId
from app.config import Env
from app.utils.net_utils import (
    wrap_response,
    wrap_error,
    wrap_chat_data,
    RESPONSE_CODE_END,
    ERROR_AI_SERVER,
    RESPONSE_CODE_SEGMENT_END,
)
from app.utils.logger import log_knowledge_handler, log_exception

logging.basicConfig(
    level=logging.WARNING,
    format="%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

class KnowledgeHandler(IntentionHandler):
    LANGUAGE_MAPPING = {
        "zh-CN": "Simplified Chinese",
        "zh-HK": "Traditional Chinese",
        "en": "English",
        "ar": "Arabic",
        "ms": "Malay",
    }

    def __init__(
        self,
        organization_id: int,
        fallback_handler: IntentionHandler = None, # 知识库失效时，调用的后备处理器
        llm_service_config: LLMServiceConfig = None,
        embedding_config: dict = None,
    ):
        self.organization_id = organization_id
        self.fallback_handler = fallback_handler 
        self.llm_service_config = dict(llm_service_config) if llm_service_config else {}
        self.llm_service_config["model"] = self.llm_service_config.get("model") or Env.DEFAULT_RAG_MODEL
        self.embedding_config = dict(embedding_config) if embedding_config else {}
        self.embedding_config["model"] = self.embedding_config.get("model") or Env.DEFAULT_EMBEDDING_MODEL

    def get_query(self, messages):
        content = messages[-1]["content"] #获取最后一个消息，即用户输入

        try:
            json_content = json.loads(content)
            user_input = json_content.get("userInput")
            if user_input:
                return user_input
        except json.JSONDecodeError:
            pass

        return content

    def combine_qa(self, qa: dict[str, Any]):
        return f"\n问题：{qa.get('question', '')}\n答案：{qa.get('answer', '')}"

    def filter_json(self, json: dict, keys: List[str]):
        return {k: v for k, v in json.items() if k in keys}

    def deduplicate_results(
        self,
        results,
        key="question",
        filter_fn=lambda x: x["score"] > Env.QUESTION_SIMILARITY_THRESHOLD, # 过滤函数，问题相似度阈值要大于设定的阈值才保留
    ):
        seen_questions = {} # 改为字典，用于记录每个内容对应的最高分数结果
        merged_results = [] # 合并结果
        for result in results:
            q_text = result[key]
            # 如果内容不在字典中，或者新结果分数更高
            if q_text not in seen_questions or result["score"] > seen_questions[q_text]["score"]:
                seen_questions[q_text] = result
        
        # 应用过滤函数
        for result in seen_questions.values():
            if not filter_fn or filter_fn(result):
                merged_results.append(result)
        
        # 根据分数排序，保证高分在前
        merged_results.sort(key=lambda x: x["score"], reverse=True)
        return merged_results

    def deduplicate_chunks(self, chunks, filter_fn=None):
        return self.deduplicate_results(
            chunks, key="chunk_content", filter_fn=filter_fn
        )

    # 核心查询方法
    async def search_question(
        self,
        organization_id: int,
        embedding: List[float] = None,
        language: str = "zh-CN",
    ): 
        """搜索问题集合，同时匹配问题和答案
        Args:
            organization_id: 组织ID
            embedding: 预计算的embedding向量
            language: 语言过滤
        Returns:
            direct_answer: 相似度最高的回答
            all_results: 过滤后的所有结果
        """

        # 向量数据库搜索问题，先尝试在问题字段中搜索
        question_results = await search_question_collection(
            organization_id,
            search_type="question",
            language=language,
            embedding=embedding,
        )

        # 过滤问题结果，使用问题相似度阈值
        filtered_question_results = [
            result
            for result in question_results
            if result["score"] > Env.QUESTION_SIMILARITY_THRESHOLD
        ]

        # 检查是否有直接可返回的问题结果
        direct_answer = None
        if (
            filtered_question_results
            and filtered_question_results[0]["score"] > Env.DIRECT_RETURN_THRESHOLD
        ):
            direct_answer = filtered_question_results[0]
            return direct_answer, filtered_question_results

        # 如果没有直接可返回的结果，继续搜索答案，再尝试在答案字段中搜索
        answer_results = await search_question_collection(
            organization_id,
            search_type="answer",
            language=language,
            embedding=embedding,
        )

        # 过滤答案结果，使用答案相似度阈值
        filtered_answer_results = [
            result
            for result in answer_results
            if result["score"] > Env.ANSWER_SIMILARITY_THRESHOLD
        ]

        # 合并并去重结果，不需要额外的相似度过滤，因为已经分别过滤过了
        all_results = self.deduplicate_results(
            filtered_question_results + filtered_answer_results,
            filter_fn=None,  # 移除这里的过滤条件
        )

        # 检查合并后的结果是否有可直接返回的答案
        if all_results and all_results[0]["score"] > Env.DIRECT_RETURN_THRESHOLD:
            direct_answer = all_results[0]

        return direct_answer, all_results

    async def get_relevant_chunks(
        self,
        organization_id: int,
        embedding: List[float] = None,
        language: str = "zh-CN",
    ):
        """获取相关的文档块
        Args:
            organization_id: 组织ID
            embedding: 预计算的embedding向量
            language: 语言过滤
        """
        # 向量数据库搜索文档块
        chunk_results = await search_chunk_collection(
            organization_id,
            language=language,
            embedding=embedding,
        )

        # 过滤文档块，使用文档块相似度阈值
        relevant_chunks = self.deduplicate_chunks(
            chunk_results,
            filter_fn=lambda x: x["score"] > Env.CHUNK_SIMILARITY_THRESHOLD,
        )

        return relevant_chunks, chunk_results

    # 通过LLM生成RAG回答
    async def generate_rag_response(
        self,
        query: str, # 用户输入的问题
        histories: List[dict], # 对话历史
        context: str, # 获取的文档块和QA对
        **kwargs
    ):
        print(f"[DEBUG-RAG-RESPONSE] 进入generate_rag_response方法")
        language = kwargs.get("language", "zh-CN") # 从kwargs获取language，若无则使用默认值
        messages = list(histories)
        # 最后一条user消息换成新的
        messages[-1] = {
            "role": "user",
            "content": f"""
            这个是用户的问题: 
            {query}

            这些是知识库里搜索到的相关内容: 
            <<<{context}>>>

            请根据知识库里的内容回答用户的问题，回答要求如下:
            1. 请务必使用 {self.LANGUAGE_MAPPING.get(language, language)} 回答，无论上下文或问题使用的是什么语言。
            2. 回答必须是自然的口语表达，因为你的输出将被用于语音合成系统。不要包含任何格式标记或特殊字符。
            3. 如果用户询问的内容不涉及知识库的内容，则<<<>>>标签可能为空，或者和用户询问的内容无关，但是你不能说“我不知道”，“没有检索到知识库”等回答，而是要根据用户的问题和你模型训练里已有的知识，回答一个合理的内容。
            """,
        }


        character_prompt = kwargs.get(EXTRA_CHARACTER_PROMPT, "")
        setId = get_base_prompt_setId(self.organization_id)
        rag_prompt = get_prompt_template(setId, KEY_KNOWLEDGE_RAG, language) # 获取基础prompt模板

        combined_prompt = f"{rag_prompt}\n{character_prompt}".strip() # 合并基础prompt模板和人设prompt
        async for response in stream_chat(
            self.llm_service_config,
            messages,
            combined_prompt,
            **kwargs
        ):
            if response.data:
                response.language = language
            response.data["source"] = SOURCE_QA_RAG
            yield response

    # 通过LLM选择QA回答
    async def qa_with_llm(self, query: str, qa_results: List[dict], **kwargs):
        language = kwargs.get("language", "zh-CN") # 从kwargs获取language，若无则使用默认值
        try:
            print(f"[DEBUG-QA-LLM] 进入qa_with_llm方法")
            print(f"[DEBUG-QA-LLM] 输入查询: '{query}'")
            print(f"[DEBUG-QA-LLM] 结果数量: {len(qa_results)}")
            
            qa_json = [
                self.filter_json(qa, ["question", "answer", KEY_QA_PAIR_ID])
                for qa in qa_results
            ]
            context = json.dumps(qa_json, ensure_ascii=False)
            print(f"[DEBUG-QA-LLM] 构建的上下文长度: {len(context)} 字符")
            
            rewrite = kwargs.get("rewrite", "")
            messages = [
                {
                    "role": "user",
                    "content": f"QA Data: <<<{context}>>>\n\nQuestion: {query}\nRewrite: {rewrite}",
                }
            ]
            
            # 修正参数顺序
            try:
                setId = get_base_prompt_setId(self.organization_id)
                qa_prompt = get_prompt_template(setId, KEY_KNOWLEDGE_QA, language)
                
                # 打印LLM接收到的完整输入数据
                print(f"[DEBUG-QA-LLM-FULL-INPUT] ========================")
                print(f"[DEBUG-QA-LLM-FULL-INPUT] 系统提示: {qa_prompt}")
                print(f"[DEBUG-QA-LLM-FULL-INPUT] ------------------------")
                print(f"[DEBUG-QA-LLM-FULL-INPUT] 用户消息: {messages[0]['content']}")
                print(f"[DEBUG-QA-LLM-FULL-INPUT] ========================")
                
                # 检查llm_client.send_messages的实现方式
                debug_combined_input = f"系统提示:\n{qa_prompt}\n\n用户消息:\n{messages[0]['content']}"
                print(f"[DEBUG-QA-LLM-COMBINED] {debug_combined_input}")
                
            except Exception as e:
                print(f"[DEBUG-QA-LLM-ERROR] 获取提示模板错误: {str(e)}")
                return None
            
            print(f"[DEBUG-QA-LLM] 开始LLM调用...")
            try:
                # 为了捕获可能的内部转换，添加相关调试代码
                if hasattr(llm_client, 'debug_last_prompt'):
                    llm_client.debug_last_prompt = None
                    
                # LLM对于QA的分析，返回json格式
                message = await send_messages(
                    llm_service_config=self.llm_service_config,
                    messages=messages,
                    sys_prompt=qa_prompt,
                    response_format={"type": "json_object"},
                    timeout=10,
                    **kwargs,
                )
                
                # 尝试打印最终发送的完整提示（如果client实现支持）
                if hasattr(llm_client, 'debug_last_prompt') and llm_client.debug_last_prompt:
                    print(f"[DEBUG-QA-LLM-ACTUAL] 实际发送的完整提示:\n{llm_client.debug_last_prompt}")
                
                print(f"[DEBUG-QA-LLM] LLM响应: {message.content[:200]}...")
            except Exception as e:
                print(f"[DEBUG-QA-LLM-ERROR] LLM调用错误: {str(e)}")
                return None
            
            # 处理分析结果
            try:
                json_content = json.loads(message.content)
                print(f"[DEBUG-QA-LLM] 解析的JSON: {json_content}")
                
                qna_pair_id = json_content.get(KEY_QA_PAIR_ID, "")
                print(f"[DEBUG-QA-LLM] 获取到的QA对ID: {qna_pair_id}")
                
                if not qna_pair_id:
                    print(f"[DEBUG-QA-LLM] 未找到有效的QA对ID")
                    return None
                
                # 查找匹配的QA对
                matching_qa = None
                for qa in qa_results:
                    if str(qa.get(KEY_QA_PAIR_ID, "")) == str(qna_pair_id):
                        matching_qa = qa
                        break
                
                print(f"[DEBUG-QA-LLM] 匹配结果: {matching_qa is not None}")
                if matching_qa:
                    print(f"[DEBUG-QA-LLM] 匹配到的答案: {matching_qa.get('answer', '')[:50]}...")
                
                return matching_qa
            except json.JSONDecodeError as e:
                print(f"[DEBUG-QA-LLM-ERROR] JSON解析错误: {str(e)}")
                print(f"[DEBUG-QA-LLM-ERROR] 原始内容: {message.content}")
                return None
        except Exception as e:
            print(f"[DEBUG-QA-LLM-ERROR] 方法异常: {str(e)}")
            import traceback
            print(f"[DEBUG-QA-LLM-ERROR] 异常堆栈: {traceback.format_exc()}")
            return None

    # 响应生成方法
    def gen_qa_resp(self, direct_answer, query, qa_results, output_source, **kwargs):
        qa_language = direct_answer.get("language", "zh-CN")
        answer = direct_answer.get("answer", "")
        extra = {
            "qna_pair_id": direct_answer.get("qna_pair_id"),
            "language": qa_language,
        }
        final_pure_text_answer = self.combine_qa(direct_answer)
        device_id = kwargs.get("deviceId", "default_device_id")
        sessionId = kwargs.get("sessionId")
        rewrite = kwargs.get("rewrite", "")
        log_knowledge_handler(
            device_id,
            query,
            rewrite,
            get_source_name(output_source),
            qa_results,
            final_pure_text_answer,
        )
        print(
            f'[Knowledge] {json.dumps({"org_id": self.organization_id, "device_id": device_id, "query": query, "rewrite": rewrite, "source": get_source_name(output_source), "answer": final_pure_text_answer}, ensure_ascii=False)}'
        )
        data = wrap_chat_data(sessionId, output_source, answer, extra)
        return wrap_response(data, RESPONSE_CODE_END, qa_language)

    # 主处理方法
    async def handle(self, messages, **kwargs):
        """
        处理知识库查询请求，实现多级查询策略。

        查询策略（按优先级）:
        1. 直接问答匹配：从问答库中查找完全匹配的答案
        2. 重写问题查询：使用重写后的问题再次查找匹配
        3. LLM辅助问答：使用LLM分析问答库中的问答结果
        4. 基础RAG检索：基于原始问题的文档块检索
        5. 重写RAG检索：使用重写问题进行文档块检索
        6. RAG生成答案：基于[4]或者[5]中检索的内容（QA对+文档块）生成回答
        7. Fallback处理：如果都没有匹配，使用后备处理器

        Args:
            messages (list): 对话历史记录
            **kwargs: 额外参数
                - rewrite (str): 重写后的问题
                - deviceId (str): 设备ID
                - sessionId (str): 会话ID
                - 其他LLM调用参数

        Yields:
            Response: 包含以下内容的响应对象:
                - 处理状态更新
                - 查询结果
                - 错误信息（如果有）
        """
        start_time = time.time()
        try:
            # 初始化参数
            language = kwargs.get("language", "zh-CN")
            rewrite = kwargs.get("rewrite", "")
            query = self.get_query(messages)
            device_id = kwargs.get("deviceId", "default_device_id")
            sessionId = kwargs.get("sessionId")
            organizationId = kwargs.get("organizationId")

            print(f"[DEBUG-FLOW] ===== 开始知识处理流程 =====")
            print(f"[DEBUG-FLOW] 查询: '{query}', 改写: '{rewrite}'")

            # 预先批量获取embeddings
            texts_to_embed = [query]
            if rewrite and rewrite != query:
                texts_to_embed.append(rewrite)

            embedding_start = time.time()
            print(f"[DEBUG-FLOW] 预先批量获取embeddings: {texts_to_embed}")
            embeddings = await get_embedding(
                text=texts_to_embed, 
                embedding_config=self.embedding_config,
                caller_module="knowledge_handler",
                **kwargs
            )

            if Env.PRINT_EMBEDDING_TIME:
                print(f"[Debug] Batch embeddings time: [{time.time() - embedding_start:.3f}s]")

            query_embedding = embeddings[0]
            rewrite_embedding = embeddings[1] if len(embeddings) > 1 else None

            # 使用self.language来翻译状态消息
            data = wrap_chat_data(
                sessionId,
                SOURCE_STATUS,
               _("我正在找答案呢，稍等一下"),
            )
            yield wrap_response(data, RESPONSE_CODE_SEGMENT_END, language)

            # 策略1：直接问答匹配，使用query_embedding，未使用LLM
            question_search_start = time.time()
            print(f"[DEBUG-FLOW] 开始直接问答匹配搜索...")
            direct_answer, qa_results = await self.search_question(
                self.organization_id,
                embedding=query_embedding,
                language=language,
            )
            
            # 打印搜索结果详情
            print(f"[DEBUG-RESULTS] 问题搜索结果: {len(qa_results)}条")
            for i, result in enumerate(qa_results[:3]):  # 只打印前3条
                print(f"[DEBUG-RESULTS] 结果{i+1}: 问题='{result.get('question', '')}', 分数={result.get('score', 0)}, ID={result.get(KEY_QA_PAIR_ID, '')}")
            
            print(f"[DEBUG-FLOW] 直接答案: {direct_answer is not None}")
            if direct_answer:
                print(f"[DEBUG-DIRECT] 直接答案分数: {direct_answer.get('score', 0)}")
                print(f"[DEBUG-DIRECT] 直接答案内容: {direct_answer.get('answer', '')[:50]}...")

            if Env.PRINT_SEARCH_TIME:
                print(f"[Debug] Question search time: [{time.time() - question_search_start:.3f}s]")

            if direct_answer:
                print(f"[DEBUG-FLOW] 找到直接匹配答案，返回SOURCE_QA_VECTOR")
                resp = self.gen_qa_resp(
                    direct_answer, query, qa_results, SOURCE_QA_VECTOR, **kwargs
                )
                yield resp
                if Env.PRINT_TOTAL_TIME:
                    print(f"[Knowledge] Total processing time: {time.time() - start_time:.3f}s")
                return

            # 策略2：重写问题查询，使用rewrite_embedding，未使用LLM
            if rewrite_embedding:
                print(f"[DEBUG-FLOW] 开始重写问题匹配搜索...")
                rewrite_search_start = time.time()
                rewrite_answer, rewrite_qa_results = await self.search_question(
                    self.organization_id,
                    embedding=rewrite_embedding,
                    language=language,
                )
                
                # 打印重写搜索结果
                print(f"[DEBUG-REWRITE] 重写搜索结果: {len(rewrite_qa_results)}条")
                if rewrite_qa_results:
                    print(f"[DEBUG-REWRITE] 首条结果分数: {rewrite_qa_results[0].get('score', 0)}")
                
                if Env.PRINT_SEARCH_TIME:
                    print(f"[Debug] Rewrite question search time: [{time.time() - rewrite_search_start:.3f}s]")

                if rewrite_answer:
                    print(f"[DEBUG-FLOW] 找到重写匹配答案，返回SOURCE_QA_VECTOR_REWRITE")
                    resp = self.gen_qa_resp(
                        rewrite_answer,
                        query,
                        rewrite_qa_results,
                        SOURCE_QA_VECTOR_REWRITE,
                        **kwargs,
                    )
                    yield resp
                    if Env.PRINT_TOTAL_TIME:
                        print(f"[Knowledge] Total processing time: {time.time() - start_time:.3f}s")
                    return
                qa_results = self.deduplicate_results(qa_results + rewrite_qa_results)
                print(f"[DEBUG-FLOW] 合并结果后共有: {len(qa_results)}条")

            data = wrap_chat_data(
                sessionId,
                SOURCE_STATUS,
                _("正在找相关资料，稍微等下哦"),
            )
            yield wrap_response(data, RESPONSE_CODE_SEGMENT_END, language)

            # 策略3：如果不能直接命中，则用LLM带qa_results判断回答，最终回答未使用LLM
            if qa_results:
                print(f"[DEBUG-FLOW] 进入LLM辅助QA流程, 结果数: {len(qa_results)}")
                print(f"[DEBUG-THRESHOLDS] 问题相似度阈值: {Env.QUESTION_SIMILARITY_THRESHOLD}, 直接返回阈值: {Env.DIRECT_RETURN_THRESHOLD}")
                
                # 检查分数是否满足使用LLM的条件
                top_score = qa_results[0].get('score', 0) if qa_results else 0
                print(f"[DEBUG-FLOW] 顶部结果分数: {top_score}")
                
                qa_llm_start = time.time()
                print(f"[DEBUG-LLM-QA] 准备调用qa_with_llm, 传入结果数: {len(qa_results)}")
                
                try:
                    direct_answer = await self.qa_with_llm(query, qa_results, **kwargs)
                    print(f"[DEBUG-LLM-QA] qa_with_llm调用完成: {direct_answer is not None}")
                    
                    if direct_answer:
                        print(f"[DEBUG-FLOW] LLM选定了答案，返回SOURCE_QA_VECTOR_LLM")
                        print(f"[DEBUG-LLM-QA] 选定答案ID: {direct_answer.get(KEY_QA_PAIR_ID)}")
                        resp = self.gen_qa_resp(
                            direct_answer, query, qa_results, SOURCE_QA_VECTOR_LLM, **kwargs
                        )
                        yield resp
                        if Env.PRINT_TOTAL_TIME:
                            print(f"[Knowledge] Total processing time: {time.time() - start_time:.3f}s")
                        return
                    else:
                        print(f"[DEBUG-LLM-QA] LLM未选定任何答案")
                        if Env.PRINT_SEARCH_TIME:
                            print(f"[Debug] LLM search question time: [{time.time() - qa_llm_start:.3f}s]\n")
                except Exception as e:
                    print(f"[DEBUG-ERROR] qa_with_llm异常: {str(e)}")
                    import traceback
                    print(f"[DEBUG-ERROR] 异常堆栈: {traceback.format_exc()}")

            # 策略4：chunk搜索，复用query_embedding，为策略6提供数据
            chunk_search_start = time.time()
            chunks, chunk_search_results = await self.get_relevant_chunks(
                self.organization_id,
                embedding=query_embedding,
                language=language,
            )
            if Env.PRINT_SEARCH_TIME:
                print(
                    f"[Debug] Chunk search time: [{time.time() - chunk_search_start:.3f}s]"
                )

            # 策略5：重写chunk搜索，复用rewrite_embedding，为策略6提供数据
            if rewrite and rewrite != query:
                rewrite_chunks, rewrite_chunk_search_results = (
                    await self.get_relevant_chunks(
                        self.organization_id,
                        embedding=rewrite_embedding,
                        language=language,
                    )
                )
                # 合并并去重chunk结果
                chunks = self.deduplicate_chunks(chunks + rewrite_chunks)
                chunk_search_results = self.deduplicate_chunks(
                    chunk_search_results + rewrite_chunk_search_results
                )
                if Env.PRINT_SEARCH_TIME:
                    print(
                        f"[Debug] Rewrite chunk search time: [{time.time() - chunk_search_start:.3f}s]"
                    )

            # 策略6：如果有chunk，则进行RAG生成，使用LLM生成回答
            if chunks or qa_results:  # 如果有任何检索结果
                data = wrap_chat_data(
                    sessionId,
                    SOURCE_STATUS,
                    _("正在整理资料，稍等片刻"),
                )
                yield wrap_response(data, RESPONSE_CODE_SEGMENT_END, language)
                final_result = ""
                output_source = SOURCE_QA_RAG
                rag_start = time.time()

                # 构建上下文，结合QA对和文档块
                context = ""
                if qa_results:
                    qatext = " ".join([self.combine_qa(q) for q in (qa_results or [])])
                    context += f"QA Data：<<<{qatext}>>>"

                if chunks:
                    chunk_text = " ".join([c["chunk_content"] for c in (chunks or [])])
                    context += f"Chunk Data：<<<{chunk_text}>>>"

                # 生成最终答案
                async for chunk in self.generate_rag_response(
                    query, messages, context, **kwargs
                ):
                    if chunk.data:
                        content = chunk.data.get("message", "")
                        final_result += content
                    if chunk.code == RESPONSE_CODE_END:
                        if Env.PRINT_GENERATION_TIME:
                            print(
                                f"[Debug] RAG generation time: {time.time() - rag_start:.3f}s"
                            )
                        # 记录查询日志
                        log_knowledge_handler(
                            device_id,
                            query,
                            rewrite,
                            get_source_name(output_source),
                            qa_results + chunk_search_results,
                            final_result,
                        )
                        print(
                            f'[Knowledge] {json.dumps({"org_id": self.organization_id, "device_id": device_id, "query": query, "rewrite": rewrite, "source": get_source_name(output_source), "answer": final_result}, ensure_ascii=False)}'
                        )
                    yield chunk
            else:  # 策略7：如果都没有，则使用fallback
                if self.fallback_handler:
                    data = wrap_chat_data(
                        sessionId,
                        SOURCE_STATUS,
                        _("我正在组织回答，稍等一下哦"),
                    )
                    yield wrap_response(data, RESPONSE_CODE_SEGMENT_END, language)
                    final_result = ""
                    output_source = SOURCE_QA_FALLBACK
                    fallback_start = time.time()

                    # 修改最后一条用户消息，添加要求
                    last_message = messages[-1].copy()
                    requirements = textwrap.dedent(f"""
                        Requirements:
                        1. Please provide your response in {self.LANGUAGE_MAPPING.get(language, language)}, regardless of the language used in the context or question. This is mandatory.
                        2. Provide your response in natural spoken language only, as your output will be processed by a text-to-speech system. Do not include any formatting marks or special characters.
                        """).strip()
                    last_message["content"] = (
                        f"{last_message['content']}\n{requirements}"
                    )
                    modified_messages = messages[:-1] + [last_message]

                    async for chunk in self.fallback_handler.handle(
                        modified_messages, **kwargs
                    ):
                        if chunk.data:
                            content = chunk.data.get("message", "")
                            final_result += content
                            # 设置 language 到 Response 对象
                            chunk.language = language
                        if chunk.code == RESPONSE_CODE_END:
                            if Env.PRINT_GENERATION_TIME:
                                print(
                                    f"[Knowledge] Fallback generation time: {time.time() - fallback_start:.3f}s"
                                )
                            # 记录fallback日志
                            log_knowledge_handler(
                                device_id,
                                query,
                                rewrite,
                                get_source_name(output_source),
                                qa_results + chunk_search_results,
                                final_result,
                            )
                            print(
                                f'[Knowledge] {json.dumps({"org_id": self.organization_id, "device_id": device_id, "query": query, "rewrite": rewrite, "source": get_source_name(output_source), "answer": final_result}, ensure_ascii=False)}'
                            )
                        yield chunk
                else:  # 完全无法回答
                    data = wrap_chat_data(
                        sessionId,
                        SOURCE_REJECT,
                        _("嗯，这个问题我暂时没法回答哦"),
                    )
                    yield wrap_response(data, RESPONSE_CODE_END, language)

            if Env.PRINT_TOTAL_TIME:
                print(
                    f"[Knowledge] Total processing time: {time.time() - start_time:.3f}s"
                )
        except Exception as e:
            import traceback
            tb = traceback.extract_tb(e.__traceback__)
            filename, line_no, func_name, text = tb[-1]
            err_msg = log_exception(device_id, e, f"KnowledgeHandler [位于 {filename}:{line_no}, 函数: {func_name}, 代码: {text}], 总耗时: {time.time() - start_time:.3f}秒")
            yield wrap_error(
                ERROR_AI_SERVER, err_msg, "KnowledgeHandler", language
            )

    def update_llm_service_config(self, llm_service_config: dict):
        """更新模型设置"""
        # 只更新llm_config中非None的值
        for key, value in llm_service_config.items():
            if value is not None:
                self.llm_service_config[key] = value
        # 如果fallback_handler也支持模型更新，则同时更新
        if self.fallback_handler and hasattr(self.fallback_handler, "update_llm_service_config"):
            self.fallback_handler.update_llm_service_config(llm_service_config)

    def update_embedding_config(self, embedding_config: dict):
        """更新嵌入配置"""
        for key, value in embedding_config.items():
            if value is not None:
                self.embedding_config[key] = value  
{"knowledge": "用户想要了解社康和家庭医生相关的问题，如果选择了这个行为，记得输出rewrite字段。你需要结合上下文，并且理解可能由于收音导致的错别字问题，这种情况下你也需要纠正。重写后的问题需要是一个完整的问题以便于检索系统来做检索，如果问题包含多个的话，可以拆成多个问题，以列表的形式展示。例如：{action: knowledge, rewrite:\"[你们公司的介绍，你们老板是谁]\"}（如果query是介绍一下你们公司和老板）。如果用户问\"帮我播放中耳炎的注意事项\"和\"帮我播放神经性耳聋的介绍视频\"之类的问题你也需要选择这个意图。", "registration": "当用户明确表示需要挂号时，选择此行为。明确的挂号意图包括：指定某个科室的号（例如：帮我挂眼科的号）；指定某位医生的号（例如：我要挂眼科李医生今天的号）；查询某个科室的医生信息（例如：眼科有哪些医生）；以及Assistant询问用户是否要挂号，User表示肯定（例如：Q: 请问是需要某某科室的号源吗？ A: 是的）。但是如果用户是直接表明了，我想要挂号的想法，并且你无法从历史记录里面查询到具体应该挂什么科室，我们应该优先选择medical-guidance行为来帮助用户确定要挂的科室。注意：如果用户尚未登录，即使意图明确，也应优先引导用户登录，此时需要选择login行为。对于挂号的行为（包括选择日期和医生），如果你发现无法简单直接通过点击某个按钮来完成任务，请选择registration行为。", "medical-guidance": "当用户描述自己身体不适、出现症状，或不确定需要挂哪个科室时，选择此行为。常见场景包括：用户问“我肚子痛怎么办？”；用户描述“卡鱼刺了，需要挂哪个科？”；或用户简单说“我不舒服，我肚子疼”;如果用户只是说我不舒服想挂号的时候，这种情况下我们需要选择这个意图，因为需要先导诊。但是如果用户明确指明了要挂号的科室，将不再进行导诊。", "login": "用户表明了需要登录或者某项业务需要先登录。例如：用户需要挂号但尚未登录，此时需要选择登录，并在extrainfo中带上登录后需要继续的操作，如 {action: 'login', extrainfo:'登录后需要跳转到挂号页'}；如果提到了具体科室，如‘我要挂骨科’，extrainfo中应提到登录后跳转到骨科的挂号信息。但是如果用户单纯说“我单纯头痛，没有其他症状。”，这种情况不能直接选择login，而是选择medical-guidance。除此之外，这个事情也非常重要就是如果用户没有登录。并且用户的意图为儿童建档、缴费、发票打印、取号签到等，必须得选择login行为。因为这些行为必须先登录才能完成。但是打印报告或者检查单不需要登录。但是有三种特殊的情况。如果用户明确说药品价格，医院介绍，项目价格是无需登录的。这个和query里面的逻辑描述是一样的。", "way-finding": "当用户询问路线或位置时，选择此行为。例如：用户问“洗手间在哪里？”或“我应该到哪里去取结果？”", "unclear-action": "当用户提及不存在的功能按钮或者输入无法归类到任何action时，选择此行为。作为前台客服，应主动引导用户回到可选的业务操作上。例如：用户说了让人无法理解的话，输出为 {action: 'unclear-action', extrainfo:'我不是很理解您的意思呢，请您再说一遍呢。'}", "chat": "当用户只是随意聊天、谈天说地，不涉及具体业务或咨询时，选择此行为。例如：用户问“你叫什么？你是谁？介绍一下你自己。”，简单输出 {action: 'chat'}即可。", "payment": "用户想要缴费", "print": "用户想要打印检查单，或者说用户想要打印报告", "query": "当用户需要查询医院相关信息（包括药品价格、检查项目费用、个人消费记录、医院/科室/医生介绍）或进行账户操作（修改手机号/密码）时，选择该选项。这是一些example，我想了解一下神经内科这个科室是干什么的，我想看一下医院的神经内科的介绍，我们做体检需要多少钱，我想查询一下医生信息，我想查询一下口腔科王医生。但是如果用户在打招呼对话页的话，请注意只在打招呼对话页生效。有三种情况要在extrainfo里面带上要跳转的pageId。如果用户说药品价格，输出为{action: 'query', extrainfo:'如果在处理查询，跳转到7.1-Invoice-Printing-Success-Page'}。如果用户说项目价格，输出为{action: 'query', extrainfo:'如果在处理查询，跳转到7.2.2-Item-Price-Inquiry-Page'}。如果用户说医院介绍，输出为{action: 'query', extrainfo:'如果在处理查询，跳转到7.3-Hospital-Introduction-Page'}。这三种情况是无需登录的。", "record": "用户想要建档，包括儿童建档。除此之外办卡和补卡也应该选择这个行为。", "get-number": "用户想要取号", "change-phone": "用户想要修改手机号", "change-password": "用户想要修改密码"}
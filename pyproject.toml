[project]
name = "ai-service"
version = "1.1.1"
description = "AI Service Project"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "DawnInator", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "langchain",
    "python-docx",
    "python-pptx",
    "PyMuPDF",
    "requests",
    "aiohttp",
    "openai",
    "fastapi",
    "fastapi-babel==0.0.9",
    "pymilvus",
    "uvicorn",
    "pymysql",
    "peewee",
    "peewee-jsonfield",
    "openpyxl",
    "py-spy",
    "tiktoken",
    "lingua-language-detector>=1.3.3",
    "opencc",
    "cachetools",
    "jinja2",
    "cryptography",
    "cython"
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-asyncio",
    "pyinstaller>=6.3.0",
    "python-dotenv"
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
asyncio_mode = "auto"
addopts = "-v --tb=short --maxfail=1"

[build-system]
requires = ["hatchling", "python-dotenv"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.uv]



[[tool.uv.index]]
name = "pytorch"
url = "https://download.pytorch.org/whl/cu121"  # CUDA 12.1 的官方源
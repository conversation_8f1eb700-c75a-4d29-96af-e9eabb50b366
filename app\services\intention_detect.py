from app.services import *
import json
from app.config import Env
from app.base.jinja2_formatter import templater
from app.base.store_utils import session, ORG_CONFIG, get_ctx_with_bg_info
from app.services.llm_client import LLMServiceConfig, send_messages

class IntentDetector:
    """意图检测器类，用于识别和分类用户的对话意图"""

    def __init__(self, intentions, formatters,  llm_service_config: LLMServiceConfig):
        self.llm_service_config = dict(llm_service_config) if llm_service_config else {}
        self.llm_service_config["model"] = self.llm_service_config.get("model") or Env.DEFAULT_INTENTION_MODEL

        if not intentions and not formatters:
            raise ValueError("意图配置和格式化器配置不能同时为空")


    async def fix_intention_response(self, content: str, intentions: dict, formatters: dict):
        intention = ""
        try:
            json_content = json.loads(content)
            intention = json_content.get("intention")

            if intention: #如果能取到intention，则尝试用intention匹配意图和格式化器
                intention, _ = await self.match_intention_formatter(intention, intentions, formatters)
                json_content["intention"] = intention
                return json_content
        except json.JSONDecodeError as e:
            print(f"[Warning] Intent detection failed: {content}, error: {e}")
            pass

        #如果取不到intention,用原文匹配意图和格式化器
        intention, _ = await self.match_intention_formatter(content, intentions, formatters)
        return {
            "intention": intention,
        }
    


    async def build_intent_detect_messages(self, ctx: dict, language: str):
        """
        构建意图检测消息
        """

        template = """
对于 intention 和 extra 的输出规则:

    {# 基础数据准备 #}
    {% set button_texts = [] %}
    {% set uiConfig = orgConfig.uiConfig %}
    {% if uiConfig %}
        {% set current_page = uiConfig.pages[backgroundInfo.page] | default({}) %}
        {% set intentions = orgConfig.intentionConfig|keep(current_page.optionalActions | default(None)) %}
        {% set directReply = uiConfig.directReply.items() %}
        {% set buttons = uiConfig.buttons|keep(current_page.buttonList | default([])) %}
        {% for key, button in buttons.items() %}
            {% set button_text = key ~ "(" ~ button.name|render ~ ": " ~ button.description|render ~ ")" %}
            {% set _ = button_texts.append(button_text) %}
        {% endfor %}
        {% set fallback = uiConfig.fallback.items() %}
    {% else %}
        {% set current_page = {} %}
        {% set intentions = orgConfig.intentionConfig %}
        {% set directReply = {} %}
        {% set fallback = {} %}
    {% endif %}

    {# 页面状态信息 #}
    {% if current_page %}
    {{ "当前页面：{{ name }}({{ description }})" | render(current_page) }}
    {{ current_page.business | default('') | render(backgroundInfo.business) }}
    {% endif %}
    {% if uiConfig %}
    当前状态: {{ uiConfig.status[backgroundInfo.status] }}
    {% endif %}

    {# 意图选择提示 #}
    根据会话上下文、背景信息来识别用户意图。用户意图可选的范围为：

    {# 直接回复选项 #}
    {% for key, reply in directReply %}
    - {{key}}：{{ reply.description }}
    {% endfor %}

    {# 业务意图选项 #}
    {% for key, description in intentions.items() %}
    - {{ key }}: {{ description }}
    {% endfor %}

    {# 按钮点击选项 #}
    {% if button_texts %}
    - Dir-button：当用户输入能够直接清晰地对应页面上的按钮时触发。
    当前页面按钮包括：{{ button_texts|join(", ") }}
    注意：如果用户表达的是复杂的指令性任务而非简单的按钮点击意图，则不应触发此行为。
    但特殊情况如前进、后退、确认、忽略、取消、打印全部、全部缴费等除外。
    输出格式为{"intention":"Dir-button", "extra":"对应的按钮ID"}
    {% endif %}

    {# 不明确操作选项 #}
    {% for key, reply in fallback %}
    - {{ key }}：{{reply.description}}
    {% endfor %}
    注意顺序排在前的优先级更高。
    输出的intention字段要是字符串，不能是数组。如"intention":"意图名称"
    注意输出必须为纯JSON，不需要额外的字符，在输出intention的时候也需要思考是否需要带上extra。

对于 rewrite 的输出规则:
    基于用户输入的内容，在下列情况做一定改写，使改写后的内容简洁、清晰且与上下文相关，如无必要，保留原文。
    改写规则：
    1.聚焦：思考用户查询的主要目的，移除不必要的词汇
    2.纠正谐音：语音识别有可能识别成谐音字，根据上下文纠正谐音字
    3.上下文指代消解：结合上下文将查询中指代词替换为准确的对象。将重写后的内容输出到rewrite字段
    4.输出的rewrite字段要是字符串，不能是数组。如"rewrite":"改写后的内容"

    什么时候不进行改写(rewrite字段为空):
    1. 用户输入的是问候语，如“你好”、“您好”、“早上好”、“晚上好”等。
    2. 用户输入的内容毫无意义，如无意义的单词和拟声词或者句子，如“ma”,“ya”,“asdwassnn”等
    3. 用户输入的文字逻辑严重混乱，你无法识别用户意图，同时和 intention 里的信息严重不相关，如“不我还皮图图”，“拍室内世锦赛的是”等

输出格式为{"intention":"", "rewrite":""(optional),"extra":""(optional)}
"""
   
            
        template_obj = templater(template, language=language)
        ret = template_obj.render(ctx)
        return ret
    

    async def detect(self, messages:list[dict[str, str]], **kwargs):
        """检测用户意图，返回(intention, rewrite, extraInfo, message)"""

        if Env.PRINT_LLM_CONFIG:
            if self.llm_service_config.get("model"):
                print(f"[INTENTION] 意图服务模型: {self.llm_service_config['model']}")
            if self.llm_service_config.get("api_key"):
                print(f"[INTENTION] 意图服务api_key: {self.llm_service_config['api_key']}")
            if self.llm_service_config.get("base_url"):
                print(f"[INTENTION] 意图服务base_url: {self.llm_service_config['base_url']}")

        intention = kwargs.get("intention", "")
        sessionId = kwargs.get("sessionId", "")
        backgroundInfo = kwargs.get("backgroundInfo", {})

        # 获取组合后的上下文
        ctx = await get_ctx_with_bg_info(sessionId, backgroundInfo)
        org_config = ctx.get(ORG_CONFIG, {})

        intentions = org_config.get(KEY_INTENTION)
        formatters = org_config.get(KEY_UI_CONFIG, {}).get(KEY_FORMATTERS)

        # 如果用户指定了intention，直接匹配并返回结果
        if intention:
            matched_intention, _ = await self.match_intention_formatter(intention, intentions, formatters)
            if matched_intention:
                return {
                    "intention": matched_intention,
                }

        language = kwargs.get("language", "zh-CN")
        
        sys_prompt = await self.build_intent_detect_messages(ctx, language)

        messages = messages[-7:]
        message = await send_messages(
            llm_service_config=self.llm_service_config,
            messages=messages,
            sys_prompt=sys_prompt,
            response_format={"type": "json_object"},
            temperature=0.3,
            **kwargs,
        )
        
        if not isinstance(formatters, dict):
            formatters = {}
        response = await self.fix_intention_response(message.content, intentions, formatters)
        return response
    
    async def match_intention_formatter(self, intention: str, intentions: dict, formatters: dict) -> tuple[str, str]:
        intention, formatter = await self.match_intention(intention, formatters)
        if formatter:
            return intention, formatter
        else:
            return await self.match_intention(intention, intentions)

    async def match_intention(self, intention: str, intentions: dict) -> tuple[str, str]:

        
        if intentions:
            if intention in intentions:
                return intention, intentions[intention]
            else:
                for key, value in intentions.items():
                    if intention in key or key in intention:
                        return key, value
                
        return intention, ""
    

    def update_llm_service_config(self, llm_service_config: dict):
        """更新模型设置"""
        # 只更新llm_config中非None的值
        for key, value in llm_service_config.items():
            if value is not None:
                self.llm_service_config[key] = value

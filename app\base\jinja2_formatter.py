import json
import logging
from typing import Any, Dict, List, Tuple
import functools
from babel import Locale
from babel.core import UnknownLocaleError

from jinja2 import Environment, pass_context, Undefined, StrictUndefined, Template, ChainableUndefined
from datetime import datetime
from app.utils.logger import log_text, log_exception
from app.services import DEVICE_AI_SERVER

def is_valid_language_code(code: str) -> bool:
    """验证语言代码是否合法
    
    Args:
        code: 语言代码，如 'zh-CN', 'en', 'zh-HK'
        
    Returns:
        bool: 是否是合法的语言代码
    """
    try:
        # 将 zh-CN 这样的代码转换为 zh_CN 格式
        normalized_code = code.replace('-', '_')
        Locale.parse(normalized_code)
        return True
    except (UnknownLocaleError, ValueError):
        return False

class Jinja2TemplateEngine:
    """使用纯Jinja2实现模板功能，管理环境和编译缓存"""
    
    def __init__(self, language: str = "zh-CN", strict_mode: bool = False, chainable: bool = True):
        """初始化Jinja2模板引擎
        
        Args:
            language: 当前使用的语言代码
            strict_mode: 是否使用严格模式（在错误时抛出异常而不是静默处理）
            chainable: 是否允许链式访问未定义属性，默认为True。注意：在strict_mode=True时此参数无效。
        """
        if not is_valid_language_code(language):
            log_text(DEVICE_AI_SERVER, f"无效的语言代码: {language}，将使用默认值 'zh-CN'")
            language = "zh-CN"
            
        self.language = language
        self.strict_mode = strict_mode
        self.chainable = chainable and not strict_mode  # 在严格模式下强制关闭chainable
        # 模板编译缓存
        self._template_cache: Dict[str, Template] = {}
        
        # 确定使用哪种 undefined 处理器
        if strict_mode:
            undefined_handler = StrictUndefined
        else:
            undefined_handler = ChainableUndefined if chainable else Undefined
        
        # 创建Jinja2环境
        self.env = Environment(
            autoescape=False,          # 不自动转义
            trim_blocks=True,          # 移除块级标签后的第一个换行
            lstrip_blocks=True,        # 移除块前的空白
            keep_trailing_newline=True, # 保留模板末尾的换行符
            undefined=undefined_handler
        )
        
        # 设置最终处理器，自动处理多语言
        self.env.finalize = self._auto_lang_finalize
        
        # 注册过滤器
        self.env.filters['keep'] = self._keep
        # 注册自定义模板过滤器 - 替代原有template函数
        self.env.filters['template'] = self._template_filter
        # 注册render过滤器 - 参数顺序与template相反
        self.env.filters['render'] = self._render_filter
        # 注册自定义JSON序列化过滤器
        self.env.filters['tojson'] = self._custom_tojson_filter
    
    def _get_fallback_localized_template(self, template_dict: Dict[str, str]) -> str:
        """为多语言模板字典获取回退语言的模板字符串
        
        Args:
            template_dict: 可能是多语言字典或普通字典
            
        Returns:
            str: 如果是多语言字典，返回选择的语言版本；
                如果是普通字典，返回整个字典的字符串表示
        """
        # 快速路径：如果字典为空，直接返回空字符串
        if not template_dict:
            return ""
        
        # 检查是否是多语言字典
        valid_lang_codes = [key for key in template_dict.keys() if is_valid_language_code(key)]
        
        # 如果没有任何有效的语言代码，说明这是普通字典
        if not valid_lang_codes:
            log_text(DEVICE_AI_SERVER, f"收到非多语言字典: {template_dict}")
            return str(template_dict)  # 返回整个字典的字符串表示
        
        # 到这里说明是多语言字典，按优先级选择语言：
        # 1. 当前语言
        # 2. 简体中文（zh-CN）
        # 3. 英语（en）
        # 4. 第一个可用的语言
        for lang in [self.language, "zh-CN", "en"]:
            if lang in template_dict:
                if lang != self.language:
                    log_text(DEVICE_AI_SERVER, f"模板语言 '{self.language}' 不存在，回退到 '{lang}'")
                return str(template_dict[lang])
        
        # 如果上述语言都不存在，使用第一个有效的语言
        fallback_lang = valid_lang_codes[0]
        log_text(DEVICE_AI_SERVER, f"模板语言 '{self.language}' 不存在，且无法找到首选回退语言，使用 '{fallback_lang}'")
        return str(template_dict[fallback_lang])

    def _auto_lang_finalize(self, value: Any) -> Any:
        """使用_get_fallback_localized_template处理多语言字典"""
        if isinstance(value, dict):
            return self._get_fallback_localized_template(value)
        return value
    
    def _keep(self, dict_value: Dict, keys: List[str] | None) -> Dict:
        """过滤字典，只保留指定键
        
        Args:
            dict_value: 要过滤的字典
            keys: 要保留的键列表，如果为None则保留所有键
            
        Returns:
            过滤后的字典
        """
        # 如果keys为None，返回完整字典
        if keys is None:
            return dict_value
        
        if not isinstance(dict_value, dict):
            log_text(DEVICE_AI_SERVER, f"警告: keep输入必须是字典，收到: {type(dict_value)}")
            return dict_value
            
        try:
            return {k: v for k, v in dict_value.items() if k in keys}
        except Exception as e:
            log_exception(DEVICE_AI_SERVER, e, "字典过滤失败")
            return dict_value
    
    @pass_context
    def _template_filter(self, context, context_data: Any, template_str: str) -> str:
        """渲染子模板的过滤器，支持合并父子上下文
        
        Args:
            context: Jinja2上下文对象(由pass_context自动传入)
            context_data: 渲染子模板所需的上下文数据
            template_str: 要渲染的模板字符串
        """
        if not isinstance(template_str, str):
            error_msg = f"警告: template过滤器的模板必须是字符串，收到: {type(template_str)}"
            log_text(DEVICE_AI_SERVER, error_msg)
            return str(context_data) # 保持对非字符串模板的原始数据返回
        
        try:
            # 检查输入数据类型
            if not isinstance(context_data, dict):
                error_msg = f"template过滤器要求左值必须是字典类型，收到: {type(context_data)}"
                log_exception(DEVICE_AI_SERVER, TypeError(error_msg), error_msg)
                if self.strict_mode:
                    raise TypeError(error_msg)
                else:
                    return str(context_data) # 保持对非字典数据的原始数据返回
            
            # 收集当前上下文中的变量（父上下文）
            parent_context = {}
            for name, value in context.items():
                # 跳过内部变量（以下划线开头）
                if not name.startswith('_'):
                    parent_context[name] = value
            
            # 合并上下文，子上下文优先覆盖
            merged_context = {**parent_context, **context_data}
            
            # !! 注意：过滤器内部的渲染调用需要特别处理 !!
            # 因为 render 方法已被移除，这里需要显式地获取模板并渲染
            # 这也突显了移除 render 方法后，过滤器这种内部调用的复杂性增加
            try:
                template_obj = self.get_template(template_str) # 获取编译模板
                result = template_obj.render(merged_context)   # 渲染
            except Exception as render_e:
                 error_msg_render = f"渲染子模板失败 (in _template_filter): {render_e}"
                 log_exception(DEVICE_AI_SERVER, render_e, error_msg_render)
                 if self.strict_mode:
                     raise
                 # 当子模板渲染失败时 (非严格模式), 返回空字符串
                 return ""

            return result
        except Exception as e: # 主要捕获合并上下文之前的错误
            error_msg = f"处理子模板过滤失败 (before render in _template_filter): {e}"
            log_exception(DEVICE_AI_SERVER, e, error_msg)
            if self.strict_mode:
                raise
            # 如果在准备上下文阶段就失败，还是返回原始 context_data 的字符串形式
            return str(context_data)
    
    @pass_context
    def _render_filter(self, context, template_str: Any, context_data: Any = None) -> str:
        """渲染模板的过滤器，参数顺序与template相反，支持合并父子上下文
        如果不传context_data，则仅作为finalize使用
        
        Args:
            context: Jinja2上下文对象(由pass_context自动传入)
            template_str: 要渲染的模板字符串或多语言字典
            context_data: 可选的渲染上下文数据，如果不传则只处理多语言
        """
        # 处理多语言模板
        if isinstance(template_str, dict):
            template_str = self._get_fallback_localized_template(template_str)

        # 如果没有context_data，直接返回处理后的值
        if context_data is None:
            return str(template_str)

        if not isinstance(template_str, str):
            error_msg = f"警告: render过滤器的模板必须是字符串或可解析的多语言字典，收到: {type(template_str)}"
            log_text(DEVICE_AI_SERVER, error_msg)
            if self.strict_mode:
                raise TypeError(error_msg)
            return str(template_str) # 保持对非字符串模板的原始模板字符串返回

        try:
            # 收集父上下文
            parent_context = {}
            for name, value in context.items():
                if not name.startswith('_'):
                    parent_context[name] = value

            # 准备子上下文
            if isinstance(context_data, dict):
                child_context = context_data
            else:
                child_context = {"value": context_data}

            # 合并上下文
            merged_context = {**parent_context, **child_context}

            # !! 显式两步渲染 !!
            try:
                template_obj = self.get_template(template_str)
                result = template_obj.render(merged_context)
            except Exception as render_e:
                 error_msg_render = f"渲染模板失败 (in _render_filter): {render_e}"
                 log_exception(DEVICE_AI_SERVER, render_e, error_msg_render)
                 if self.strict_mode:
                     raise
                 # 当子模板渲染失败时 (非严格模式), 返回空字符串
                 return ""

            return result
        except Exception as e: # 主要捕获合并上下文之前的错误
            error_msg = f"处理渲染过滤失败 (before render in _render_filter): {e}"
            log_exception(DEVICE_AI_SERVER, e, error_msg)
            if self.strict_mode:
                raise
            # 如果在准备上下文阶段就失败，还是返回原始 template_str
            return str(template_str)
    
    def get_template(self, template_string: str) -> Template:
        """
        第一步：编译或从缓存获取 Jinja2 Template 对象。

        Args:
            template_string: 模板字符串。

        Returns:
            编译后的 jinja2.Template 对象。

        Raises:
            jinja2.TemplateSyntaxError: 如果模板字符串有语法错误。
            Exception: 其他编译时可能发生的异常。
        """
        if template_string in self._template_cache:
            return self._template_cache[template_string]
        else:
            try:
                template_obj = self.env.from_string(template_string)
                self._template_cache[template_string] = template_obj
                return template_obj
            except Exception as e:
                log_exception(DEVICE_AI_SERVER, e, f"模板编译失败，模板内容: {template_string}")
                if self.strict_mode:
                    raise
                raise # 编译失败总是抛出


    def _custom_tojson_filter(self, value: Any) -> str:
        """自定义JSON序列化过滤器，保留中文字符
        
        对于无法序列化的对象：
        1. 如果是Undefined对象，打印其路径信息
        2. 其他类型则打印类型信息
        3. 统一返回空字符串
        """
        def default(obj):
            if isinstance(obj, (Undefined, ChainableUndefined)):
                # 获取Undefined对象的路径信息
                path = []
                cur = obj
                while hasattr(cur, "_undefined_name"):
                    name = getattr(cur, "_undefined_name", None)
                    if name is not None:
                        path.append(str(name))
                    cur = getattr(cur, "_undefined_obj", None)
                    if not isinstance(cur, (Undefined, ChainableUndefined)):
                        break
                path = ".".join(reversed(path)) if path else str(obj)
                log_text(DEVICE_AI_SERVER, f"[JSON序列化] 遇到未定义变量: {path}")
            else:
                log_text(DEVICE_AI_SERVER, f"[JSON序列化] 遇到无法序列化的对象: {type(obj).__name__}")
            return ""
            
        return json.dumps(value, ensure_ascii=False, default=default)

# --- 引擎实例缓存 --- 
@functools.lru_cache(maxsize=16)
def _get_cached_engine(language: str = "zh-CN", strict_mode: bool = False, chainable: bool = True) -> Jinja2TemplateEngine:
    """获取或创建缓存的 Jinja2TemplateEngine 实例"""
    return Jinja2TemplateEngine(language=language, strict_mode=strict_mode, chainable=chainable)

# --- 新增的对外接口 --- 
def templater(
    template_string: str,
    language: str = "zh-CN",
    strict_mode: bool = False,
    chainable: bool = True
) -> Template:

    engine = _get_cached_engine(language=language, strict_mode=strict_mode, chainable=chainable)
    return engine.get_template(template_string)



def get_json(path: str) -> Dict:
    """读取JSON文件，出错时返回空字典
    
    Args:
        path: JSON文件路径
        
    Returns:
        JSON内容字典
    """
    try:
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"加载文件失败: {path}", exc_info=True)
        return {} 
    
def prompt_format(template_str:str, context:Dict, language:str="zh-CN", strict_mode:bool=False, chainable:bool=True) -> str:
    try:
        template_obj = templater(template_str, language=language, strict_mode=strict_mode, chainable=chainable)
        return template_obj.render(context)
    except Exception as e:
        log_exception(DEVICE_AI_SERVER, e, f"模板格式化失败，模板内容: {template_str}，上下文: {context}")
        return template_str
    
def get_formatted_time(dt=None, language="zh-CN"):
    """使用jinja2模板获取格式化的时间字符串 (适配两步渲染)
    
    Args:
        dt: 要格式化的日期时间对象，默认为当前时间
        language: 语言代码，支持zh-CN、en、zh-HK
        
    Returns:
        格式化后的时间字符串
    """
    dt = dt or datetime.now()

    templates = {
        "zh-CN": "{{ dt.strftime('%Y年%m月%d日 %H:%M') }} {{ ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'][dt.weekday()] }}",
        "en": "{{ dt.strftime('%Y-%m-%d %H:%M') }} {{ ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][dt.weekday()] }}",
        "zh-HK": "{{ dt.strftime('%Y年%m月%d日 %H:%M') }} {{ ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'][dt.weekday()] }}"
    }

    selected_template = templates.get(language, templates.get('zh-CN', next(iter(templates.values()))))
    context = {"dt": dt}

    try:
        # 使用新的封装函数获取编译模板
        template_obj = templater(selected_template, language=language)
        
        # 第二步：渲染模板
        return template_obj.render(context)
        
    except Exception as e:
        # 捕获并记录编译或渲染错误
        log_exception(DEVICE_AI_SERVER, e, f"格式化时间失败 (language={language})")
        return "" 
"""store_utils.py - 存储工具模块"""

from types import SimpleNamespace
from typing import Any, Dict, Optional
from app.base.store import Store

# 命名空间常量
NAMESPACE = SimpleNamespace(
    SESSION="session",
    ORGANIZATION="org",
)

# 内置关键数据的路径
BACKGROUND_INFO="backgroundInfo"
UICONFIG="uiConfig"
RESPONSE="response"
ORG_CONFIG="orgConfig"

class PathBuilder:
    """路径构建工具"""
    
    def __init__(self, namespace: str, item_id: str = None):
        self.namespace = namespace
        self.item_id = item_id
    
    def for_item(self, item_id: str) -> "PathBuilder":
        """为特定项目创建路径构建器"""
        return PathBuilder(self.namespace, item_id)
    
    def path(self, *segments) -> str:
        """构建完整路径"""
        if not self.item_id:
            raise ValueError("Item ID is required. Use for_item() first.")
            
        path_parts = [part.strip("/") for part in segments if part]
        rel_path = "/".join(path_parts)
        
        return f"/{self.namespace}/{self.item_id}/{rel_path}" if rel_path else f"/{self.namespace}/{self.item_id}"

def create_accessor(namespace: str, item_id: str):
    """创建数据访问器"""
    builder = PathBuilder(namespace, item_id)
    
    class Accessor:
        @staticmethod
        async def get(path: str = "", default: Any = None):
            full_path = builder.path(path) if path else builder.path()
            return await Store.get(full_path, default)
        
        @staticmethod
        async def set(path_or_value: str, value: Any = None):
            # 支持两种调用方式:
            # accessor.set("path", value) 或 accessor.set(value) 设置根路径
            if value is None:
                await Store.set(builder.path(), path_or_value)
            else:
                await Store.set(builder.path(path_or_value), value)
        
        @staticmethod
        async def delete(path: str = ""):
            full_path = builder.path(path) if path else builder.path()
            await Store.delete(full_path)
    
    return Accessor

# 预定义访问器工厂
def session(session_id: str):
    return create_accessor(NAMESPACE.SESSION, session_id)

def organization(org_id: str):
    return create_accessor(NAMESPACE.ORGANIZATION, org_id)


# 初始化函数
async def initialize_stores(session_max_size=10000, org_max_size=1000):
    """初始化所有存储实例"""
    
    await Store.configure(NAMESPACE.SESSION, max_size=session_max_size)
    await Store.configure(NAMESPACE.ORGANIZATION, max_size=org_max_size)

# 在文件末尾添加上下文组合工具函数

async def get_ctx_with_bg_info(session_id: str, background_info: dict = None) -> dict:
    """
    获取session上下文并与背景信息组合
    
    这是一个工具函数，解决并发环境下backgroundInfo的隔离问题
    
    Args:
        session_id: 会话ID
        background_info: 当前请求的背景信息，可选
        
    Returns:
        dict: 组合后的上下文字典
    """
    ctx = await session(session_id).get()
    if background_info:
        # 创建新字典，避免修改原始ctx
        combined_ctx = dict(ctx)
        combined_ctx[BACKGROUND_INFO] = background_info
        return combined_ctx
    return ctx
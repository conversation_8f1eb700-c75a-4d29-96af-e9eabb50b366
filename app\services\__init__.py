from abc import ABC, abstractmethod
from typing import AsyncGenerator
from app.utils.net_utils import Response
from app.config import (
    MYSQL_HOST,
    MYSQL_PORT,
    MYSQL_USER,
    MYSQL_PASSWORD,
    MYSQL_DATABASE,
)

# 常量定义
KEY_INTENTION = "intentionConfig"
KEY_PROMPT = "promptConfig"
KEY_UI_CONFIG = "uiConfig"
KEY_FORMATTERS = "formatters"


KEY_INTENTION_DETECT = "intentDetect"
KEY_ALL_BUSINESS = "allBusiness"
KEY_KNOWLEDGE_RAG = "knowledge"
KEY_KNOWLEDGE_QA = "knowledgeQA"
KEY_CHAT = "chat"

KEY_PROMPT_ID = "promptId"
KEY_TEMPLATE = "template"
KEY_TOOLS = "tools"
KEY_BIZ_DATA = "bizData"

KEY = "key"
KEY_DATA = "data"

KEY_INIENT_BIZ = "business"
KEY_INIENT_KNOWLEDGE = "knowledge"
KEY_INIENT_CHAT = "chat"
KEY_INTENTION_LIST = "intentionList"

KEY_QA_PAIR_ID = "qna_pair_id"

DEVICE_BIZ_SERVER = "biz_server"
DEVICE_AI_SERVER = "ai_server"
DEVICE_DEFAULT = "default_device"

SOURCE_UNSPECIFIED = 0  # 未知
SOURCE_BIZ_ANSWER = 1  # LLM最终回答
SOURCE_STATUS = 2  # 状态回答（比如正在查询/处理xxx..）
SOURCE_QA_VECTOR = 3  # 问答-向量搜索
SOURCE_QA_RAG = 4  # 问答-RAG搜索
SOURCE_QA_FALLBACK = 5  # 问答-兜底
SOURCE_LLM_TEXT = 6  # LLM闲聊纯文本
SOURCE_REJECT = 7  # 拒绝服务回答
SOURCE_QA_VECTOR_REWRITE = 8  # 问答-用重写query向量搜索
SOURCE_QA_VECTOR_LLM = 9  # 问答-向量搜索-LLM回答
SOURCE_BUTTON_CLICK = 10  # 按钮点击
SOURCE_DIRECT_ANSWER = 11  # 直接回答
SOURCE_UNCLEAR_INTENT = 12  # 不明确意图

EXTRA_CHARACTER_PROMPT = "character_prompt"

LLM_BIZ_CONFIG = "biz"
LLM_RAG_CONFIG = "rag"
LLM_CHAT_CONFIG = "chat"
LLM_QUESTION_GENERATOR_CONFIG = "question_generator"
LLM_INTENTION_CONFIG = "intention"

def get_source_name(source_code):
    if source_code == SOURCE_BIZ_ANSWER:
        return "业务回答"
    elif source_code == SOURCE_STATUS:
        return "状态回答"
    elif source_code == SOURCE_QA_VECTOR:
        return "问答-向量搜索"
    elif source_code == SOURCE_QA_RAG:
        return "问答-RAG搜索"
    elif source_code == SOURCE_QA_FALLBACK:
        return "问答-兜底"
    elif source_code == SOURCE_QA_VECTOR_REWRITE:
        return "问答-用重写query向量搜索"
    elif source_code == SOURCE_QA_VECTOR_LLM:
        return "问答-向量搜索-LLM回答"
    elif source_code == SOURCE_LLM_TEXT:
        return "LLM闲聊纯文本"
    elif source_code == SOURCE_REJECT:
        return "拒绝服务回答"
    elif source_code == SOURCE_BUTTON_CLICK:
        return "按钮点击"
    elif source_code == SOURCE_DIRECT_ANSWER:
        return "直接回答"
    elif source_code == SOURCE_UNCLEAR_INTENT:
        return "不明确意图"
    else:
        return "未知"


class IntentionHandler(ABC):
    @abstractmethod
    async def handle(
        self, messages, **kwargs
    ) -> AsyncGenerator[Response, None]:
        pass

    def update_llm_service_config(self, llm_service_config: dict):
        pass

def initialize_database():
    from app.services.mysql_service import initialize_database as _initialize_database
    return _initialize_database(
        MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE, MYSQL_PORT
    )


def connect_milvus():
    from app.services.milvus_service import connect_milvus as _connect_milvus
    _connect_milvus()

def initialize_stores():
    """初始化所有存储实例"""
    import asyncio
    from app.base.store_utils import initialize_stores as _initialize_stores
    
    # 使用事件循环运行异步初始化函数
    loop = asyncio.get_event_loop()
    if loop.is_running():
        # 如果事件循环已经在运行，创建一个任务
        asyncio.create_task(_initialize_stores())
    else:
        # 否则直接运行
        loop.run_until_complete(_initialize_stores())
    
    print("所有存储实例已初始化")

# 在文件末尾调用初始化函数
db = initialize_database()
connect_milvus()
initialize_stores()


from contextlib import suppress
from gettext import gettext
from fastapi_babel.core import _context_var

def setup_translation_context():
    """只在需要时设置翻译上下文"""
    # 检查是否已经有上下文
    with suppress(LookupError):
        _context_var.get()
        return  # 如果已有上下文，直接返回
        
    # 没有上下文时才设置
    _context_var.set(gettext)

# 在模块导入时就执行设置
setup_translation_context()
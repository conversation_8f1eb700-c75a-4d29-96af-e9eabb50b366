import requests
from app.config import IS_DEBUG

LARK_WEBHOOK_URL_PRODUCT_SERVER = "https://open.larksuite.com/open-apis/bot/v2/hook/e02402ae-c77c-442c-8d1f-989bd30f2df2"
LARK_WEBHOOK_URL_TEST_SERVER = "https://open.larksuite.com/open-apis/bot/v2/hook/5dccd9db-717f-4797-a1f8-d09fa481363d"

# 根据当前环境选择合适的webhook地址
LARK_WEBHOOK_URL = (
    LARK_WEBHOOK_URL_TEST_SERVER if IS_DEBUG else LARK_WEBHOOK_URL_PRODUCT_SERVER
)


def send_lark_message(message, webhook_url=None):
    """
    发送消息到飞书机器人

    Args:
        message: 要发送的文本消息
        webhook_url: 可选的webhook地址，如果不提供则使用默认配置

    Returns:
        dict: 飞书API的响应结果
    """
    headers = {"Content-Type": "application/json"}
    webhook = webhook_url or LARK_WEBHOOK_URL
    payload = {"msg_type": "text", "content": {"text": message}}

    response = requests.post(webhook, json=payload, headers=headers)
    return response.json()


# 使用示例
if __name__ == "__main__":
    # 使用默认webhook地址发送消息
    result = send_lark_message("测试消息")
    print(result)

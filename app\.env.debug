#####################################
# 业务服务器配置 (必须选择其中一个)
#####################################

# 选项1: 北京正式业务服务器
BIZ_SERVER_URL=http://************/prod-api/business/data/ai

# 选项2: 新加坡正式业务服务器
# BIZ_SERVER_URL=http://18.141.68.98/prod-api/business/data/ai

# 选项3: 开发环境局域网业务服务器
# BIZ_SERVER_URL=http://192.168.2.59/dev-api/business/data/ai

#####################################
# OpenAI 配置 (必须选择其中一个)
#####################################

# 选项1: OpenAI 官方接口
# OPENAI_BASE_URL=https://api.openai.com/v1

# 选项2: 新加坡直连
# OPENAI_BASE_URL=http://18.141.68.98:5001

# 选项3: 北京中转
# OPENAI_BASE_URL=http://************/prod-api/openai/v1/

# NGINX反向代理
OPENAI_BASE_URL=http://openai-ng.withufuture.com/v1

#####################################
# Azure 配置
#####################################
AZURE_BASE_URL=https://azureopenaiapi01.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview
AZURE_API_VERSION=2025-01-01-preview

#####################################
# 智谱AI 配置
#####################################
ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4/

#####################################
# Deepseek 配置 (必须选择其中一个)
#####################################

# 选项1: Deepseek 官方接口
# DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# 选项2: Deepseek 阿里云转接接口
DEEPSEEK_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

#####################################
# 火山引擎配置
#####################################
VOLCANO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3

#####################################
# 阿里Qwen配置
#####################################
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

#####################################
# 数据库配置 (必须选择其中一个组合)
#####################################

# 选项1: 本地4080测试数据库
# MYSQL_HOST=***********
# MYSQL_PORT=3306
# MYSQL_DATABASE=ai_chat
# MILVUS_HOST=***********
# MILVUS_PORT=19530

# 选项2: P40转接北京正式服
# MYSQL_HOST=************
# MYSQL_PORT=6306
# MYSQL_DATABASE=ai_chat
# MILVUS_HOST=************
# MILVUS_PORT=29530

# 选项3: P40转接新加坡正式服
# MYSQL_HOST=************
# MYSQL_PORT=7306
# MYSQL_DATABASE=ai_chat
# MILVUS_HOST=************
# MILVUS_PORT=39530

# 选项4: 北京测试服务器
# MYSQL_HOST=***************
# MYSQL_PORT=3306
# MYSQL_DATABASE=ai_chat
# MILVUS_HOST=***************
# MILVUS_PORT=19530 

# 选项5: 新加坡测试服务器
# MYSQL_HOST=*************
# MYSQL_PORT=3306
# MYSQL_DATABASE=ai_chat
# MILVUS_HOST=*************
# MILVUS_PORT=19530

# 选项6: 本地环境
# MYSQL_HOST=localhost
# MYSQL_PORT=3306
# MYSQL_DATABASE=ai_chat
# MILVUS_HOST=localhost
# MILVUS_PORT=19530

# 选项7: 公司测试数据服务器(P40)
# MYSQL_HOST=************
# MYSQL_PORT=3307
# MYSQL_DATABASE=ai_chat
# MILVUS_HOST=************
# MILVUS_PORT=19530

# 选项8: 北京测试数据服务器
MYSQL_HOST=************
MYSQL_PORT=3317
MYSQL_DATABASE=ai_chat
MILVUS_HOST=************
MILVUS_PORT=49530

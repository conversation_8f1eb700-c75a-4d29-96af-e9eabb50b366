﻿# 这里是pip install -r requirements.txt
langchain
python-docx
python-pptx
PyMuPDF
requests
# faiss-cpu
# FlagEmbedding
# transformers
openai
# peft
fastapi
fastapi-babel==0.0.9
aiohttp
pymilvus
uvicorn
pytest

pymysql
peewee
peewee-jsonfield
openpyxl 
py-spy

# 中文分词
# jieba

# GPT官方分词，用于计算token
tiktoken

# language detect module
langdetect
opencc
lingua-language-detector

# 计算token数量
tiktoken

# excel读取的库
openpyxl

# 连接高版本MySQL数据库使用的
cryptography

# Jina reranker安装的
# einops

cachetools
jinja2
cython

# textract是用来读取word文档和ppt来做切片的，不一样要基于这个使用
# 安装transformers库的时候会自动安装cpu版本的torch，先卸载再安装gpu版本的torch
# pip uninstall torch torchvision torchaudio
# pip install torch==2.3.1 torchvision==0.18.1 torchaudio==2.3.1 --index-url https://download.pytorch.org/whl/cu121
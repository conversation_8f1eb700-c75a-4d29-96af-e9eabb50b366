{"formatters": {"Dir-button": "{% set data = {'message': '好的', 'extra': {'pageId': backgroundInfo.page, 'Action': 'buttonClick', 'data': {'buttonId': response.extra}}} %}{{ data|tojson }}", "sensitive": "{{ {'message': orgConfig.uiConfig.directReply.sensitive.content|render}|tojson }}", "irrelevant": "{{ {'message': orgConfig.uiConfig.directReply.irrelevant.content|render}|tojson }}", "Dir-unclear": "{{ {'message': response.message|default('')}|tojson }}"}, "pages": {"1.2-Greeting-Dialogue-Page": {"name": {"zh-CN": "打招呼对话页", "en": "Greeting Dialogue Page"}, "description": {"zh-CN": "在这个页面，自助机将会引导用户登录也可以回答用户的疑问以及解决用户的各种问题。假如你发现历史聊天记录里面assistant曾回复\"签到需要先登录，请问您想使用什么方式登录\"类似的话，说白了就是assistant曾经反问过用户想要用什么方式登录。这种情况下，用户说身份证，身份证登录，我们都不应该选择button而是选择login。", "en": "On this page, the self-service machine will guide users to log in and answer their questions and solve their various problems. If you find that the assistant has replied in the historical chat record that \"You need to log in first to sign in. How do you want to log in?\", it means that the assistant has asked the user how he wants to log in. In this case, if the user says ID card, ID card login, we should not select button but login."}, "buttonList": ["medical_insurance_face_recognition_login", "medical_insurance_card", "electronic_medical_insurance_login", "id_card_login", "medical_visit_card_login", "passport_scan", "electronic_health_card", "hong_kong_and_macau_resident_permit", "medical_visit_barcode", "simplified_chinese", "english"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding", "login"]}, "2.1-menu-page": {"name": {"zh-CN": "菜单页", "en": "<PERSON><PERSON>"}, "description": {"zh-CN": "在这个页面里面，自助机将会引导用户挂号也可以回答用户的疑问以及解决用户的各种问题。请注意在这个页面里如果用户表达了想要缴费，打印报告，打印发票和取号的想法。请直接选择对应的按钮。如果是建档，请选择record。如果用户说签到视同于取号。如果用户说我要挂号，或者挂号，请直接选择对应的按钮意图。除非用户询问要挂什么科室才会选择导诊。", "en": "On this page, the self-service machine will guide users to register, answer their questions, and resolve various issues. Please note that on this page, if the user expresses the desire to pay, print report, print invoice and get number, please select the corresponding button directly. If it is for creating a file, please select record. If the user states that check-in is equivalent to taking a number. If the user says \"I want to register\" or \"register,\" directly select the corresponding button intent. Only choose triage if the user asks which department to register for."}, "buttonList": ["register", "payment", "inpatient_recharge", "medical_appointment", "invoice_printing", "report_printing", "self_service_filing", "general_inquiry", "satisfaction_evaluation", "hospital_introduction", "to_do_list", "back", "exit", "ignore", "get_number"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding", "record"]}, "3.1-Select-Department-Page": {"name": {"zh-CN": "选择科室页", "en": "Select Department Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户选择挂号的科室", "en": "On this page, the self-service machine will guide users to select the department for registration"}, "buttonList": ["all", "page_up", "page_down", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding"]}, "3.2-Select-Time-Page": {"name": {"zh-CN": "选择时间页", "en": "Select Time Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户选择挂号的时间和医生。如果用户只说了周一，周二这种的指令，我们可以当做是点击了按钮。但是如果用户说了具体的医生名字和具体的日期，请走registration意图，因为对具体日期和医生的逻辑处理，我们需要在那里完成。", "en": "On this page, the self-service machine guides users to select appointment times and doctors. If the user only provides instructions like 'Monday' or 'Tuesday,' we can treat it as if they clicked a button. However, if the user specifies a particular doctor's name and a specific date, please proceed with the 'registration' action. This is because the logic processing for specific dates and doctors needs to be completed within that action."}, "buttonList": ["friday", "saturday", "sunday", "monday", "tuesday", "wednesday", "thursday", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "registration", "way-finding"], "business": {"en": "The user has selected the {{ department }} department for an appointment on {{ date }} in the Time Selection Page.", "zh-CN": "用户在选择时间页面已选择在{{ date }}前往{{ department }}就诊。", "zh-HK": "用戶在選擇時間頁面已選擇在{{ date }}前往{{ department }}就診。"}}, "3.3-Confirm-Registration-Information-Page": {"name": {"zh-CN": "确认挂号信息页", "en": "Confirm Registration Information Page"}, "description": {"zh-CN": "在这个页面里面，自助机想引导用户确认自己的挂号信息", "en": "On this page, the self-service machine intends to guide users to confirm their registration information"}, "buttonList": ["back", "exit"], "optionalActions": ["knowledge", "chat", "query", "registration", "way-finding"]}, "3.4-Registration-Process-Payment-Method-Selection-Page": {"name": {"zh-CN": "挂号流程支付方式选择页", "en": "Registration Process Payment Method Selection Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户选择支付方式", "en": "On this page, the self-service machine will guide users to select a payment method"}, "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "way-finding"]}, "3.5-Scan-Code-Page": {"name": {"zh-CN": "扫码页", "en": "Scan Code Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户扫描二维码支付", "en": "On this page, the self-service machine will guide users to scan a QR code for payment"}, "buttonList": ["cancel"], "optionalActions": ["knowledge", "chat", "query", "way-finding"]}, "3.6-Registration-Success-Page": {"name": {"zh-CN": "挂号成功页", "en": "Registration Success Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户取走小票，并在用户拿走小票后给用户导航", "en": "On this page, the self-service machine will guide the user to take the receipt, and after the user takes the receipt, it will provide navigation for the user"}, "buttonList": ["confirm", "location_navigation"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding"]}, "3.7-Navigation-Page": {"name": {"zh-CN": "导航页", "en": "Navigation Page"}, "description": {"zh-CN": "在这个页面里面，自助机会帮用户导航", "en": "On this page, the self-service machine will help the user navigate"}, "buttonList": ["exit"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding"]}, "3.9-Select-Time-Period-Page": {"name": {"zh-CN": "选时间段页", "en": "Select Time Period Page"}, "description": {"zh-CN": "在这个页面里面，用户可以选择挂号的具体时间段。如果用户要选择具体的时间，或者修改挂号信息，请走registration。", "en": "In this page, users can select the specific time period for registration. If users want to select a specific time or modify registration information, please go to registration."}, "buttonList": ["cancel", "confirm"], "optionalActions": ["knowledge", "chat", "query", "registration"], "business": {"en": "The user has selected Dr. {{ doctor }} at the {{ department }} department for an appointment on {{ date }} in the Time Period Selection Page.", "zh-CN": "用户在选择时间段页面已选择在{{ date }}由{{ doctor }}医生在{{ department }}进行诊疗。", "zh-HK": "用戶在選擇時間段頁面已選擇在{{ date }}由{{ doctor }}醫生在{{ department }}進行診療。"}}, "3.10-Confirm-And-Select-Payment-Method-Page": {"name": {"zh-CN": "确认并选支付方式页", "en": "Confirm And Select Payment Method Page"}, "description": {"zh-CN": "在这个页面里面，用户可以确认挂号详细信息，并选择支付方式，在这个页面里面对于支付的方式，我们统一使用Dir-button的逻辑。如果你觉得用户是在表达想用某种方式完成支付，请直接选择对应的按钮。如果用户想要修改挂号信息，例如想要更换医生，科室，时间，请走registration。", "en": "On this page, users can review their appointment details and select a payment method. For payment options, we consistently follow the Dir-button logic. If you believe the user intends to pay using a specific method, directly select the corresponding button. If the user wants to modify appointment details, such as changing the doctor, department, or time, please proceed through the registration process."}, "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "registration"], "business": {"en": "The user has booked an appointment with Dr. {{ doctor }} at the {{ department }} department on {{ date }}, {{ time_slot }} in the Payment Confirmation Page.", "zh-CN": "用户在确认支付页面已预约{{ date }} {{ time_slot }}在{{ department }}由{{ doctor }}医生进行诊疗。", "zh-HK": "用戶在確認支付頁面已預約{{ date }} {{ time_slot }}在{{ department }}由{{ doctor }}醫生進行診療。"}}, "2.2-Pending-Page": {"name": {"zh-CN": "待办页", "en": "Pending Page"}, "description": {"zh-CN": "在这个页面里面，自助机会提醒用户有待缴费或待打印或待取号的项目。请注意在这个页面里如果用户表达了想要缴费，打印报告，打印发票和取号的想法。请直接选择对应的按钮。", "en": "On this page, the self-service machine will remind users that there are items to be paid, printed or numbered. Please note that on this page, if the user expresses the desire to pay, print report, print invoice and get number, please select the corresponding button directly."}, "buttonList": ["ignore", "invoice_printing", "report_printing", "get_number"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "4.1-Payment-Selection-Page": {"name": {"zh-CN": "缴费选择页", "en": "Payment Selection Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查看待缴费的项目列表。如果用户说全部缴费，或者缴费都选择对应的按钮。如果用户说了", "en": "On this page, the user can view the list of pending payment items. If the user says pay all or make payments, select the corresponding button."}, "buttonList": ["back", "exit", "previous_page", "next_page", "pay_all"], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}, "4.2-Prescription-Details-View-Page": {"name": {"zh-CN": "处方详情查看页", "en": "Prescription Details View Page"}, "description": {"zh-CN": "在这个页面里面，用户会查看自己所有的待缴费项目详情", "en": "On this page, the user will view the details of all their pending payment items"}, "buttonList": ["close", "previous_page", "next_page"], "optionalActions": ["knowledge", "chat", "query"]}, "4.3-Payment-Details-Confirmation-Page": {"name": {"zh-CN": "缴费详情确认页", "en": "Payment Details Confirmation Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户确认自己的缴费信息", "en": "On this page, the self-service machine will guide the user to confirm their payment information"}, "buttonList": ["back", "exit"], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}, "4.4-Payment-Process-Payment-Method-Selection-Page": {"name": {"zh-CN": "缴费流程支付方式选择页", "en": "Payment Process Payment Method Selection Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户选择一种支付方式", "en": "On this page, the self-service machine will guide the user to select a payment method"}, "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}, "4.5-Medical-Insurance-Method-Guidance-Card-Insertion-Page": {"name": {"zh-CN": "医保方式指引插卡页", "en": "Medical Insurance Method Guidance Card Insertion Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户插入医保卡", "en": "On this page, the self-service machine will guide the user to insert their medical insurance card"}, "buttonList": ["back"], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}, "4.6-Medical-Insurance-Method-Guidance-Card-Insertion-Page": {"name": {"zh-CN": "微信和支付宝方式扫码页", "en": "Medical Insurance Method Guidance Card Insertion Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户扫码", "en": "On this page, the self-service machine will guide the user to insert their medical insurance card"}, "buttonList": ["cancel"], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}, "4.7-Payment-Success-Page": {"name": {"zh-CN": "缴费成功页", "en": "Payment Success Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户取走小票，并在用户拿走小票后给用户导航", "en": "On this page, the self-service machine will guide the user to take the receipt, and after the user takes the receipt, it will provide navigation for the user"}, "buttonList": ["confirm", "location_navigation"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "2.3-Print-Pending-Page": {"name": {"zh-CN": "打印待办页", "en": "Print Pending Page"}, "description": {"zh-CN": "在这个页面里面，自助机会提醒用户有待打印的事项", "en": "On this page, the self-service machine will remind the user of items pending printing"}, "buttonList": ["ignore", "invoice_printing", "report_printing"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "5.1-Print-List-Page": {"name": {"zh-CN": "报告打印列表页", "en": "Print List Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查看待打印的项目列表。用户若说打印，请选择对应的按钮。", "en": "On this page, users can view a list of items pending printing. If the user says print, please select the corresponding button."}, "buttonList": ["back", "exit", "previous_page", "next_page", "printing"], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}, "5.2-Report-Details-View-Page": {"name": {"zh-CN": "报告详情查看页", "en": "Report Details View Page"}, "description": {"zh-CN": "在这个页面里面，用户会查看报告详情", "en": "On this page, users can view the details of the report"}, "buttonList": ["close"], "optionalActions": ["knowledge", "chat", "query"]}, "5.3-Print-Report-Waiting-Page": {"name": {"zh-CN": "打印报告等待页", "en": "Print Report Waiting Page"}, "description": {"zh-CN": "在这个页面里面，用户正在等待自助机打印，自助机会显示打印进度", "en": "On this page, the user is waiting for the self-service machine to print, and the self-service machine will display the printing progress"}, "buttonList": [], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}, "5.4-Report-Printing-Success-Page": {"name": {"zh-CN": "报告打印成功页", "en": "Report Printing Success Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户取走报告", "en": "On this page, the self-service machine will guide the user to take the report."}, "buttonList": ["complete"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "5.5-Invoice-Printing-List-Page": {"name": {"zh-CN": "发票打印列表页", "en": "Invoice Printing List Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查看待打印的项目列表", "en": "On this page, users can view a list of items pending printing"}, "buttonList": ["back", "exit", "previous_page", "next_page", "printing", "within_one_week", "within_one_month", "within_six_months", "within_one_year"], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}, "5.6-Invoice-Printing-Waiting-Page": {"name": {"zh-CN": "发票打印等待页", "en": "Invoice Printing Waiting Page"}, "description": {"zh-CN": "在这个页面里面，用户正在等待自助机打印，自助机会显示打印进度", "en": "On this page, the user is waiting for the self-service machine to print, and the self-service machine will display the printing progress"}, "buttonList": [], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}, "5.7-Invoice-Printing-Success-Page": {"name": {"zh-CN": "发票打印成功页", "en": "Invoice Printing Success Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户取走发票", "en": "On this page, the self-service machine will guide the user to take the invoice"}, "buttonList": ["complete"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.1-Invoice-Printing-Success-Page": {"name": {"zh-CN": "药品查询页", "en": "Invoice Printing Success Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查询药品价格", "en": "On this page, the self-service machine will guide the user to take the invoice"}, "buttonList": ["complete"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.2-Comprehensive-Inquiry-Page": {"name": {"zh-CN": "综合查询页", "en": "Comprehensive Inquiry Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查询药品价格、项目价格、消费清单等一些信息。", "en": "On this page, users can check information such as drug prices, item prices, consumption lists, and more."}, "buttonList": ["drug_price", "item_price", "consumption_list", "inpatient_cost_list", "outpatient_cost_list", "change_phone_number", "change_password", "back"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.2.1-Drug-Price-Inquiry-Page": {"name": {"zh-CN": "药品价格查询页", "en": "Drug Price Inquiry Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查询药品价格的详细信息。", "en": "On this page, users can inquire about detailed information on drug prices."}, "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.2.2-Item-Price-Inquiry-Page": {"name": {"zh-CN": "项目价格查询页", "en": "Item Price Inquiry Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查询项目价格的详细信息。", "en": "On this page, users can inquire about detailed information on item prices."}, "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.2.3-Consumption-List-Inquiry-Page": {"name": {"zh-CN": "消费清单查询页", "en": "Consumption List Inquiry Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查看消费清单。", "en": "On this page, users can view their consumption list."}, "buttonList": ["query", "previous_page", "next_page", "back", "exit", "wechat_save_to_phone"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.2.4-Inpatient-Expense-List-Page": {"name": {"zh-CN": "住院费用清单页", "en": "Inpatient Expense List Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查看住院费用清单。", "en": "On this page, users can view the inpatient expense list."}, "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.2.5-Outpatient-Expense-List-Page": {"name": {"zh-CN": "门诊费用清单页", "en": "Outpatient Expense List Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查看门诊费用清单。", "en": "On this page, users can view the outpatient expense list."}, "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.2.6-Modify-Phone-Number-Page": {"name": {"zh-CN": "修改手机号页", "en": "Modify Phone Number Page"}, "description": {"zh-CN": "在这个页面里面，用户可以修改手机号。", "en": "On this page, users can modify their phone number."}, "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "change-phone"]}, "7.2.7-Modify-Password-Page": {"name": {"zh-CN": "修改密码页", "en": "Modify Password Page"}, "description": {"zh-CN": "在这个页面里面，用户可以修改密码。", "en": "On this page, users can modify their password."}, "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "change-password"]}, "7.3-Hospital-Introduction-Page": {"name": {"zh-CN": "医院介绍页", "en": "Hospital Introduction Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查询医院简介、科室简介、医生简介的信息。", "en": "On this page, users can check information about the hospital introduction, department introduction, and doctor introduction."}, "buttonList": ["hospital_introduction", "department_introduction", "doctor_introduction", "back"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.3.1-Hospital-Detailed-Introduction-Page": {"name": {"zh-CN": "医院具体简介页", "en": "Hospital Detailed Introduction Page"}, "description": {"zh-CN": "在这个页面里面，用户可以查看医院简介的详细信息。", "en": "On this page, users can view detailed information about the hospital introduction."}, "buttonList": ["exit", "back"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.3.2-Department-Introduction-Page": {"name": {"zh-CN": "科室介绍页", "en": "Department Introduction Page"}, "description": {"zh-CN": "在这个页面里面，用户可以选择要查看的科室。", "en": "On this page, users can select the department they want to view."}, "buttonList": ["exit", "back", "respiratory_medicine_clinic", "cardiology", "vascular_surgery", "thyroid_surgery_clinic", "shoulder_joint_clinic", "previous_page", "next_page"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "7.3.3-Doctor-Introduction-Page": {"name": {"zh-CN": "医生介绍页", "en": "Doctor Introduction Page"}, "description": {"zh-CN": "在这个页面里面，用户可以选择要查看的医生。", "en": "On this page, users can select the doctor they want to view."}, "buttonList": ["zhou_junqing", "peng_fang", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"]}, "2.4-Adult-Registration-Page": {"name": {"zh-CN": "成人建档页", "en": "Adult Registration Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户输入手机号建档或补卡。", "en": "On this page, the self-service machine will guide users to enter their mobile phone number to create a file or reissue a card."}, "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "business": {"en": "You are currently on the Adult Registration Page. Voice operation is only supported for phone number input.", "zh-CN": "用户位于成人建档页，仅支持使用语言输入电话号码。", "zh-HK": "用戶位於成人建檔頁，僅支持使用語言輸入電話號碼。"}}, "6.1-Adult-Registration-Confirmation-Page": {"name": {"zh-CN": "成人建档确认页", "en": "Adult Registration Confirmation Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户确认建档信息。", "en": "On this page, the self-service machine will guide the user to confirm their registration information."}, "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"]}, "6.2-Adult-Registration-Confirmation-Page": {"name": {"zh-CN": "建档成功页", "en": "Adult Registration Confirmation Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户取走就诊卡", "en": "On this page, the self-service machine will guide the user to confirm their registration information."}, "buttonList": ["complete"], "optionalActions": ["knowledge", "chat", "query", "record"]}, "6.3-Child-Registration-Page": {"name": {"zh-CN": "儿童建档页", "en": "Child Registration Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户输入儿童信息进行儿童建档。", "en": "On this page, the self-service machine will guide the user to enter the child's information for child registration."}, "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "business": {"en": "You are currently on the Child Registration Page. Voice operation is supported for name, date, gender, and phone number.", "zh-CN": "用户位于儿童建档页，支持对姓名，日期，性别和电话的语音操作。", "zh-HK": "用戶位於兒童建檔頁，支持對姓名，日期，性別和電話的語音操作。"}}, "6.4-Child-Registration-Confirmation-Page": {"name": {"zh-CN": "儿童建档确认信息页", "en": "Child Registration Confirmation Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户确认儿童建档信息。", "en": "On this page, the self-service machine will guide the user to confirm the child's registration information."}, "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"]}, "8.1-Ticket-Collection-Page": {"name": {"zh-CN": "取号页", "en": "Ticket Collection Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户取号。", "en": "On this page, the self-service machine will guide the user to collect a ticket."}, "buttonList": ["previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "chat", "query"]}, "8.2-Ticket-Collection-Success-Page": {"name": {"zh-CN": "取号成功页", "en": "Ticket Collection Success Page"}, "description": {"zh-CN": "在这个页面里面，用户取号成功，引导用户拿走凭条。", "en": "On this page, the user has successfully collected a ticket, guiding the user to take the receipt."}, "buttonList": ["complete", "location_navigation"], "optionalActions": ["knowledge", "way-finding", "chat", "query"]}}, "buttons": {"exit": {"name": {"zh-CN": "退出", "en": "Exit"}, "description": {"zh-CN": "用户表达了退出", "en": "用户表达了退出"}}, "back": {"name": {"zh-CN": "返回", "en": "Back"}, "description": {"zh-CN": "用户说了返回", "en": "用户说了返回"}}, "medical_insurance": {"name": {"zh-CN": "医保", "en": "Medical Insurance"}, "description": {"zh-CN": "", "en": ""}}, "medical_insurance_face_recognition_login": {"name": {"zh-CN": "医保刷脸登录", "en": "Medical insurance face recognition login"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "medical_insurance_card": {"name": {"zh-CN": "医保卡", "en": "Medical insurance card"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "electronic_medical_insurance_login": {"name": {"zh-CN": "电子医保登录", "en": "Electronic medical insurance login"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "id_card_login": {"name": {"zh-CN": "身份证登录", "en": "ID card login"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "medical_visit_card_login": {"name": {"zh-CN": "就诊卡登录", "en": "Medical visit card login"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "passport_scan": {"name": {"zh-CN": "护照扫描", "en": "Passport scan"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "electronic_health_card": {"name": {"zh-CN": "电子健康卡", "en": "Electronic health card"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "hong_kong_and_macau_resident_permit": {"name": {"zh-CN": "港澳居民居住证", "en": "Hong Kong and Macau resident permit"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "medical_visit_barcode": {"name": {"zh-CN": "就诊条码", "en": "Medical visit barcode"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "simplified_chinese": {"name": {"zh-CN": "简体中文", "en": "简体中文"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "english": {"name": {"zh-CN": "English", "en": "English"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "price_inquiry": {"name": {"zh-CN": "物价查询", "en": "Price Inquiry"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "general_inquiry": {"name": {"zh-CN": "综合查询", "en": "General Inquiry"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "department_inquiry": {"name": {"zh-CN": "科室查询", "en": "Department Inquiry"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "register": {"name": {"zh-CN": "挂号", "en": "Register"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "payment": {"name": {"zh-CN": "缴费", "en": "Payment"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "inpatient_recharge": {"name": {"zh-CN": "住院充值", "en": "Inpatient Recharge"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "medical_appointment": {"name": {"zh-CN": "医技预约", "en": "Medical Appointment"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "invoice_printing": {"name": {"zh-CN": "发票打印", "en": "Invoice Printing"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "report_printing": {"name": {"zh-CN": "报告打印", "en": "Report Printing"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "self_service_filing": {"name": {"zh-CN": "自助建档", "en": "Self-Service Filing"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "satisfaction_evaluation": {"name": {"zh-CN": "满意度评价", "en": "Satisfaction Evaluation"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "hospital_introduction": {"name": {"zh-CN": "医院介绍", "en": "Hospital Introduction"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "to_do_list": {"name": {"zh-CN": "待办事项", "en": "To-Do List"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "all": {"name": {"zh-CN": "全部", "en": "All"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "internal_medicine": {"name": {"zh-CN": "内科", "en": "Internal Medicine"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "surgery": {"name": {"zh-CN": "外科", "en": "Surgery"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "orthopedics": {"name": {"zh-CN": "骨科", "en": "Orthopedics"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "respiratory_medicine_clinic": {"name": {"zh-CN": "呼吸内科门诊", "en": "Respiratory Medicine Clinic"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "cardiology": {"name": {"zh-CN": "心内科", "en": "Cardiology"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "vascular_surgery": {"name": {"zh-CN": "血管外科", "en": "Vascular Surgery"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "thyroid_surgery_clinic": {"name": {"zh-CN": "甲状腺外科门诊", "en": "Thyroid Surgery Clinic"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "shoulder_joint_clinic": {"name": {"zh-CN": "肩关节门诊", "en": "Shoulder Joint Clinic"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "page_up": {"name": {"zh-CN": "上翻", "en": "Page Up"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "page_down": {"name": {"zh-CN": "下翻", "en": "Page Down"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "friday": {"name": {"zh-CN": "星期五", "en": "Friday"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "saturday": {"name": {"zh-CN": "星期六", "en": "Saturday"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "sunday": {"name": {"zh-CN": "星期日", "en": "Sunday"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "monday": {"name": {"zh-CN": "星期一", "en": "Monday"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "tuesday": {"name": {"zh-CN": "星期二", "en": "Tuesday"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "wednesday": {"name": {"zh-CN": "星期三", "en": "Wednesday"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "thursday": {"name": {"zh-CN": "星期四", "en": "Thursday"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "cash": {"name": {"zh-CN": "现金", "en": "Cash"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "unionpay_card": {"name": {"zh-CN": "银联卡", "en": "UnionPay Card"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "alipay": {"name": {"zh-CN": "支付宝", "en": "Alipay"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "wechat": {"name": {"zh-CN": "微信", "en": "WeChat"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "face_scan_payment_alipay": {"name": {"zh-CN": "刷脸付（支付宝）", "en": "Face Scan Payment (Alipay)"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "face_scan_payment_wechat": {"name": {"zh-CN": "刷脸付（微信）", "en": "Face Scan Payment (WeChat)"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "cancel": {"name": {"zh-CN": "取消", "en": "Cancel"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "confirm": {"name": {"zh-CN": "确定", "en": "Confirm"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "location_navigation": {"name": {"zh-CN": "位置导航", "en": "Location Navigation"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "ignore": {"name": {"zh-CN": "忽略", "en": "Ignore"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "previous_page": {"name": {"zh-CN": "上一页", "en": "Previous Page"}, "description": {"zh-CN": "用户说上一页", "en": "用户说上一页"}, "message": {"zh-CN": "好的", "en": "好的"}}, "next_page": {"name": {"zh-CN": "下一页", "en": "Next Page"}, "description": {"zh-CN": "用户说下一页", "en": "用户说下一页"}, "message": {"zh-CN": "好的", "en": "好的"}}, "pay_all": {"name": {"zh-CN": "全部缴费", "en": "Pay All"}, "description": {"zh-CN": "用户表明了想要缴费，或者要全部缴费的想法", "en": "用户表明了想要缴费，或者要全部缴费的想法"}, "message": {"zh-CN": "好的", "en": "好的"}}, "close": {"name": {"zh-CN": "关闭", "en": "Close"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "printing": {"name": {"zh-CN": "打印", "en": "Printing"}, "description": {"zh-CN": "用户表达了想要打印的想法，如果用户说我要打印", "en": "用户表达了想要打印的想法，如果用户说我要打印"}, "message": {"zh-CN": "好的", "en": "好的"}}, "complete": {"name": {"zh-CN": "完成", "en": "Complete"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "within_one_week": {"name": {"zh-CN": "一周内", "en": "Within One Week"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "within_one_month": {"name": {"zh-CN": "一月内", "en": "Within One Month"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "within_six_months": {"name": {"zh-CN": "半年内", "en": "Within Six Months"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "within_one_year": {"name": {"zh-CN": "一年内", "en": "Within One Year"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "drug_price": {"name": {"zh-CN": "药品价格", "en": "Drug Price"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "item_price": {"name": {"zh-CN": "项目价格", "en": "<PERSON><PERSON>"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "consumption_list": {"name": {"zh-CN": "消费清单", "en": "Consumption List"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "inpatient_cost_list": {"name": {"zh-CN": "住院费用清单", "en": "Inpatient Cost List"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "outpatient_cost_list": {"name": {"zh-CN": "门诊费用清单", "en": "Outpatient Cost List"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "change_phone_number": {"name": {"zh-CN": "修改手机号", "en": "Change Phone Number"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "change_password": {"name": {"zh-CN": "修改密码", "en": "Change Password"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "query": {"name": {"zh-CN": "查询", "en": "Query"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "wechat_save_to_phone": {"name": {"zh-CN": "微信存至手机", "en": "WeChat Save to Phone"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "department_introduction": {"name": {"zh-CN": "科室简介", "en": "Department Introduction"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "doctor_introduction": {"name": {"zh-CN": "医生简介", "en": "Doctor Introduction"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "get_number": {"name": {"zh-CN": "取号", "en": "Get Number"}, "description": {"zh-CN": "用户表达了想要签到和取号的意愿。", "en": "The user expressed the desire to sign in and take a number."}, "message": {"zh-CN": "好的", "en": "好的"}}, "Children's_Card_Application": {"name": {"zh-CN": "儿童办卡", "en": "Children's Card Application"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "Card_Application": {"name": {"zh-CN": "办卡", "en": "Card Application"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}, "Card_Replacement": {"name": {"zh-CN": "补卡", "en": "Card Replacement"}, "description": {"zh-CN": "", "en": ""}, "message": {"zh-CN": "好的", "en": "好的"}}}, "status": {"not_logged_in": {"en": "User is not logged in, some operations are restricted", "zh-CN": "用户当前还未登录，部分操作受限"}, "login_id_card": {"en": "User has successfully logged in with ID card", "zh-CN": "用户已经使用身份证登录成功了"}, "login_medical_card": {"en": "User has successfully logged in with medical insurance card", "zh-CN": "用户已经使用医保卡登录成功了"}, "login_electronic_medical_insurance": {"en": "User has successfully logged in with electronic medical insurance", "zh-CN": "用户已经使用电子医保登录成功了"}, "login_medical_insurance_face_recognition": {"en": "User has successfully logged in with medical insurance facial recognition", "zh-CN": "用户已经使用医保刷脸登录成功了"}, "login_medical_visit_card": {"en": "User has successfully logged in with medical visit card", "zh-CN": "用户已经使用就诊卡登录成功了"}, "login_outpatient_number": {"en": "User has successfully logged in with outpatient number", "zh-CN": "用户已经使用门诊号登录成功了"}, "login_electronic_health_card": {"en": "User has successfully logged in with electronic health card", "zh-CN": "用户已经使用电子健康卡登录成功了"}, "login_passport_scan": {"en": "User has successfully logged in with passport scan", "zh-CN": "用户已经使用护照扫描登录成功了"}, "login_hong_kong_and_macau_resident_permit": {"en": "User has successfully logged in with Hong Kong and Macau resident permit", "zh-CN": "用户已经使用港澳居民居住证登录成功了"}, "login_medical_visit_barcode": {"en": "User has successfully logged in with medical visit barcode", "zh-CN": "用户已经使用就诊条码登录成功了"}}, "directReply": {"sensitive": {"content": {"en": "I don't understand this.", "zh-CN": "这个我不明白。"}, "description": {"en": "When the user asks about any content that the Chinese government considers sensitive and inappropriate to ask about in public settings. Output format: {\"intention\":\"sensitive\"}", "zh-CN": "当用户询问关于任何中国政府觉得敏感，不适合在公开场合询问的内容时触发。输出格式为{\"intention\":\"sensitive\"}"}}, "irrelevant": {"content": {"en": "The current service focuses on bank self-service kiosks.", "zh-CN": "当前服务专注于银行自助机。"}, "description": {"en": "To avoid occupying unnecessary public resources, users are only allowed to ask the following types of questions: \"banking, finance, electronic payments, deposits, loans, credit services, money transfers, bank account maintenance (e.g., password modifications), as well as questions related to interactions with virtual banking assistants.\" If you believe the user's inquiry falls outside this scope, please directly choose this action. Output format: {\"intention\":\"irrelevant\"}", "zh-CN": "为了避免被占用不必要的公共资源，因此用户仅仅被允许询问下面类型的问题：\"银行、金融、电子支付、存款、贷款、信用卡、资金转账、银行账户维护（例如修改密码）、以及允许被询问和虚拟人交互之类的问题\"。如果你认为用户询问的内容超出了这个范围，请直接选择这个行为。输出格式为{\"intention\":\"irrelevant\"}"}}}, "fallback": {"Dir-unclear": {"description": {"en": "When the user mentions a non-existent feature or input cannot be classified into any of the above options, trigger this action. Think about how to improve the user experience or guide the user back to the current available business operations. For example: if the user says something that is difficult to understand, you can guide the user to rephrase it. Output format: {\"intention\":\"Dir-unclear\", \"message\":\"prompt information to guide the user\"}", "zh-CN": "当用户提及不存在的功能或输入无法归类到任何可选项时触发。思考如何提升用户体验，或者引导用户回到当前可选的业务操作上。例如：用户说了让人无法理解的话，可以引导用户重新表述。输出格式为{\"intention\":\"Dir-unclear\", \"message\":\"引导用户的提示信息\"}"}}}}
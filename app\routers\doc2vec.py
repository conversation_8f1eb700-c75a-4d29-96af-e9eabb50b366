# 标准库导入
import os
import glob
from typing import List, Optional
from urllib.parse import urlparse
from enum import Enum

# 第三方库导入
import requests
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from fastapi_babel import _
from pydantic import BaseModel

# 本地应用导入
from app.config import MAX_EMBEDDING_TOKENS
from app.services.chunk_splitter import split_document
from app.services.milvus_service import (
    delete_chunks_by_document_ids,
    insert_batch_document_chunks,
    ChunkUpsertItem,
)
from app.services.question_generator import QuestionGeneratorWorkflow
from app.services.ai_translate import translate_chunk
from app.utils.logger import log_exception
from app.utils.token_utils import TokenCalculator
from app.utils.net_utils import (
    wrap_response,
    RESPONSE_CODE_SEGMENT_END,
    RESPONSE_CODE_END,
    ERROR_AI_SERVER,
)

# 在文件顶部添加常量定义
QUESTION_GENERATOR_DEVICE_ID = "question_generator"

router = APIRouter()

class SupportedLanguage(str, Enum):
    ZH_CN = "zh-CN"
    ZH_HK = "zh-HK"
    EN = "en"
    AR = "ar"
    MS = "ms"

class DocItem(BaseModel):
    id: int
    url: Optional[str] = None
    genQA: bool
    targetLanguages: Optional[List[str]] = None
    sourceLanguage: SupportedLanguage


class Doc2VecRequest(BaseModel):
    organizationId: int
    docList: List[DocItem]


class QAPair(BaseModel):
    docId: int
    questionList: List[str]
    answer: str
    language: str


class DeleteDocsRequest(BaseModel):
    organizationId: int
    docList: List[int]  # 直接使用文档ID列表


def format_percentage(percentage: float) -> float:
    """格式化百分比，最多保留2位小数"""
    return round(percentage, 2)

@router.post("/doc2vec")
async def doc2vec_endpoint(request: Doc2VecRequest):
    temp_files = []

    async def event_generator():
        try:
            qa_pairs = []
            doc_ids = [doc.id for doc in request.docList]
            total_docs = len(request.docList)

            # Delete chunks for all document IDs
            delete_chunks_by_document_ids(request.organizationId, doc_ids)
            
            for doc_index, doc in enumerate(request.docList, 1):
                doc_qa_pairs = []  # 存储当前文档的QA对
                if doc.url:
                    event_data = {
                        "stage": "start",
                        "message": f"Processing document {doc_index}/{total_docs}",
                        "percentage": format_percentage(0),  # 格式化百分比
                        "currentDoc": {
                            "id": doc.id,
                            "index": doc_index,
                            "total": total_docs
                        }
                    }
                    event_data = wrap_response(event_data, RESPONSE_CODE_SEGMENT_END)
                    print(f"Progress: {event_data.to_sse()}")
                    yield event_data.to_sse()

                    # Download and process the document
                    response = requests.get(doc.url)
                    if response.status_code != 200:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Failed to download document from {doc.url}",
                        )

                    file_extension = os.path.splitext(urlparse(doc.url).path)[1]
                    temp_file_path = f"temp_doc_{doc.id}{file_extension}"
                    temp_files.append(temp_file_path)

                    with open(temp_file_path, "wb") as temp_file:
                        temp_file.write(response.content)

                    chunks = split_document(temp_file_path)
                    os.remove(temp_file_path)

                    all_chunk_items = []
                    valid_chunks = []  # 用于存储可用于QA生成的chunks

                    for i, chunk in enumerate(chunks, 1):
                        # 只在需要翻译时才发送进度事件
                        if doc.targetLanguages and len(doc.targetLanguages) > 0:
                            percentage = (i / len(chunks)) * (20 if doc.genQA else 100)
                            event_data = {
                                "stage": "translating",
                                "message": f"Document {doc_index}/{total_docs} - Translating chunk ({i}/{len(chunks)})",
                                "percentage": format_percentage(percentage),  # 格式化百分比
                                "currentDoc": {
                                    "id": doc.id,
                                    "index": doc_index,
                                    "total": total_docs
                                }
                            }
                            event_data = wrap_response(event_data, RESPONSE_CODE_SEGMENT_END)
                            print(f"Progress: {event_data.to_sse()}")
                            yield event_data.to_sse()

                        # 添加原始语言的chunk到存储列表
                        all_chunk_items.append(
                            ChunkUpsertItem(
                                document_id=doc.id,
                                language=doc.sourceLanguage,
                                content=chunk,
                            )
                        )

                        # 判断是否需要翻译
                        if not doc.targetLanguages or len(doc.targetLanguages) == 0:
                            # 不需要翻译的情况，直接添加到有效chunks
                            valid_chunks.append(chunk)
                            continue

                        # 需要翻译的情况
                        try:
                            translation_result = await translate_chunk(
                                text=chunk, target_languages=doc.targetLanguages
                            )

                            # 检查翻译结果
                            if translation_result and "translations" in translation_result:
                                # 翻译成功，将原始chunk添加到有效列表
                                valid_chunks.append(chunk)
                                
                                # 添加翻译后的chunks到存储列表
                                for lang, translated_content in translation_result["translations"].items():
                                    if translated_content and translated_content.strip():
                                        all_chunk_items.append(
                                            ChunkUpsertItem(
                                                document_id=doc.id,
                                                language=lang,
                                                content=translated_content,
                                            )
                                        )
                            else:
                                print(f"Translation failed for chunk in document {doc.id}")
                        except Exception as e:
                            print(f"Translation error for chunk in document {doc.id}: {str(e)}")

                    # 按token数量和文本数量分批处理chunks
                    current_batch = []
                    current_tokens = 0

                    for item in all_chunk_items:
                        item_tokens = TokenCalculator.calculate_tokens([item.content])

                        # 同时检查token数量和文本数量限制(embedding model最多支持10个输入)
                        if (current_tokens + item_tokens > MAX_EMBEDDING_TOKENS or 
                            len(current_batch) >= 10) and current_batch:
                            # 当前批次已满，先处理当前批次
                            await insert_batch_document_chunks(
                                request.organizationId, current_batch
                            )
                            current_batch = [item]
                            current_tokens = item_tokens
                        else:
                            current_batch.append(item)
                            current_tokens += item_tokens

                    # 处理最后一个批次
                    if current_batch:
                        await insert_batch_document_chunks(
                            request.organizationId, current_batch
                        )

                    # QA生成部分使用valid_chunks
                    if doc.genQA:
                        async for event in QuestionGeneratorWorkflow.process(
                            valid_chunks,  # 只使用有效的chunks生成问题
                            language=doc.sourceLanguage,
                            organization_id=str(request.organizationId)  # 传递组织ID
                        ):
                            if event.stage == "complete":
                                # 处理完成时，保存当前文档的QA对
                                for qa_pair in event.data:
                                    qa_pair_obj = QAPair(
                                        docId=doc.id,
                                        questionList=qa_pair["questions"],
                                        answer=qa_pair["answer"],
                                        language=doc.sourceLanguage,
                                    )
                                    doc_qa_pairs.append(qa_pair_obj)
                                    qa_pairs.append(qa_pair_obj)
                            else:
                                # 更新进度事件
                                event_dict = event.to_dict()
                                event_dict["currentDoc"] = {
                                    "id": doc.id,
                                    "index": doc_index,
                                    "total": total_docs
                                }
                                # 在这里格式化百分比
                                if "percentage" in event_dict:
                                    event_dict["percentage"] = format_percentage(event_dict["percentage"])
                                event_data = wrap_response(event_dict, RESPONSE_CODE_SEGMENT_END)
                                print(f"Progress: {event_data.to_sse()}")
                                yield event_data.to_sse()

                    # 每个文档处理完成后，发送该文档的QA结果
                    if doc_qa_pairs:
                        doc_complete_data = {
                            "stage": "doc_complete",
                            "message": f"Document {doc_index}/{total_docs} processing completed",
                            "currentDoc": {
                                "id": doc.id,
                                "index": doc_index,
                                "total": total_docs
                            },
                            "qa_pairs": [qa.model_dump() for qa in doc_qa_pairs]
                        }
                        event_data = wrap_response(doc_complete_data, RESPONSE_CODE_SEGMENT_END)
                        # print(f"Progress: {event_data.to_sse()}")
                        yield event_data.to_sse()

            # 所有文档处理完成，发送最终结果
            event_data = wrap_response(qa_pairs, RESPONSE_CODE_END)
            # print(f"Progress: {event_data.to_sse()}")
            yield event_data.to_sse()

        except Exception as e:
            err_msg = log_exception(f"{QUESTION_GENERATOR_DEVICE_ID}_{request.organizationId}", e, "doc2vec")
            error_data = {
                "code": 500,
                "data": None,
                "errorMsg": _("服务器处理请求时遇到错误: {}").format(err_msg),
            }
            event_data = wrap_response(error_data, ERROR_AI_SERVER)
            print(f"Progress: {event_data.to_sse()}")
            yield event_data.to_sse()

        finally:
            # 清理临时文件
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            for temp_file in glob.glob("temp_doc_*"):
                os.remove(temp_file)

    return StreamingResponse(event_generator(), media_type="text/event-stream")

@router.delete("/docs")
async def delete_docs(request: DeleteDocsRequest):
    try:
        delete_chunks_by_document_ids(request.organizationId, request.docList)
        return {"code": 0, "message": "Documents successfully deleted"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting documents: {str(e)}")

async def test_doc2vec():
    try:
        # 创建测试数据
        test_request = Doc2VecRequest(
            organizationId=522,
            docList=[
                DocItem(
                    id=16,
                    # url="https://huanzhenshenzhen.oss-cn-shenzhen.aliyuncs.com/upload/2025/01/13/20250113111128A004.docx",
                    url="https://huanzhenshenzhen.oss-cn-shenzhen.aliyuncs.com/upload/2024/11/02/20241102155855A055.txt",
                    genQA=True,
                    targetLanguages=[],
                    sourceLanguage="en",
                )
            ],
        )

        # 执行测试
        response = await doc2vec_endpoint(test_request)

        print("\n=== 开始接收 SSE 事件流 ===\n")
        
        # 处理流式响应
        async for event in response.body_iterator:
            event_str = event if isinstance(event, str) else event.decode("utf-8")
            decoded_str = event_str.encode().decode('unicode-escape')
            print(decoded_str, end="")

        print("\n=== SSE 事件流结束 ===")

    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        print(traceback.format_exc())

# 运行测试
if __name__ == "__main__":
    import asyncio

    asyncio.run(test_doc2vec())

import asyncio
import uuid
from typing import List, Dict, Tuple

class MessageHistory:
    def __init__(self):
        self._histories: Dict[str, List[Dict]] = {}
        self._lock = asyncio.Lock()

    async def add_message(self, session_id: str, message: Dict, max_count: int = 21) -> Tuple[str, List[Dict]]:
        async with self._lock:
            if not session_id:
                session_id = str(uuid.uuid4())
            
            if session_id not in self._histories:
                self._histories[session_id] = []
            
            self._histories[session_id].append(message)
            
            self._histories[session_id] = self._histories[session_id][-max_count:]
            
            return session_id, self._histories[session_id]
    
    async def set_history(self, messages: List[Dict]):
        async with self._lock:
            session_id = str(uuid.uuid4())
            self._histories[session_id] = messages
            return session_id

    async def get_history(self, session_id: str) -> List[Dict]:
        async with self._lock:
            return self._histories.get(session_id, [])

    async def clear_history(self, session_id: str):
        async with self._lock:
            self._histories.pop(session_id, None)

    async def clear_all_histories(self):
        async with self._lock:
            self._histories.clear()
import app.services.mysql_service as db
from app.utils.default_base_prompt import DEFAULT_BASE_PROMPT
from pydantic import BaseModel
from typing import List, Dict

# 添加全局缓存字典
base_prompt_config = {}


class PromptConfig(BaseModel):
    template: str
    tools: List[dict] = []


class BasePromptData(BaseModel):
    intentDetect: Dict[str, PromptConfig] = {}
    knowledge: Dict[str, PromptConfig] = {}
    knowledgeQA: Dict[str, PromptConfig] = {}
    chat: Dict[str, PromptConfig] = {}

    def to_list(self) -> list[dict]:
        return [
            {
                "promptId": prompt_id,
                "language": language,
                "template": config["template"],
                "tools": config["tools"],
            }
            for prompt_id, language_configs in self.model_dump().items()
            for language, config in language_configs.items()
        ]

    @classmethod
    def from_list(cls, data_list: list[dict]) -> "BasePromptData":
        result = {"intentDetect": {}, "knowledge": {}, "knowledgeQA": {}, "chat": {}}

        for item in data_list:
            prompt_id = item.get("promptId")
            language = item.get("language")
            if prompt_id and language:
                if prompt_id not in result:
                    result[prompt_id] = {}

                result[prompt_id][language] = {
                    "template": item.get("template", ""),
                    "tools": item.get("tools", []),
                }

        return cls(**result)


def get_base_prompt_config(setId: int) -> BasePromptData:
    # 如果缓存中存在，直接返回
    if setId in base_prompt_config:
        return base_prompt_config[setId]

    # 否则从数据库加载并缓存
    config = BasePromptData.from_list(
        db.get_base_prompt_list(setId)
    ).model_dump()

    base_prompt_config[setId] = config
    return config


def get_prompt_template(setId: int, promptId: str, language: str):
    # 获取流程：选择意图类型 -> 选择语言 ->获取模板
    language = language or "zh-CN"

    # 1. 使用缓存获取特定组织的配置
    org_config = get_base_prompt_config(setId)
    prompt_dict = org_config.get(promptId, {}) # 根据意图类型获取配置
    ret = prompt_dict.get(language, {}).get("template") # 根据语言获取模板
    
    if not ret and language != "zh-CN":
        ret = prompt_dict.get("zh-CN", {}).get("template")

    # 2. 如果没找到，使用缓存获取组织ID为0的配置
    if not ret and setId != 0:
        default_org_config = get_base_prompt_config(0)
        prompt_dict = default_org_config.get(promptId, {})
        ret = prompt_dict.get(language, {}).get("template")

        if not ret and language != "zh-CN":
            ret = prompt_dict.get("zh-CN", {}).get("template")

    # 3. 如果还没找到，尝试 DEFAULT_BASE_PROMPT
    if not ret:
        prompt_dict = DEFAULT_BASE_PROMPT.get(promptId, {})
        ret = prompt_dict.get(language, {}).get("template")

        if not ret and language != "zh-CN":
            ret = prompt_dict.get("zh-CN", {}).get("template")

    # 如果所有层级都没找到有效模板，抛出异常
    if not ret:
        raise ValueError(
            f"未找到提示模板: promptId={promptId}, language={language}, setId={setId}"
        )
    return ret


def push_base_prompt(setId: int, data: BasePromptData):
    # 更新数据列表中添加组织ID
    data_list = data.to_list()
    for item in data_list:
        item["setId"] = setId

    # 更新数据库
    db.batch_upsert_base_prompt(setId, data_list)

    # 更新缓存
    base_prompt_config[setId] = data.model_dump()

# llm客户端管理，适用于采用不同厂家的不同模型，以及轮换api-key等情况

from openai import AsyncOpenAI, AsyncAzureOpenAI

from functools import lru_cache
from app.config import Env
from app.services import DEVICE_DEFAULT

import asyncio
from app.utils.logger import log_llm_request, log_llm_response, log_text, log_exception, log_embedding_request
from app.utils.net_utils import *
from typing import AsyncGenerator, List, Union,  TypedDict
from fastapi_babel import _
from openai._types import NOT_GIVEN
from app.config import VECTOR_DIMENSION
from app.utils.token_utils import TokenCalculator
from app.utils.logger import log_retry as logger_log_retry
import inspect
import traceback
import os

# 在文件开头添加备选API字典
MODEL_FALLBACK_MAP = {
    "openai": ["azure.gpt-4o-mini"],  # openai的备选provider和备选模型
    "azure": ["openai.gpt-4o-mini"],  # azure的备选provider和备选模型
    "volcano": ["deepseek.deepseek-chat"],  # 火山引擎的备选provider和备选模型
}

def get_models_try_list(model: str) -> List[str]:
    """根据主模型获取要尝试的模型列表
    执行顺序：
    1. 主模型
    2. 备选provider + 主模型名
    3. 备选provider + 备选模型名

    使用自定义模型时，provider填self，直接返回模型列表
    """
    if "." not in model:
        return [model]
        
    provider, model_name = model.split(".", 1)
    if provider not in MODEL_FALLBACK_MAP:
        return [model]
        
    # 构建模型列表
    models_try_list = [model]  # 1. 主模型
    
    # 遍历备选模型，同时处理备选provider的主模型和备选模型
    for fallback_model in MODEL_FALLBACK_MAP[provider]:
        fallback_provider, _ = fallback_model.split(".", 1)
        
        # 2. 备选provider + 主模型名
        fallback_with_main_model = f"{fallback_provider}.{model_name}"
        if fallback_with_main_model not in models_try_list:
            models_try_list.append(fallback_with_main_model)
            
        # 3. 备选provider + 备选模型名
        if fallback_model not in models_try_list:
            models_try_list.append(fallback_model)
    
    return models_try_list

"""
此工具类负责与LLM服务器的交互，包括文本生成、嵌入向量生成、流式响应等
1. 通过retry_decorator装饰器实现了自动重试机制
2. 内部维护了OpenAI的异步客户端实例，根据模型名称选择不同的api-key和base-url，并使用了LruCache缓存
3. 其中文本生成和流式响应，还支持添加system message作为prompt，调用完成后自动移除，
    并且打印了请求和响应日志，方便追溯LLM的调用情况
"""

@lru_cache(maxsize=20)
def _get_client(api_key: str, base_url: str = None, client_type: str = "openai"):
    if client_type == "azure":
        endpoint = base_url
        return AsyncAzureOpenAI(
            api_key=api_key,
            azure_endpoint=endpoint,
            api_version=Env.AZURE_API_VERSION, 
        )
    else:  # 默认为普通OpenAI
        if base_url:
            return AsyncOpenAI(api_key=api_key, base_url=base_url)
        else:
            return AsyncOpenAI(api_key=api_key)


def get_client(model: str): # 配置模型对应的api-key和base-url
    if model.startswith("azure"):  # 添加Azure OpenAI支持
        api_key = Env.AZURE_API_KEY
        base_url = Env.AZURE_BASE_URL
        client_type = "azure"
    elif model.startswith("openai") or model.startswith("gpt"): # 添加 OpenAI 支持
        api_key = Env.OPENAI_API_KEY
        base_url = Env.OPENAI_BASE_URL
        client_type = "openai"
    elif model.startswith("zhipu") or model.startswith("glm"):  # 添加智谱支持
        api_key = Env.ZHIPU_API_KEY
        base_url = Env.ZHIPU_BASE_URL
        client_type = "openai"
    elif model.startswith("deepseek"):  # 添加 DeepSeek 支持
        api_key = Env.DEEPSEEK_API_KEY
        base_url = Env.DEEPSEEK_BASE_URL
        client_type = "openai"
    elif model.startswith("volcano"):  # 添加火山引擎支持
        api_key = Env.VOLCANO_API_KEY
        base_url = Env.VOLCANO_BASE_URL
        client_type = "openai"
    elif model.startswith("aliyun") or model.startswith("qwen"):  # 添加 Qwen 支持
        api_key = Env.QWEN_API_KEY
        base_url = Env.QWEN_BASE_URL
        client_type = "openai"
    else:
        raise ValueError(_("未知的模型: {}").format(model))
    
    return _get_client(api_key, base_url, client_type)

'''
llm_config : {
    llm_service_config: {
        model: str
        base_url: str
        api_key: str
    }
} 
'''
class LLMServiceConfig(TypedDict):
    api_key: str
    base_url: str
    model: str

def get_deploy_model(model: str):
    # 从模型名称中提取部署名称，例如 azure.gpt-4o -> gpt-4o
    return model.split(".", 1)[1] if "." in model else model


def get_llm_client(llm_service_config: LLMServiceConfig) -> AsyncOpenAI:
    api_key: str
    base_url: str
    model: str

    api_key = llm_service_config.get("api_key") or None
    base_url = llm_service_config.get("base_url") or None
    model = llm_service_config.get("model") or None

    if api_key and base_url:  # 优先使用数据库里面的配置
        return _get_client(api_key, base_url)
    elif model:  # 如果数据库没有配置api但有model，则使用环境变量对应的配置
        return get_client(model)
    else:  # 如果数据库完全没有配置，使用默认环境变量模型
        return get_client(Env.DEFAULT_CHAT_MODEL)
    

async def get_embedding(
    text, embedding_config: LLMServiceConfig = None, **kwargs
) -> Union[List[float], List[List[float]]]:
    """异步获取文本的嵌入向量"""
    try:
        device_id = kwargs.get("deviceId", DEVICE_DEFAULT)
        dimensions = kwargs.get("dimensions", VECTOR_DIMENSION)
        token_limit = Env.EMBEDDING_TOKEN_LIMIT
        caller_module = kwargs.get("caller_module", "unknown")


        # 进行变量校验
        embedding_config = dict(embedding_config) if embedding_config else {}
        model = embedding_config.get("model") or Env.DEFAULT_EMBEDDING_MODEL
        client = get_llm_client({**embedding_config, "model": model})
        print(f"[Embedding] Embedding_MODEL: {model}")
        deploy_model = get_deploy_model(model)

        # 修复：正确处理字符串和列表输入，并进行token截断
        if isinstance(text, str):
            input_text = text.replace("\n", " ")
            input_text = TokenCalculator.truncate_string_to_token_limit(input_text, token_limit)
            is_single = True
        else:
            input_text = [TokenCalculator.truncate_string_to_token_limit(t.replace("\n", " "), token_limit) for t in text]
            is_single = False

        def log_retry(retry_state):
            exception = retry_state.outcome.exception()
            from app.utils.logger import log_retry as logger_log_retry
            logger_log_retry(device_id, retry_state.attempt_number, exception, "embedding")

        @retry_decorator(log_retry, request_type="embedding")
        async def make_request():
            # 记录 embedding 请求
            log_embedding_request(
                device_id, 
                {"embedding_model": model}, 
                input_text, 
                caller_module
            )
            print(f"[Embedding] Caller Module: {caller_module}")
            return await client.embeddings.create(
                input=input_text,
                model=deploy_model,
                dimensions=dimensions,
                timeout=NET_CONFIG["timeout"]["embedding"],
            )

        resp = await make_request()
        if resp.data:
            if is_single:
                return resp.data[0].embedding
            return [item.embedding for item in resp.data]
        else:
            raise ValueError("embedding failed")
    except (Exception, asyncio.CancelledError) as e:
        log_exception(device_id, e, "get_embedding")
        raise ValueError(f"Error in get_embedding: {str(e)}")

# 非流式生成接口
async def send_messages(
    messages: list,
    llm_service_config: LLMServiceConfig,
    sys_prompt: str = None,
    functions: list = None,
    response_format=None,
    **kwargs,
):
     # 初始化状态变量
    current_model_index = 0
    attempts_on_current_model = 0
    try:
        device_id = kwargs.get("deviceId", DEVICE_DEFAULT)
        temperature = kwargs.get("temperature", None)
        timeout = kwargs.get("timeout", NET_CONFIG["timeout"]["default"])
        model = (llm_service_config and llm_service_config.get("model")) or None

        log_text(device_id, f"""\nModel in Database: {model}\n""")

        # 确定要尝试的模型列表
        models_try_list = get_models_try_list(model)
        if not models_try_list:
            raise ValueError(_("未提供有效的模型"))


        if sys_prompt:
            messages = add_sys_prompt(messages, sys_prompt)

        def log_retry_switch_model(retry_state):
            nonlocal current_model_index, attempts_on_current_model
            exception = retry_state.outcome.exception()
            
            # 记录重试日志
            logger_log_retry(device_id, retry_state.attempt_number, exception, "chat")
            
            # 需要等待的错误增加尝试次数，其他错误直接切换模型
            if getattr(retry_state, 'need_wait', False):
                attempts_on_current_model += 1
                if attempts_on_current_model < Env.MAX_ATTEMPTS_PER_MODEL:
                    return  # 未达到最大尝试次数，继续使用当前模型
            
            # 切换模型并重置尝试次数
            current_model_index = (current_model_index + 1) % len(models_try_list)
            attempts_on_current_model = 0

        @retry_decorator(log_retry_switch_model, request_type="send_messages", retry_all=True)
        async def make_request():
            nonlocal current_model_index, attempts_on_current_model
            current_model = models_try_list[current_model_index]
            deploy_model = get_deploy_model(current_model)
            
            # 记录请求日志
            log_llm_request(
                device_id,
                {"model": current_model},
                messages,
                functions=functions,
                response_format=response_format,
            )

            # 替换llm_config的model
            client = get_llm_client({**llm_service_config, "model": current_model})
            response = await client.chat.completions.create(
                model=deploy_model,
                messages=messages,
                tools=functions if functions else NOT_GIVEN,
                response_format=response_format if response_format else NOT_GIVEN,
                temperature=temperature if temperature else NOT_GIVEN,
                timeout=timeout,
            )
            
            # API 调用成功，重置当前模型的尝试次数
            attempts_on_current_model = 0
            return response

        response = await make_request()
        response_data = response.model_dump()
        # 使用请求时的模型替换响应中的模型
        response_data["model"] = models_try_list[current_model_index]
        log_llm_response(device_id, response_data)

        if response.choices and response.choices[0]:
            message = response.choices[0].message
            _process_json_response(response_format, message)
            return message
        else:
            log_text(device_id, f"响应为空{response}")
            raise ValueError(_("未响应"))

    except (Exception, asyncio.CancelledError) as e:
        resp = wrap_error(ERROR_LLM_SERVER, f"Error in send_messages: {str(e)}", 
                         models_try_list[current_model_index])
        raise ResponseException(resp)
    finally:
        if sys_prompt:
            remove_sys_prompt(messages)

def _process_json_response(response_format, message):
    """处理JSON响应，如果内容包含JSON格式则提取内容"""
    if not response_format or not message.content:
        return
        
    type = get_value(response_format, "type")
    if type != "json_object":
        return
        
    content = message.content.strip()
    if content.startswith('```json'):
        content = content[7:]  # 移除 ```json
    if content.endswith('```'):
        content = content[:-3]  # 移除结尾的 ```
    message.content = content.strip()

# 流式生成接口
async def stream_chat(
    llm_service_config: LLMServiceConfig, messages: list, sys_prompt: str, **kwargs
) -> AsyncGenerator[Response, None]:
     # 初始化状态变量
    current_model_index = 0
    attempts_on_current_model = 0
    try:
        device_id = kwargs.get("deviceId", DEVICE_DEFAULT)
        session_id = kwargs.get("sessionId")
        custom = kwargs.get("custom", None)

        # 优先使用custom里面model(即接口传参的model)，其次使用llm_config(数据库里的model)，最后使用默认模型(env里的model)
        model = (custom and custom.get("model")) or (llm_service_config and llm_service_config.get("model")) or None

        log_text(device_id, f"""\nModel in Database: {model}\n""")

        # 确定要尝试的模型列表
        models_try_list = get_models_try_list(model)
        if not models_try_list:
            raise ValueError(_("未提供有效的模型"))
        
        # 添加系统提示
        if sys_prompt:
            messages = add_sys_prompt(messages, sys_prompt)

        def log_retry_switch_model(retry_state):
            nonlocal current_model_index, attempts_on_current_model
            exception = retry_state.outcome.exception()
            
            # 记录重试日志
            from app.utils.logger import log_retry as logger_log_retry
            logger_log_retry(device_id, retry_state.attempt_number, exception, "stream chat")
            
            # 需要等待的错误增加尝试次数，其他错误直接切换模型
            if getattr(retry_state, 'need_wait', False):
                attempts_on_current_model += 1
                if attempts_on_current_model < Env.MAX_ATTEMPTS_PER_MODEL:
                    return  # 未达到最大尝试次数，继续使用当前模型
            
            # 切换模型并重置尝试次数
            current_model_index = (current_model_index + 1) % len(models_try_list)
            attempts_on_current_model = 0

        @retry_decorator(log_retry_switch_model, request_type="stream", retry_all=True)
        async def create_stream():
            nonlocal current_model_index, attempts_on_current_model
            current_model = models_try_list[current_model_index]
            deploy_model = get_deploy_model(current_model)
            log_llm_request(
                device_id, 
                {"model": current_model}, 
                messages
            )

            # 替换llm_config的model
            client = get_llm_client({**llm_service_config, "model": current_model})
            stream = await client.chat.completions.create(
                model=deploy_model, 
                messages=messages, 
                stream=True, 
                timeout=NET_CONFIG["timeout"]["stream"]
            )
            
            # API 调用成功，重置当前模型的尝试次数
            attempts_on_current_model = 0
            return stream

        # 此处开始处理流式数据读取中的异常重试
        max_stream_retry = len(models_try_list)
        stream_attempt = 0
        
        while stream_attempt < max_stream_retry:
            try:
                stream = await create_stream()
                
                content = ""
                async for chunk in stream:
                    # 添加检查，确保 choices 列表不为空
                    if not chunk.choices or len(chunk.choices) == 0:
                        continue
                        
                    choice = chunk.choices[0]
                    if choice.finish_reason:
                        yield wrap_response(wrap_chat_data(session_id), RESPONSE_CODE_END)
                        dump = chunk.model_dump()
                        dump["choices"][0]["delta"]["content"] = content
                        # 使用请求时的模型替换响应中的模型
                        dump["model"] = models_try_list[current_model_index]
                        log_llm_response(device_id, dump)
                        return
                    else:
                        delta_content = choice.delta.content or ""
                        content += delta_content
                        data = wrap_chat_data(session_id, message=delta_content)
                        yield wrap_response(data, RESPONSE_CODE_CONTINUE)
                return
            except asyncio.CancelledError:
                raise
            except Exception as stream_error:
                # 流处理过程中出错，记录并重试
                stream_attempt += 1
                
                # 记录重试日志
                from app.utils.logger import log_retry as logger_log_retry
                logger_log_retry(device_id, stream_attempt, stream_error, "stream chat")
                
                # 增加当前模型的尝试次数，如果超过限制则切换模型
                attempts_on_current_model += 1
                if attempts_on_current_model >= Env.MAX_ATTEMPTS_PER_MODEL:
                    current_model_index = (current_model_index + 1) % len(models_try_list)
                    attempts_on_current_model = 0
                
                # 继续while循环尝试下一次
                await asyncio.sleep(0.5)
                continue
        
        resp = wrap_error(
            ERROR_LLM_SERVER, 
            f"流处理过程中所有重试均失败 (尝试次数: {stream_attempt})",
            model[current_model_index]
        )
        raise ResponseException(resp)
    except (Exception, asyncio.CancelledError) as e:
        resp = wrap_error(ERROR_LLM_SERVER, f"Error in stream_chat: {str(e)}", 
                         models_try_list[current_model_index])
        raise ResponseException(resp)
    finally:
        if sys_prompt:
            remove_sys_prompt(messages)

{"organizationId": 316, "name": "page", "data": [{"pageId": "1.2-Greeting-Dialogue-Page", "name": "打招呼对话页", "description": "在这个页面，自助机将会引导用户登录也可以回答用户的疑问以及解决用户的各种问题。假如你发现历史聊天记录里面assistant曾回复\"签到需要先登录，请问您想使用什么方式登录\"类似的话，说白了就是assistant曾经反问过用户想要用什么方式登录。这种情况下，用户说身份证，身份证登录，我们都不应该选择button而是选择login。", "language": "zh-CN", "buttonList": ["medical_insurance_face_recognition_login", "medical_insurance_card", "electronic_medical_insurance_login", "id_card_login", "medical_visit_card_login", "passport_scan", "electronic_health_card", "hong_kong_and_macau_resident_permit", "medical_visit_barcode", "simplified_chinese", "english"], "optionalActions": ["knowledge", "registration", "medical-guidance", "login", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "1.2-Greeting-Dialogue-Page", "name": "Greeting Dialogue Page", "description": "On this page, the self-service machine will guide users to log in and answer their questions and solve their various problems. If you find that the assistant has replied in the historical chat record that \"You need to log in first to sign in. How do you want to log in?\", it means that the assistant has asked the user how he wants to log in. In this case, if the user says ID card, ID card login, we should not select button but login.", "language": "en", "buttonList": ["medical_insurance_face_recognition_login", "medical_insurance_card", "electronic_medical_insurance_login", "id_card_login", "medical_visit_card_login", "passport_scan", "electronic_health_card", "hong_kong_and_macau_resident_permit", "medical_visit_barcode", "simplified_chinese", "english"], "optionalActions": ["knowledge", "registration", "medical-guidance", "login", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "2.1-menu-page", "name": "菜单页", "description": "在这个页面里面，自助机将会引导用户挂号也可以回答用户的疑问以及解决用户的各种问题。请注意在这个页面里如果用户表达了想要缴费，打印报告，打印发票和取号的想法。请直接选择对应的按钮。如果是建档，请选择record。如果用户说签到视同于取号。如果用户说我要挂号，或者挂号，请直接选择对应的按钮意图。除非用户询问要挂什么科室才会选择导诊。", "language": "zh-CN", "buttonList": ["register", "payment", "inpatient_recharge", "medical_appointment", "invoice_printing", "report_printing", "self_service_filing", "general_inquiry", "satisfaction_evaluation", "hospital_introduction", "to_do_list", "exit", "ignore", "get_number"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "record", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "2.1-menu-page", "name": "<PERSON><PERSON>", "description": "On this page, the self-service machine will guide users to register, answer their questions, and resolve various issues. Please note that on this page, if the user expresses the desire to pay, print report, print invoice and get number, please select the corresponding button directly. If it is for creating a file, please select record. If the user states that check-in is equivalent to taking a number. If the user says \"I want to register\" or \"register,\" directly select the corresponding button intent. Only choose triage if the user asks which department to register for.", "language": "en", "buttonList": ["register", "payment", "ticket_collection", "inpatient_recharge", "medical_appointment", "invoice_printing", "report_printing", "self_service_filing", "general_inquiry", "satisfaction_evaluation", "hospital_introduction", "to_do_list", "exit", "ignore", "get_number"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "record", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.1-Select-Department-Page", "name": "选择科室页", "description": "在这个页面里面，自助机会引导用户选择挂号的科室", "language": "zh-CN", "buttonList": ["all", "page_up", "page_down", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.1-Select-Department-Page", "name": "Select Department Page", "description": "On this page, the self-service machine will guide users to select the department for registration", "language": "en", "buttonList": ["all", "page_up", "page_down", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.2-Select-Time-Page", "name": "选择时间页", "description": "在这个页面里面，自助机会引导用户选择挂号的时间和医生。如果用户只说了周一，周二这种的指令，我们可以当做是点击了按钮。但是如果用户说了具体的医生名字和具体的日期，请走registration意图，因为对具体日期和医生的逻辑处理，我们需要在那里完成。", "language": "zh-CN", "buttonList": ["friday", "saturday", "sunday", "monday", "tuesday", "wednesday", "thursday", "back", "exit"], "optionalActions": ["knowledge", "registration", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.2-Select-Time-Page", "name": "Select Time Page", "description": "On this page, the self-service machine guides users to select appointment times and doctors. If the user only provides instructions like 'Monday' or 'Tuesday,' we can treat it as if they clicked a button. However, if the user specifies a particular doctor's name and a specific date, please proceed with the 'registration' action. This is because the logic processing for specific dates and doctors needs to be completed within that action.", "language": "en", "buttonList": ["friday", "saturday", "sunday", "monday", "tuesday", "wednesday", "thursday", "back", "exit"], "optionalActions": ["knowledge", "registration", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.3-Confirm-Registration-Information-Page", "name": "确认挂号信息页", "description": "在这个页面里面，自助机想引导用户确认自己的挂号信息", "language": "zh-CN", "buttonList": ["back", "exit"], "optionalActions": ["knowledge", "registration", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.3-Confirm-Registration-Information-Page", "name": "Confirm Registration Information Page", "description": "On this page, the self-service machine intends to guide users to confirm their registration information", "language": "en", "buttonList": ["back", "exit"], "optionalActions": ["knowledge", "registration", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.4-Registration-Process-Payment-Method-Selection-Page", "name": "挂号流程支付方式选择页", "description": "在这个页面里面，自助机会引导用户选择支付方式", "language": "zh-CN", "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.4-Registration-Process-Payment-Method-Selection-Page", "name": "Registration Process Payment Method Selection Page", "description": "On this page, the self-service machine will guide users to select a payment method", "language": "en", "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.5-<PERSON><PERSON>-<PERSON>-<PERSON>", "name": "扫码页", "description": "在这个页面里面，自助机会引导用户扫描二维码支付", "language": "zh-CN", "buttonList": ["cancel"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.5-<PERSON><PERSON>-<PERSON>-<PERSON>", "name": "Scan Code Page", "description": "On this page, the self-service machine will guide users to scan a QR code for payment", "language": "en", "buttonList": ["cancel"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.6-Registration-Success-Page", "name": "挂号成功页", "description": "在这个页面里面，自助机会引导用户取走小票，并在用户拿走小票后给用户导航", "language": "zh-CN", "buttonList": ["confirm", "location_navigation"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.6-Registration-Success-Page", "name": "Registration Success Page", "description": "On this page, the self-service machine will guide the user to take the receipt, and after the user takes the receipt, it will provide navigation for the user", "language": "en", "buttonList": ["confirm", "location_navigation"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.7-Navigation-Page", "name": "导航页", "description": "在这个页面里面，自助机会帮用户导航", "language": "zh-CN", "buttonList": ["exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.7-Navigation-Page", "name": "Navigation Page", "description": "On this page, the self-service machine will help the user navigate", "language": "en", "buttonList": ["exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.9-Select-Time-Period-Page", "name": "选时间段页", "description": "在这个页面里面，用户可以选择挂号的具体时间段。如果用户要选择具体的时间，或者修改挂号信息，请走registration。", "language": "zh-CN", "buttonList": ["cancel", "confirm"], "optionalActions": ["knowledge", "registration", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.9-Select-Time-Period-Page", "name": "Select Time Period Page", "description": "In this page, users can select the specific time period for registration. If users want to select a specific time or modify registration information, please go to registration.", "language": "en", "buttonList": ["cancel", "confirm"], "optionalActions": ["knowledge", "registration", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.10-Confirm-And-Select-Payment-Method-Page", "name": "确认并选支付方式页", "description": "在这个页面里面，用户可以确认挂号详细信息，并选择支付方式，在这个页面里面对于支付的方式，我们统一使用Dir-button的逻辑。如果你觉得用户是在表达想用某种方式完成支付，请直接选择对应的按钮。如果用户想要修改挂号信息，例如想要更换医生，科室，时间，请走registration。", "language": "zh-CN", "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "registration", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "3.10-Confirm-And-Select-Payment-Method-Page", "name": "Confirm And Select Payment Method Page", "description": "On this page, users can review their appointment details and select a payment method. For payment options, we consistently follow the Dir-button logic. If you believe the user intends to pay using a specific method, directly select the corresponding button. If the user wants to modify appointment details, such as changing the doctor, department, or time, please proceed through the registration process.", "language": "en", "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "registration", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "2.2-Pending-<PERSON>", "name": "待办页", "description": "在这个页面里面，自助机会提醒用户有待缴费或待打印或待取号的项目。请注意在这个页面里如果用户表达了想要缴费，打印报告，打印发票和取号的想法。请直接选择对应的按钮。", "language": "zh-CN", "buttonList": ["ignore", "invoice_printing", "report_printing", "get_number"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "2.2-Pending-<PERSON>", "name": "Pending Page", "description": "On this page, the self-service machine will remind users that there are items to be paid, printed or numbered. Please note that on this page, if the user expresses the desire to pay, print report, print invoice and get number, please select the corresponding button directly.", "language": "en", "buttonList": ["ignore", "invoice_printing", "report_printing", "get_number"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.1-Payment-Selection-Page", "name": "缴费选择页", "description": "在这个页面里面，用户可以查看待缴费的项目列表。如果用户说全部缴费，或者缴费都选择对应的按钮。如果用户说了", "language": "zh-CN", "buttonList": ["back", "exit", "previous_page", "next_page", "pay_all"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.1-Payment-Selection-Page", "name": "Payment Selection Page", "description": "On this page, the user can view the list of pending payment items. If the user says pay all or make payments, select the corresponding button.", "language": "en", "buttonList": ["back", "exit", "previous_page", "next_page", "pay_all"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.2-Prescription-Details-View-Page", "name": "处方详情查看页", "description": "在这个页面里面，用户会查看自己所有的待缴费项目详情", "language": "zh-CN", "buttonList": ["close", "previous_page", "next_page"], "optionalActions": ["knowledge", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.2-Prescription-Details-View-Page", "name": "Prescription Details View Page", "description": "On this page, the user will view the details of all their pending payment items", "language": "en", "buttonList": ["close", "previous_page", "next_page"], "optionalActions": ["knowledge", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.3-Payment-Details-Confirmation-Page", "name": "缴费详情确认页", "description": "在这个页面里面，自助机会引导用户确认自己的缴费信息", "language": "zh-CN", "buttonList": ["back", "exit"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.3-Payment-Details-Confirmation-Page", "name": "Payment Details Confirmation Page", "description": "On this page, the self-service machine will guide the user to confirm their payment information", "language": "en", "buttonList": ["back", "exit"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.4-Payment-Process-Payment-Method-Selection-Page", "name": "缴费流程支付方式选择页", "description": "在这个页面里面，自助机会引导用户选择一种支付方式", "language": "zh-CN", "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.4-Payment-Process-Payment-Method-Selection-Page", "name": "Payment Process Payment Method Selection Page", "description": "On this page, the self-service machine will guide the user to select a payment method", "language": "en", "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.5-Medical-Insurance-Method-Guidance-Card-Insertion-Page", "name": "医保方式指引插卡页", "description": "在这个页面里面，自助机会引导用户插入医保卡", "language": "zh-CN", "buttonList": ["back"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.5-Medical-Insurance-Method-Guidance-Card-Insertion-Page", "name": "Medical Insurance Method Guidance Card Insertion Page", "description": "On this page, the self-service machine will guide the user to insert their medical insurance card", "language": "en", "buttonList": ["back"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.6-Medical-Insurance-Method-Guidance-Card-Insertion-Page", "name": "微信和支付宝方式扫码页", "description": "在这个页面里面，自助机会引导用户扫码", "language": "zh-CN", "buttonList": ["cancel"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.6-Medical-Insurance-Method-Guidance-Card-Insertion-Page", "name": "Medical Insurance Method Guidance Card Insertion Page", "description": "On this page, the self-service machine will guide the user to insert their medical insurance card", "language": "en", "buttonList": ["cancel"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.7-Payment-Success-Page", "name": "缴费成功页", "description": "在这个页面里面，自助机会引导用户取走小票，并在用户拿走小票后给用户导航", "language": "zh-CN", "buttonList": ["confirm", "location_navigation"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "4.7-Payment-Success-Page", "name": "Payment Success Page", "description": "On this page, the self-service machine will guide the user to take the receipt, and after the user takes the receipt, it will provide navigation for the user", "language": "en", "buttonList": ["confirm", "location_navigation"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "2.3-Print-Pending-Page", "name": "打印待办页", "description": "在这个页面里面，自助机会提醒用户有待打印的事项", "language": "zh-CN", "buttonList": ["ignore", "invoice_printing", "report_printing"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "2.3-Print-Pending-Page", "name": "Print Pending Page", "description": "On this page, the self-service machine will remind the user of items pending printing", "language": "en", "buttonList": ["ignore", "invoice_printing", "report_printing"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.1-Print-List-Page", "name": "报告打印列表页", "description": "在这个页面里面，用户可以查看待打印的项目列表。用户若说打印，请选择对应的按钮。", "language": "zh-CN", "buttonList": ["back", "exit", "previous_page", "next_page", "printing"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.1-Print-List-Page", "name": "Print List Page", "description": "On this page, users can view a list of items pending printing. If the user says print, please select the corresponding button.", "language": "en", "buttonList": ["back", "exit", "previous_page", "next_page", "printing"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.2-Report-Details-View-Page", "name": "报告详情查看页", "description": "在这个页面里面，用户会查看报告详情", "language": "zh-CN", "buttonList": ["close"], "optionalActions": ["knowledge", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.2-Report-Details-View-Page", "name": "Report Details View Page", "description": "On this page, users can view the details of the report", "language": "en", "buttonList": ["close"], "optionalActions": ["knowledge", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.3-Print-Report-Waiting-Page", "name": "打印报告等待页", "description": "在这个页面里面，用户正在等待自助机打印，自助机会显示打印进度", "language": "zh-CN", "buttonList": [], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.3-Print-Report-Waiting-Page", "name": "Print Report Waiting Page", "description": "On this page, the user is waiting for the self-service machine to print, and the self-service machine will display the printing progress", "language": "en", "buttonList": [], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.4-Report-Printing-Success-Page", "name": "报告打印成功页", "description": "在这个页面里面，自助机会引导用户取走报告", "language": "zh-CN", "buttonList": ["complete"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.4-Report-Printing-Success-Page", "name": "Report Printing Success Page", "description": "On this page, the self-service machine will guide the user to take the report.", "language": "en", "buttonList": ["complete"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.5-Invoice-Printing-List-Page", "name": "发票打印列表页", "description": "在这个页面里面，用户可以查看待打印的项目列表", "language": "zh-CN", "buttonList": ["back", "exit", "previous_page", "next_page", "printing", "within_one_week", "within_one_month", "within_six_months", "within_one_year"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.5-Invoice-Printing-List-Page", "name": "Invoice Printing List Page", "description": "On this page, users can view a list of items pending printing", "language": "en", "buttonList": ["back", "exit", "previous_page", "next_page", "printing", "within_one_week", "within_one_month", "within_six_months", "within_one_year"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.6-Invoice-Printing-Waiting-Page", "name": "发票打印等待页", "description": "在这个页面里面，用户正在等待自助机打印，自助机会显示打印进度", "language": "zh-CN", "buttonList": [], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.6-Invoice-Printing-Waiting-Page", "name": "Invoice Printing Waiting Page", "description": "On this page, the user is waiting for the self-service machine to print, and the self-service machine will display the printing progress", "language": "en", "buttonList": [], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.7-Invoice-Printing-Success-Page", "name": "发票打印成功页", "description": "在这个页面里面，自助机会引导用户取走发票", "language": "zh-CN", "buttonList": ["complete"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "5.7-Invoice-Printing-Success-Page", "name": "Invoice Printing Success Page", "description": "On this page, the self-service machine will guide the user to take the invoice", "language": "en", "buttonList": ["complete"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.1-Invoice-Printing-Success-Page", "name": "药品查询页", "description": "在这个页面里面，用户可以查询药品价格", "language": "zh-CN", "buttonList": ["complete"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.1-Invoice-Printing-Success-Page", "name": "Invoice Printing Success Page", "description": "On this page, the self-service machine will guide the user to take the invoice", "language": "en", "buttonList": ["complete"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2-Comprehensive-Inquiry-<PERSON>", "name": "综合查询页", "description": "在这个页面里面，用户可以查询药品价格、项目价格、消费清单等一些信息。", "language": "zh-CN", "buttonList": ["drug_price", "item_price", "consumption_list", "inpatient_cost_list", "outpatient_cost_list", "change_phone_number", "change_password", "back"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2-Comprehensive-Inquiry-<PERSON>", "name": "Comprehensive Inquiry Page", "description": "On this page, users can check information such as drug prices, item prices, consumption lists, and more.", "language": "en", "buttonList": ["drug_price", "item_price", "consumption_list", "inpatient_cost_list", "outpatient_cost_list", "change_phone_number", "change_password", "back"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.1-Drug-Price-Inquiry-Page", "name": "药品价格查询页", "description": "在这个页面里面，用户可以查询药品价格的详细信息。", "language": "zh-CN", "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.1-Drug-Price-Inquiry-Page", "name": "Drug Price Inquiry Page", "description": "On this page, users can inquire about detailed information on drug prices.", "language": "en", "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.2-<PERSON><PERSON>-Price-Inquiry-Page", "name": "项目价格查询页", "description": "在这个页面里面，用户可以查询项目价格的详细信息。", "language": "zh-CN", "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.2-<PERSON><PERSON>-Price-Inquiry-Page", "name": "Item Price Inquiry Page", "description": "On this page, users can inquire about detailed information on item prices.", "language": "en", "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.3-Consumption-List-Inquiry-Page", "name": "消费清单查询页", "description": "在这个页面里面，用户可以查看消费清单。", "language": "zh-CN", "buttonList": ["query", "previous_page", "next_page", "back", "exit", "wechat_save_to_phone"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.3-Consumption-List-Inquiry-Page", "name": "Consumption List Inquiry Page", "description": "On this page, users can view their consumption list.", "language": "en", "buttonList": ["query", "previous_page", "next_page", "back", "exit", "wechat_save_to_phone"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.4-Inpatient-Expense-List-Page", "name": "住院费用清单页", "description": "在这个页面里面，用户可以查看住院费用清单。", "language": "zh-CN", "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.4-Inpatient-Expense-List-Page", "name": "Inpatient Expense List Page", "description": "On this page, users can view the inpatient expense list.", "language": "en", "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.5-Outpatient-Expense-List-Page", "name": "门诊费用清单页", "description": "在这个页面里面，用户可以查看门诊费用清单。", "language": "zh-CN", "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.5-Outpatient-Expense-List-Page", "name": "Outpatient Expense List Page", "description": "On this page, users can view the outpatient expense list.", "language": "en", "buttonList": ["query", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.6-<PERSON><PERSON><PERSON>-Phone-Number-Page", "name": "修改手机号页", "description": "在这个页面里面，用户可以修改手机号。", "language": "zh-CN", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "change-phone"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.6-<PERSON><PERSON><PERSON>-Phone-Number-Page", "name": "Modify Phone Number Page", "description": "On this page, users can modify their phone number.", "language": "en", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "change-phone"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.7-Modify-Password-Page", "name": "修改密码页", "description": "在这个页面里面，用户可以修改密码。", "language": "zh-CN", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "change-password"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.2.7-Modify-Password-Page", "name": "Modify Password Page", "description": "On this page, users can modify their password.", "language": "en", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "change-password"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.3-Hospital-Introduction-Page", "name": "医院介绍页", "description": "在这个页面里面，用户可以查询医院简介、科室简介、医生简介的信息。", "language": "zh-CN", "buttonList": ["hospital_introduction", "department_introduction", "doctor_introduction", "back"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.3-Hospital-Introduction-Page", "name": "Hospital Introduction Page", "description": "On this page, users can check information about the hospital introduction, department introduction, and doctor introduction.", "language": "en", "buttonList": ["hospital_introduction", "department_introduction", "doctor_introduction", "back"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.3.1-Hospital-Detailed-Introduction-Page", "name": "医院具体简介页", "description": "在这个页面里面，用户可以查看医院简介的详细信息。", "language": "zh-CN", "buttonList": ["exit", "back"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.3.1-Hospital-Detailed-Introduction-Page", "name": "Hospital Detailed Introduction Page", "description": "On this page, users can view detailed information about the hospital introduction.", "language": "en", "buttonList": ["exit", "back"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.3.2-Department-Introduction-Page", "name": "科室介绍页", "description": "在这个页面里面，用户可以选择要查看的科室。", "language": "zh-CN", "buttonList": ["exit", "back", "respiratory_medicine_clinic", "cardiology", "vascular_surgery", "thyroid_surgery_clinic", "shoulder_joint_clinic", "previous_page", "next_page"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.3.2-Department-Introduction-Page", "name": "Department Introduction Page", "description": "On this page, users can select the department they want to view.", "language": "en", "buttonList": ["exit", "back", "respiratory_medicine_clinic", "cardiology", "vascular_surgery", "thyroid_surgery_clinic", "shoulder_joint_clinic", "previous_page", "next_page"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.3.3-Doctor-Introduction-Page", "name": "医生介绍页", "description": "在这个页面里面，用户可以选择要查看的医生。", "language": "zh-CN", "buttonList": ["zhou_junqing", "peng_fang", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "7.3.3-Doctor-Introduction-Page", "name": "Doctor Introduction Page", "description": "On this page, users can select the doctor they want to view.", "language": "en", "buttonList": ["zhou_junqing", "peng_fang", "previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "registration", "medical-guidance", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "2.4-Adult-Registration-Page", "name": "成人建档页", "description": "在这个页面里面，自助机会引导用户输入手机号建档或补卡。", "language": "zh-CN", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "2.4-Adult-Registration-Page", "name": "Adult Registration Page", "description": "On this page, the self-service machine will guide users to enter their mobile phone number to create a file or reissue a card.", "language": "en", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "6.1-Adult-Registration-Confirmation-Page", "name": "成人建档确认页", "description": "在这个页面里面，自助机会引导用户确认建档信息。", "language": "zh-CN", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "6.1-Adult-Registration-Confirmation-Page", "name": "Adult Registration Confirmation Page", "description": "On this page, the self-service machine will guide the user to confirm their registration information.", "language": "en", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "6.2-Adult-Registration-Confirmation-Page", "name": "建档成功页", "description": "在这个页面里面，自助机会引导用户取走就诊卡", "language": "zh-CN", "buttonList": ["complete"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "6.2-Adult-Registration-Confirmation-Page", "name": "Adult Registration Confirmation Page", "description": "On this page, the self-service machine will guide the user to confirm their registration information.", "language": "en", "buttonList": ["complete"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "6.3-Child-Registration-Page", "name": "儿童建档页", "description": "在这个页面里面，自助机会引导用户输入儿童信息进行儿童建档。", "language": "zh-CN", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "6.3-Child-Registration-Page", "name": "Child Registration Page", "description": "On this page, the self-service machine will guide the user to enter the child's information for child registration.", "language": "en", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "6.4-Child-Registration-Confirmation-Page", "name": "儿童建档确认信息页", "description": "在这个页面里面，自助机会引导用户确认儿童建档信息。", "language": "zh-CN", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "6.4-Child-Registration-Confirmation-Page", "name": "Child Registration Confirmation Page", "description": "On this page, the self-service machine will guide the user to confirm the child's registration information.", "language": "en", "buttonList": ["confirm", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "record"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "8.1-Ticket-Collection-Page", "name": "取号页", "description": "在这个页面里面，自助机会引导用户取号。", "language": "zh-CN", "buttonList": ["previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "8.1-Ticket-Collection-Page", "name": "Ticket Collection Page", "description": "On this page, the self-service machine will guide the user to collect a ticket.", "language": "en", "buttonList": ["previous_page", "next_page", "back", "exit"], "optionalActions": ["knowledge", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "8.2-Ticket-Collection-Success-Page", "name": "取号成功页", "description": "在这个页面里面，用户取号成功，引导用户拿走凭条。", "language": "zh-CN", "buttonList": ["complete", "location_navigation"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}, {"pageId": "8.2-Ticket-Collection-Success-Page", "name": "Ticket Collection Success Page", "description": "On this page, the user has successfully collected a ticket, guiding the user to take the receipt.", "language": "en", "buttonList": ["complete", "location_navigation"], "optionalActions": ["knowledge", "way-finding", "chat", "query"], "directReply": ["sensitive", "irrelevant"]}]}
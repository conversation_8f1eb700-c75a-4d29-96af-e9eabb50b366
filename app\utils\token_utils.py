import tiktoken

class TokenCalculator:
    _instance = None
    _encoding = None

    @classmethod
    def get_encoding(cls):
        """获取tokenizer单例实例"""
        if cls._encoding is None:
            cls._encoding = tiktoken.get_encoding("cl100k_base")
        return cls._encoding

    @classmethod
    def calculate_tokens(cls, texts: list[str]) -> int:
        """计算文本列表的总token数
        
        Args:
            texts: 要计算的文本列表
            
        Returns:
            int: 总token数
        """
        encoding = cls.get_encoding()
        return sum(len(encoding.encode(text)) for text in texts)

    @classmethod
    def truncate_string_to_token_limit(cls, text: str, max_tokens: int) -> str:
        """截取字符串以确保不超过指定的token限制
        
        Args:
            text: 要检查的字符串
            max_tokens: 最大允许的token数
            
        Returns:
            str: 原字符串或截取后的字符串
        """
        encoding = cls.get_encoding()
        tokens = encoding.encode(text)
        
        if len(tokens) <= max_tokens:
            return text
        
        truncated_tokens = tokens[:max_tokens]
        return encoding.decode(truncated_tokens)

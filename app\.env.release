# 以下为动态开发环境配置，支持热更新
# 上线之前记得注释不要用的配置

#####################################
# 业务服务器配置 (必须选择其中一个)
#####################################

# 选项1: 北京正式业务服务器
BIZ_SERVER_URL=http://************/prod-api/business/data/ai

# 选项2: 新加坡正式业务服务器
# BIZ_SERVER_URL=http://************/prod-api/business/data/ai

#####################################
# OpenAI 配置 (必须选择其中一个)
#####################################

# 选项1: OpenAI 官方接口
OPENAI_BASE_URL=https://api.openai.com/v1

# 选项2: 新加坡直连
# OPENAI_BASE_URL=http://************:5001

# 选项3: 北京中转
# OPENAI_BASE_URL=http://************/prod-api/openai/v1/

#####################################
# 智谱AI 配置
#####################################
ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4/

#####################################
# Deepseek 配置 (必须选择其中一个)
#####################################

# 选项1: Deepseek 官方接口
# DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# 选项2: Deepseek 阿里云转接接口
DEEPSEEK_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

#####################################
# 火山引擎配置
#####################################
VOLCANO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3

#####################################
# 阿里Qwen配置
#####################################
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

#####################################
# 数据库配置 (必须选择其中一个组合)
#####################################

# 选项1: 本地环境
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=ai_chat
MILVUS_HOST=localhost
MILVUS_PORT=19530
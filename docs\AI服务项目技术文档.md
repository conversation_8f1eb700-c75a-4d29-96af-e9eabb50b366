# AI服务项目技术文档

## 1. 项目概述

### 1.1 项目目标
AI服务项目是一个基于FastAPI构建的智能对话系统，旨在为多组织提供统一的AI聊天、知识库问答、意图检测、文档处理和翻译服务。系统支持多语言、多模型、多租户架构，具备高可用性和可扩展性。

### 1.2 核心功能
- **智能对话服务**：支持上下文感知的多轮对话，集成多种LLM模型
- **知识库问答**：基于RAG（检索增强生成）技术的智能问答系统
- **意图检测**：自动识别用户意图并进行问题重写优化
- **多语言支持**：支持中文（简体/繁体）、英语、马来语、阿拉伯语等
- **文档处理**：支持PDF、Word、PPT、Excel等多种格式的文档解析和分块
- **翻译服务**：提供高质量的多语言翻译功能
- **配置管理**：支持动态配置推送和热更新
- **工具调用**：支持本地和远程工具的链式调用

### 1.3 技术栈

#### 后端框架
- **FastAPI**：现代化的Python Web框架，支持异步处理和自动API文档生成
- **Uvicorn**：高性能ASGI服务器
- **Pydantic**：数据验证和序列化

#### 数据存储
- **MySQL**：关系型数据库，存储配置信息、提示词、业务数据等
- **Milvus**：向量数据库，存储知识库文档块和问答对的向量表示
- **内存存储**：基于自研Store系统的会话和缓存管理

#### AI/ML组件
- **LangChain**：LLM应用开发框架
- **OpenAI API**：GPT系列模型接口
- **多模型支持**：Azure OpenAI、智谱AI、DeepSeek、阿里Qwen、火山引擎等
- **Embedding模型**：text-embedding-3-small等向量化模型
- **语言检测**：Lingua + OpenCC中文繁简体转换

#### 模板引擎
- **Jinja2**：动态提示词模板渲染，支持多语言和上下文注入

#### 文档处理
- **PyMuPDF**：PDF文档解析
- **python-docx**：Word文档处理
- **python-pptx**：PowerPoint文档处理
- **openpyxl**：Excel文档处理

#### 其他工具
- **tiktoken**：Token计算和限制
- **cachetools**：缓存管理
- **cryptography**：加密和安全
- **fastapi-babel**：国际化支持

## 2. 系统架构分析

AI聊天核心流程图

```mermaid
flowchart TD
    Start([用户发送消息]) --> LanguageDetect[语言检测<br/>LanguageDetector]
    LanguageDetect --> InputProcess[输入预处理<br/>wrap_user_input]
    InputProcess --> AddHistory[添加到消息历史<br/>MessageHistory.add_message]
    
    AddHistory --> IntentDetect[意图检测<br/>IntentDetector.detect]
    IntentDetect --> IntentLLM[调用LLM进行意图识别<br/>返回intention和rewrite]
    IntentLLM --> DirectCheck{是否有直接回复?}
    
    DirectCheck -->|是| DirectReply[返回直接回复]
    DirectCheck -->|否| HandlerSelect[根据意图选择处理器<br/>create_handlers]
    
    HandlerSelect --> KnowledgeFlow{意图类型判断}
    
    %% 知识库流程
    KnowledgeFlow -->|knowledge| KnowledgeHandler[知识库处理器<br/>KnowledgeHandler.handle]
    KnowledgeHandler --> QASearch[问答对搜索<br/>search_question]
    QASearch --> QACheck{问答匹配度检查}
    
    QACheck -->|高匹配度| DirectAnswer[直接返回问答结果]
    QACheck -->|低匹配度| RewriteSearch[重写问题搜索]
    RewriteSearch --> RewriteCheck{重写问题匹配度检查}
    
    RewriteCheck -->|高匹配度| QALLMAnalysis[LLM分析问答结果<br/>qa_llm_analysis]
    RewriteCheck -->|低匹配度| RAGSearch[RAG文档块检索<br/>get_relevant_chunks]
    
    QALLMAnalysis --> FinalAnswer[生成最终回答]
    RAGSearch --> RAGGenerate[RAG生成回答<br/>generate_rag_response]
    RAGGenerate --> FinalAnswer
    
    %% 业务流程
    KnowledgeFlow -->|business| BizHandler[业务处理器<br/>BizHandler.handle]
    BizHandler --> BizLLM[调用业务LLM<br/>支持工具调用]
    BizLLM --> ToolCheck{是否包含工具调用?}
    
    ToolCheck -->|是| ToolExecution[执行工具调用<br/>本地/远程函数]
    ToolExecution --> ToolResponse[获取工具响应]
    ToolResponse --> ToolLLM[LLM处理工具响应]
    ToolLLM --> ToolCheck
    
    ToolCheck -->|否| BizAnswer[生成业务回答]
    BizAnswer --> FinalAnswer
    
    %% 聊天流程
    KnowledgeFlow -->|chat| ChatHandler[聊天处理器<br/>ChatHandler.handle]
    ChatHandler --> ChatLLM[调用聊天LLM<br/>stream_chat]
    ChatLLM --> ChatAnswer[生成聊天回答]
    ChatAnswer --> FinalAnswer
    
    %% 最终处理
    DirectReply --> AddAssistantHistory[添加助手回复到历史]
    FinalAnswer --> AddAssistantHistory
    AddAssistantHistory --> StreamResponse[流式返回响应<br/>SSE格式]
    StreamResponse --> End([结束])
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef llmCall fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef storage fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    
    class Start,End startEnd
    class LanguageDetect,InputProcess,HandlerSelect,QASearch,RewriteSearch,RAGSearch,ToolExecution,ToolResponse process
    class DirectCheck,QACheck,RewriteCheck,ToolCheck,KnowledgeFlow decision
    class IntentLLM,QALLMAnalysis,BizLLM,ToolLLM,ChatLLM llmCall
    class AddHistory,AddAssistantHistory storage
```

Agent链式调用流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Router as 聊天路由
    participant AiChat as AI聊天服务
    participant IntentAgent as 意图检测Agent
    participant KnowledgeAgent as 知识库Agent
    participant BizAgent as 业务Agent
    participant LLMClient as LLM客户端
    participant Tools as 工具服务
    participant DB as 数据库

    Client->>Router: 发送聊天请求
    Router->>AiChat: handle_chat()
    
    Note over AiChat: 1. 语言检测和输入预处理
    AiChat->>AiChat: 语言检测
    AiChat->>AiChat: 输入预处理
    AiChat->>AiChat: 添加到消息历史
    
    Note over AiChat,IntentAgent: 2. 意图检测阶段
    AiChat->>IntentAgent: detect(messages, **kwargs)
    IntentAgent->>LLMClient: 调用LLM进行意图识别
    LLMClient-->>IntentAgent: 返回意图和重写结果
    IntentAgent-->>AiChat: 返回检测结果
    
    Note over AiChat: 3. 处理器选择和调用
    AiChat->>AiChat: 根据意图选择处理器
    
    alt 知识库意图
        AiChat->>KnowledgeAgent: handle(messages, **kwargs)
        
        Note over KnowledgeAgent: 4a. 知识库处理流程
        KnowledgeAgent->>DB: 搜索问答对
        DB-->>KnowledgeAgent: 返回匹配结果
        
        alt 高匹配度问答
            KnowledgeAgent-->>AiChat: 直接返回问答结果
        else 需要RAG检索
            KnowledgeAgent->>DB: 检索文档块
            DB-->>KnowledgeAgent: 返回相关文档
            KnowledgeAgent->>LLMClient: 生成RAG回答
            LLMClient-->>KnowledgeAgent: 返回生成结果
            KnowledgeAgent-->>AiChat: 返回最终回答
        end
        
    else 业务意图
        AiChat->>BizAgent: handle(messages, **kwargs)
        
        Note over BizAgent: 4b. 业务处理流程（支持工具调用链）
        BizAgent->>LLMClient: 调用业务LLM
        LLMClient-->>BizAgent: 返回响应（可能包含工具调用）
        
        loop 工具调用链（最多3次）
            alt 包含工具调用
                BizAgent->>BizAgent: 解析工具调用参数
                
                alt 本地函数调用
                    BizAgent->>BizAgent: 执行本地函数
                    BizAgent-->>BizAgent: 获取本地函数结果
                else 远程工具调用
                    BizAgent->>Tools: 调用远程工具
                    Tools-->>BizAgent: 返回工具结果
                end
                
                BizAgent->>BizAgent: 将工具结果添加到消息历史
                BizAgent->>LLMClient: 继续LLM处理
                LLMClient-->>BizAgent: 返回新响应
            else 无工具调用
                Note over BizAgent: 退出工具调用循环
            end
        end
        
        BizAgent-->>AiChat: 返回最终业务回答
    end
    
    Note over AiChat: 5. 响应处理和返回
    AiChat->>AiChat: 添加助手回复到历史
    AiChat->>Router: 流式返回响应
    Router->>Client: SSE流式响应
    
    Note over Client,DB: 错误处理和重试机制
    Note over LLMClient: • LLM调用支持多模型重试<br/>• 网络错误自动重试<br/>• 超时控制和熔断
    Note over BizAgent: • 工具调用限制最多3次<br/>• 异常时清理工具消息<br/>• 支持本地和远程工具
    Note over DB: • 数据库连接重试<br/>• 事务回滚机制<br/>• 连接池管理
```

数据流向图



```mermaid
flowchart LR
    %% 输入数据源
    subgraph "输入数据源"
        UserInput[用户输入文本]
        BackgroundInfo[背景信息]
        SessionId[会话ID]
        DeviceId[设备ID]
        OrgId[组织ID]
    end
    
    %% 数据预处理层
    subgraph "数据预处理层"
        LanguageDetect[语言检测<br/>zh-CN/en/zh-HK/ms/ar]
        InputWrapper[输入包装器<br/>格式化和清理]
        ContextBuilder[上下文构建器<br/>合并背景信息]
    end
    
    %% 配置数据层
    subgraph "配置数据层"
        OrgConfig[组织配置<br/>从MySQL加载]
        IntentConfig[意图配置<br/>意图列表和描述]
        PromptConfig[提示词配置<br/>多语言模板]
        LLMConfig[LLM配置<br/>模型和API设置]
        UIConfig[UI配置<br/>格式化器配置]
    end
    
    %% 消息历史层
    subgraph "消息历史层"
        MessageHistory[消息历史<br/>内存存储]
        SessionStore[会话存储<br/>Store系统]
        HistoryLimit[历史限制<br/>最多21条消息]
    end
    
    %% 意图检测层
    subgraph "意图检测层"
        IntentPrompt[意图检测提示词<br/>Jinja2模板渲染]
        IntentLLM[意图检测LLM<br/>返回JSON格式]
        IntentResult[意图结果<br/>intention + rewrite]
    end
    
    %% 知识库数据层
    subgraph "知识库数据层"
        QACollection[问答对集合<br/>Milvus向量库]
        ChunkCollection[文档块集合<br/>Milvus向量库]
        EmbeddingService[向量化服务<br/>text-embedding-3-small]
        SimilaritySearch[相似度搜索<br/>IP距离计算]
    end
    
    %% 处理器层
    subgraph "处理器层"
        KnowledgeProcessor[知识库处理器<br/>多级查询策略]
        BizProcessor[业务处理器<br/>工具调用支持]
        ChatProcessor[聊天处理器<br/>对话生成]
    end
    
    %% LLM调用层
    subgraph "LLM调用层"
        LLMRouter[LLM路由器<br/>多模型支持]
        ModelFallback[模型降级<br/>失败时切换]
        ResponseStream[响应流<br/>SSE格式]
    end
    
    %% 输出数据层
    subgraph "输出数据层"
        ResponseData[响应数据<br/>结构化JSON]
        StreamOutput[流式输出<br/>实时传输]
        HistoryUpdate[历史更新<br/>添加助手回复]
    end
    
    %% 数据流连接
    UserInput --> LanguageDetect
    BackgroundInfo --> ContextBuilder
    SessionId --> SessionStore
    DeviceId --> MessageHistory
    OrgId --> OrgConfig
    
    LanguageDetect --> InputWrapper
    InputWrapper --> ContextBuilder
    ContextBuilder --> MessageHistory
    
    OrgConfig --> IntentConfig
    OrgConfig --> PromptConfig
    OrgConfig --> LLMConfig
    OrgConfig --> UIConfig
    
    MessageHistory --> IntentPrompt
    IntentConfig --> IntentPrompt
    PromptConfig --> IntentPrompt
    IntentPrompt --> IntentLLM
    IntentLLM --> IntentResult
    
    IntentResult --> KnowledgeProcessor
    IntentResult --> BizProcessor
    IntentResult --> ChatProcessor
    
    KnowledgeProcessor --> EmbeddingService
    EmbeddingService --> QACollection
    EmbeddingService --> ChunkCollection
    QACollection --> SimilaritySearch
    ChunkCollection --> SimilaritySearch
    SimilaritySearch --> KnowledgeProcessor
    
    KnowledgeProcessor --> LLMRouter
    BizProcessor --> LLMRouter
    ChatProcessor --> LLMRouter
    
    LLMConfig --> LLMRouter
    LLMRouter --> ModelFallback
    ModelFallback --> ResponseStream
    
    ResponseStream --> ResponseData
    ResponseData --> StreamOutput
    ResponseData --> HistoryUpdate
    HistoryUpdate --> MessageHistory
    
    %% 样式定义
    classDef inputStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef processStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef configStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef storageStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef llmStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef outputStyle fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    
    class UserInput,BackgroundInfo,SessionId,DeviceId,OrgId inputStyle
    class LanguageDetect,InputWrapper,ContextBuilder,IntentPrompt,IntentLLM,EmbeddingService,SimilaritySearch processStyle
    class OrgConfig,IntentConfig,PromptConfig,LLMConfig,UIConfig configStyle
    class MessageHistory,SessionStore,QACollection,ChunkCollection storageStyle
    class KnowledgeProcessor,BizProcessor,ChatProcessor,LLMRouter,ModelFallback llmStyle
    class ResponseData,StreamOutput,HistoryUpdate outputStyle
```

核心类关系图

```mermaid
classDiagram
    %% 核心服务类
    class AiChat {
        +organization_id: int
        +language: str
        +config: dict
        +llm_config: dict
        +embedding_config: dict
        +message_history: MessageHistory
        +intentDetector: IntentDetector
        +knowledge_handler: KnowledgeHandler
        +default_handler: KnowledgeHandler
        +chat(userInput, backgroundInfo, sessionId, characterId, deviceId, language, llm_config, custom)
        +create_handlers(handler_config, promptConfig)
        +update_aichat_llm_config(llm_config, language)
        +resolve_background_info(backgroundInfo, sessionId)
        +wrap_user_input(userInput, ctx)
        +gen_direct_message(sessionId, detect_result, language, backgroundInfo)
    }
    
    %% 意图检测器
    class IntentDetector {
        +organization_id: int
        +llm_service_config: dict
        +detect(messages, **kwargs)
        +build_intent_detect_messages(ctx, language)
        +fix_intention_response(content, intentions, formatters)
        +match_intention_formatter(intention, intentions, formatters)
        +match_intention(intention, config)
    }
    
    %% 知识库处理器
    class KnowledgeHandler {
        +organization_id: int
        +chat_handler: ChatHandler
        +llm_service_config: dict
        +embedding_config: dict
        +LANGUAGE_MAPPING: dict
        +handle(messages, **kwargs)
        +search_question(organization_id, embedding, language)
        +get_relevant_chunks(organization_id, embedding, language)
        +generate_rag_response(query, histories, context, **kwargs)
        +qa_llm_analysis(qa_results, query, language, **kwargs)
        +deduplicate_chunks(chunks, filter_fn)
    }
    
    %% 业务处理器
    class BizHandler {
        +organization_id: int
        +llm_service_config: dict
        +prompt: str
        +prompt_template: str
        +promptId: str
        +functions: list
        +handle(messages, **kwargs)
        +process_message(message, messages, **kwargs)
        +parse_tool_call(message)
        +handle_local_function(function_info, organization_id)
        +request(path, params)
        +http_request(path, params)
        +insertToolResp(message, path, tool_response)
        +removeToolMessages(messages)
        +update_prompt(prompt_str, biz_data)
    }
    
    %% 聊天处理器
    class ChatHandler {
        +organization_id: int
        +llm_service_config: dict
        +handle(messages, **kwargs)
    }
    
    %% 消息历史管理
    class MessageHistory {
        -_histories: Dict[str, List[Dict]]
        -_lock: asyncio.Lock
        +add_message(session_id, message, max_count)
        +set_history(messages)
        +get_history(session_id)
        +clear_history(session_id)
        +clear_all_histories()
    }
    
    %% LLM客户端
    class LLMServiceConfig {
        +model: str
        +api_key: str
        +base_url: str
        +api_version: str
    }
    
    %% 基础提示词服务
    class BasePromptService {
        +get_prompt_template(setId, promptId, language)
        +push_base_prompt(setId, data)
        +get_base_prompt_config(setId)
    }
    
    %% Jinja2模板引擎
    class Jinja2TemplateEngine {
        +language: str
        +strict_mode: bool
        +chainable: bool
        +env: Environment
        -_template_cache: Dict[str, Template]
        +get_template(template_string)
        +_auto_lang_finalize(value)
        +_get_fallback_localized_template(template_dict)
        +_template_filter(context, context_data, template_str)
        +_render_filter(context, template_str, context_data)
    }
    
    %% 存储系统
    class Store {
        -_cache: dict
        -_backend: Backend
        -_lock: asyncio.Lock
        +get(item_id, path, default)
        +set(item_id, value, path)
        +delete(item_id, path)
    }
    
    %% 语言检测器
    class LanguageDetector {
        +detector: LanguageDetector
        +converter_s2t: OpenCC
        +converter_t2s: OpenCC
        +chinese_range: Pattern
        +detect_language(query, default_lang)
    }
    
    %% 关系定义
    AiChat --> IntentDetector : 使用
    AiChat --> KnowledgeHandler : 使用
    AiChat --> MessageHistory : 管理
    AiChat --> LanguageDetector : 使用
    
    KnowledgeHandler --> ChatHandler : 包含
    KnowledgeHandler --> BizHandler : 可选使用
    
    IntentDetector --> LLMServiceConfig : 配置
    KnowledgeHandler --> LLMServiceConfig : 配置
    BizHandler --> LLMServiceConfig : 配置
    ChatHandler --> LLMServiceConfig : 配置
    
    IntentDetector --> BasePromptService : 获取提示词
    KnowledgeHandler --> BasePromptService : 获取提示词
    BizHandler --> BasePromptService : 获取提示词
    
    BasePromptService --> Jinja2TemplateEngine : 模板渲染
    
    AiChat --> Store : 会话存储
    MessageHistory --> Store : 历史存储
    
    %% 继承关系
    KnowledgeHandler --|> IntentionHandler : 继承
    BizHandler --|> IntentionHandler : 继承
    ChatHandler --|> IntentionHandler : 继承
```



### 2.1 整体架构设计

系统采用分层架构设计，从上到下分为以下几层：

1. **API网关层**：FastAPI应用 + 中间件（超时控制、多语言支持）
2. **路由层**：按功能模块划分的路由处理器
3. **核心服务层**：业务逻辑处理的核心组件
4. **LLM客户端层**：统一的大语言模型调用接口
5. **提示词管理层**：动态提示词生成和缓存
6. **数据存储层**：MySQL、Milvus、内存存储
7. **工具层**：语言检测、文档处理、日志等辅助工具

### 2.2 模块划分

#### 路由层模块
- `chat.py`：聊天对话接口
- `translate.py`：翻译服务接口
- `history.py`：消息历史管理接口
- `prompt.py`：提示词管理接口
- `push_config.py`：配置推送接口
- `doc2vec.py`：文档向量化接口
- `env.py`：环境配置接口

#### 核心服务层模块
- `ai_chat.py`：AI聊天服务核心逻辑
- `intention_detect.py`：意图检测服务
- `knowledge_handler.py`：知识库处理服务
- `biz_handler.py`：业务处理服务（支持工具调用）
- `ai_translate.py`：翻译服务
- `message_history.py`：消息历史管理
- `llm_client.py`：LLM客户端统一接口

#### 数据服务层模块
- `mysql_service.py`：MySQL数据库操作
- `milvus_service.py`：Milvus向量数据库操作
- `config_service.py`：配置管理服务

### 2.3 核心组件职责

#### AiChat（AI聊天服务）
- 统筹整个对话流程的执行
- 管理消息历史和会话状态
- 协调意图检测和处理器选择
- 处理多语言和多模型配置

#### IntentDetector（意图检测器）
- 分析用户输入的意图类型
- 执行问题重写和优化
- 支持多语言意图识别
- 提供意图匹配和格式化功能

#### KnowledgeHandler（知识库处理器）
- 实现多级知识库查询策略
- 管理问答对和文档块检索
- 执行RAG（检索增强生成）流程
- 支持相似度阈值控制和结果去重

#### BizHandler（业务处理器）
- 处理业务相关的复杂逻辑
- 支持工具调用链（最多3次）
- 管理本地和远程工具调用
- 提供错误处理和重试机制

## 3. 核心流程梳理

### 3.1 聊天对话流程

完整的聊天对话流程包含以下关键步骤：

1. **请求接收**：客户端发送聊天请求到 `/chat` 接口
2. **语言检测**：使用Lingua+OpenCC检测用户输入语言
3. **输入预处理**：格式化和清理用户输入
4. **消息历史管理**：将用户消息添加到会话历史（最多21条）
5. **意图检测**：调用LLM分析用户意图并重写问题
6. **处理器选择**：根据检测到的意图选择对应的处理器
7. **业务处理**：执行具体的业务逻辑（知识库查询/业务处理/聊天生成）
8. **响应生成**：生成结构化响应并添加到历史记录
9. **流式返回**：以SSE格式实时返回响应给客户端

### 3.2 意图检测流程

意图检测是系统的核心环节，具体流程如下：

1. **上下文构建**：合并会话历史、背景信息和组织配置
2. **提示词生成**：使用Jinja2模板动态生成意图检测提示词
3. **LLM调用**：调用意图检测专用LLM模型
4. **结果解析**：解析返回的JSON格式结果（intention + rewrite）
5. **意图匹配**：将检测结果与配置的意图列表进行匹配
6. **格式化处理**：应用UI配置中的格式化器
7. **结果返回**：返回最终的意图和重写结果

### 3.3 知识库查询流程

知识库查询采用多级查询策略，确保查询效果：

1. **直接问答匹配**：在问答库中查找高相似度匹配（阈值>0.98）
2. **重写问题查询**：使用重写后的问题再次查找匹配
3. **LLM辅助问答**：使用LLM分析问答库中的相关结果
4. **基础RAG检索**：基于原始问题检索相关文档块
5. **重写RAG检索**：使用重写问题进行文档块检索
6. **RAG生成答案**：基于检索内容生成最终回答
7. **Fallback处理**：如果都没有匹配，使用后备处理器

### 3.4 工具调用流程

业务处理器支持复杂的工具调用链：

1. **LLM响应解析**：检查LLM返回是否包含工具调用
2. **工具类型判断**：区分本地函数调用和远程工具调用
3. **参数提取**：解析工具调用的参数信息
4. **工具执行**：执行对应的工具函数
5. **结果处理**：将工具执行结果添加到对话历史
6. **继续处理**：让LLM处理工具响应，可能触发新的工具调用
7. **循环控制**：最多允许3次工具调用，防止无限循环
8. **清理机制**：处理完成后清理工具调用相关消息

## 4. 系统提示词构建机制分析

### 4.1 提示词模板设计模式

系统采用分层的提示词管理架构：



#### 4.1.1 基础提示词层
- **默认模板**：`app/utils/default_base_prompt.py` 中定义的系统默认提示词
- **组织级模板**：存储在MySQL中，支持组织级别的自定义
- **多语言支持**：每个提示词支持多种语言版本（zh-CN、en、zh-HK等）

#### 4.1.2 提示词类型分类
```python
# 核心提示词类型
KEY_INTENTION_DETECT = "intentDetect"    # 意图检测提示词
KEY_KNOWLEDGE_RAG = "knowledge"          # 知识库RAG提示词
KEY_KNOWLEDGE_QA = "knowledgeQA"         # 知识库问答提示词
KEY_CHAT = "chat"                        # 聊天对话提示词
KEY_ALL_BUSINESS = "allBusiness"         # 业务处理提示词
```

#### 4.1.3 提示词获取优先级
1. **组织特定配置**：优先使用当前组织的自定义提示词
2. **语言回退**：如果当前语言不存在，回退到zh-CN
3. **组织回退**：如果组织配置不存在，使用组织ID为0的默认配置
4. **系统默认**：最后使用硬编码的系统默认提示词

### 4.2 动态提示词生成机制

#### 4.2.1 Jinja2模板引擎
系统使用自研的Jinja2模板引擎（`app/base/jinja2_formatter.py`）：

**核心特性：**
- **模板缓存**：编译后的模板对象缓存，提高渲染性能
- **多语言自动处理**：自动选择合适的语言版本
- **上下文合并**：支持父子上下文的智能合并
- **错误处理**：严格模式和宽松模式的错误处理策略
- **链式访问**：支持未定义属性的链式访问

**关键方法：**
```python
# 模板编译和缓存
def get_template(template_string: str) -> Template

# 多语言模板处理
def _get_fallback_localized_template(template_dict: Dict[str, str]) -> str

# 自定义过滤器
def _template_filter(context, context_data: Any, template_str: str) -> str
def _render_filter(context, template_str: Any, context_data: Any) -> str
```

#### 4.2.2 上下文信息注入

**上下文数据来源：**
1. **会话上下文**：当前会话的历史消息和状态
2. **背景信息**：用户提供的背景信息和设备信息
3. **组织配置**：组织级别的配置信息
4. **业务数据**：动态的业务相关数据
5. **系统信息**：当前时间、语言设置等

**上下文构建流程：**
```python
# 获取组合后的上下文
ctx = await get_ctx_with_bg_info(sessionId, backgroundInfo)

# 上下文包含的关键信息
{
    "ORG_CONFIG": {
        "intentionConfig": {...},    # 意图配置
        "promptConfig": {...},       # 提示词配置
        "uiConfig": {...}           # UI配置
    },
    "backgroundInfo": {...},        # 背景信息
    "currentTime": "2024年01月01日 10:30 星期一"  # 当前时间
}
```

### 4.3 提示词优化和版本管理

#### 4.3.1 提示词版本控制
- **数据库存储**：所有提示词版本存储在MySQL的`base_prompt`表中
- **组织隔离**：通过`setId`字段实现组织级别的提示词隔离
- **批量更新**：支持通过API批量推送和更新提示词

#### 4.3.2 提示词优化策略
1. **A/B测试支持**：可以为不同组织配置不同的提示词版本
2. **性能监控**：记录提示词使用效果和响应质量
3. **动态调整**：支持运行时动态更新提示词配置
4. **回滚机制**：保留历史版本，支持快速回滚

#### 4.3.3 提示词模板示例

**意图检测提示词模板：**
```jinja2
根据会话上下文、背景信息来识别用户意图类型。以下是可能的意图类型及其描述：

<<<{intentionList}>>>

请仔细分析用户的输入内容，将其归类到以上意图类型中的一种，输出到intention字段。

任务2.基于用户输入的内容，在下列情况做一定改写，使改写后的内容简洁、清晰且与上下文相关：
1.聚焦：思考用户查询的主要目的，移除不必要的词汇
2.纠正谐音：语音识别有可能识别成谐音字，根据上下文纠正谐音字
3.上下文指代消解：结合上下文将查询中指代词替换为准确的对象
4.使用简体中文来输出改写内容，但是保留特殊专有名词不再改写

将重写后的内容输出到rewrite字段。注意输出必须为纯JSON，格式如下：{"intention":"","rewrite":""}
```

**知识库RAG提示词模板：**
```jinja2
请基于Context中的信息回答问题。在寻找答案时请注意：
1.考虑中文数字和阿拉伯数字的等价性（比如"第一百三十四条"和"第134条"是等价的）
2.使用Context中的原文本回答，不要自行添加原文没有的内容
3.如果无法从Context中确定答案，请用友好的语气向用户解释你无法回答
4.回答时不要提到"Context"、"QA Data"、"Chunk Data"这几个词
5.不要输出markdown格式（比如**, #等）
6.无论用户用什么语言问你，都使用{{language}}回答

{{character_prompt}}
```

## 5. Agent链式调用流程分析

### 5.1 Agent定义和分类

系统中的Agent按功能分为以下几类：

#### 5.1.1 核心Agent
- **AiChat Agent**：聊天服务的总控制器，负责流程编排
- **IntentDetector Agent**：意图检测专用Agent
- **KnowledgeHandler Agent**：知识库查询和RAG处理Agent
- **BizHandler Agent**：业务逻辑处理Agent，支持工具调用
- **ChatHandler Agent**：纯聊天对话Agent

#### 5.1.2 工具Agent
- **LanguageDetector Agent**：语言检测Agent
- **TranslateService Agent**：翻译服务Agent
- **ChunkSplitter Agent**：文档分块处理Agent

### 5.2 调用链的触发条件和执行顺序

#### 5.2.1 主调用链
```
客户端请求 → 路由层 → AiChat Agent → IntentDetector Agent →
处理器选择 → [KnowledgeHandler/BizHandler/ChatHandler] Agent →
LLM调用 → 响应生成 → 客户端
```

#### 5.2.2 触发条件
1. **意图驱动**：根据意图检测结果选择对应的处理Agent
2. **阈值控制**：基于相似度阈值决定是否进入下一级处理
3. **配置驱动**：根据组织配置决定启用哪些Agent
4. **异常触发**：异常情况下的降级和重试机制

#### 5.2.3 执行顺序控制
- **串行执行**：主流程采用串行执行，确保数据一致性
- **并行优化**：翻译等独立任务支持并行处理
- **优先级队列**：配置推送等任务使用优先级队列

### 5.3 Agent间的数据传递机制

#### 5.3.1 数据传递格式
```python
# 标准的Agent间数据传递格式
{
    "messages": [...],           # 对话历史
    "deviceId": "device_123",    # 设备ID
    "sessionId": "session_456",  # 会话ID
    "organizationId": 1,         # 组织ID
    "language": "zh-CN",         # 语言
    "rewrite": "重写后的问题",    # 重写问题
    "backgroundInfo": {...},     # 背景信息
    "custom": {...}             # 自定义参数
}
```

#### 5.3.2 上下文传递机制
- **会话级上下文**：通过SessionStore在Agent间共享会话状态
- **请求级上下文**：通过kwargs参数传递请求相关信息
- **组织级上下文**：通过组织配置在Agent间共享配置信息

#### 5.3.3 状态管理
- **无状态设计**：Agent本身保持无状态，状态存储在外部
- **状态同步**：通过Store系统确保状态的一致性
- **状态隔离**：不同会话和组织的状态完全隔离

### 5.4 错误处理和重试策略

#### 5.4.1 LLM调用重试
```python
# 多模型重试机制
@retry_decorator(log_retry_switch_model, request_type="send_messages", retry_all=True)
async def make_request():
    # 模型列表轮询
    models_try_list = ["gpt-4o-mini", "gpt-3.5-turbo", "claude-3-sonnet"]
    # 每个模型最多重试2次
    MAX_ATTEMPTS_PER_MODEL = 2
```

**重试策略：**
- **指数退避**：重试间隔逐渐增加
- **模型切换**：单个模型失败后切换到备用模型
- **错误分类**：区分可重试和不可重试的错误类型
- **熔断机制**：连续失败达到阈值时暂停调用

#### 5.4.2 数据库重试
```python
@retry_on_db_error(is_write=False, max_retries=3, retry_delay=1)
def database_operation():
    # 数据库操作重试机制
    # 自动检测连接状态并重连
    # 区分读写操作的重试策略
```

#### 5.4.3 工具调用错误处理
- **调用次数限制**：最多3次工具调用，防止无限循环
- **异常清理**：异常发生时自动清理工具调用消息
- **降级处理**：工具调用失败时的降级策略
- **超时控制**：工具调用的超时时间控制

### 5.5 性能优化措施

#### 5.5.1 缓存策略
- **模板缓存**：Jinja2模板编译结果缓存
- **配置缓存**：组织配置的内存缓存
- **LRU缓存**：模板引擎实例的LRU缓存
- **向量缓存**：Embedding结果的缓存

#### 5.5.2 连接池管理
- **数据库连接池**：MySQL连接池（最大32个连接）
- **HTTP连接池**：LLM API调用的连接复用
- **向量数据库连接**：Milvus连接的生命周期管理

#### 5.5.3 异步优化
- **异步I/O**：全面使用异步I/O操作
- **并发控制**：使用信号量控制并发数量
- **流式处理**：支持流式响应，减少延迟
- **批量操作**：数据库批量插入和更新操作

## 6. 关键代码模块说明

### 6.1 核心类详细说明

#### 6.1.1 AiChat类 (`app/services/ai_chat.py`)

**主要职责：**
- 统筹整个AI聊天流程的执行
- 管理消息历史和会话状态
- 协调各个处理器的调用

**关键方法：**

<augment_code_snippet path="app/services/ai_chat.py" mode="EXCERPT">
````python
class AiChat:
    def __init__(self, organization_id: int, language: str = "zh-CN"):
        self.organization_id = organization_id
        self.language = language
        self.config = load_config(organization_id)
        self.llm_config = get_llm_config(organization_id)
        self.embedding_config = get_embedding_config(organization_id)
        # 初始化各种处理器
        self.message_history = MessageHistory()
        self.intentDetector = IntentDetector(organization_id, self.llm_config.get(LLM_INTENTION_CONFIG))
````
</augment_code_snippet>

**核心流程方法：**
- `chat()`：主要的聊天处理入口
- `create_handlers()`：根据配置创建不同类型的处理器
- `resolve_background_info()`：解析和处理背景信息
- `wrap_user_input()`：用户输入的预处理

#### 6.1.2 IntentDetector类 (`app/services/intention_detect.py`)

**主要职责：**
- 检测用户输入的意图类型
- 执行问题重写和优化
- 支持多语言意图识别

**关键方法：**

<augment_code_snippet path="app/services/intention_detect.py" mode="EXCERPT">
````python
async def detect(self, messages, **kwargs) -> tuple[str, str, str, str, dict]:
    """检测用户意图，返回(intention, rewrite, extraInfo, message)"""
    # 构建意图检测提示词
    sys_prompt = await self.build_intent_detect_messages(ctx, language)
    # 调用LLM进行意图识别
    message = await send_messages(
        llm_service_config=self.llm_service_config,
        messages=messages,
        sys_prompt=sys_prompt,
        response_format={"type": "json_object"},
        temperature=0.3,
        **kwargs,
    )
````
</augment_code_snippet>

#### 6.1.3 KnowledgeHandler类 (`app/services/knowledge_handler.py`)

**主要职责：**
- 实现多级知识库查询策略
- 管理问答对和文档块检索
- 执行RAG（检索增强生成）流程

**多级查询策略：**

<augment_code_snippet path="app/services/knowledge_handler.py" mode="EXCERPT">
````python
async def handle(self, messages, **kwargs):
    """
    处理知识库查询请求，实现多级查询策略。

    查询策略（按优先级）:
    1. 直接问答匹配：从问答库中查找完全匹配的答案
    2. 重写问题查询：使用重写后的问题再次查找匹配
    3. LLM辅助问答：使用LLM分析问答库中的问答结果
    4. 基础RAG检索：基于原始问题的文档块检索
    5. 重写RAG检索：使用重写问题进行文档块检索
    6. RAG生成答案：基于检索的内容生成回答
    7. Fallback处理：如果都没有匹配，使用后备处理器
    """
````
</augment_code_snippet>

#### 6.1.4 BizHandler类 (`app/services/biz_handler.py`)

**主要职责：**
- 处理业务相关的复杂逻辑
- 支持工具调用链（最多3次）
- 管理本地和远程工具调用

**工具调用处理：**

<augment_code_snippet path="app/services/biz_handler.py" mode="EXCERPT">
````python
async def process_message(self, message, messages, **kwargs):
    """
    处理LLM返回的消息，支持工具调用链和普通对话响应。

    工具调用流程:
    1. 解析工具调用参数
    2. 执行工具调用
    3. 将工具响应添加到对话历史
    4. 继续让LLM处理工具响应

    Safety:
    - 限制最多3次工具调用，防止无限循环
    - 确保在异常情况下清理工具调用相关消息
    """
````
</augment_code_snippet>

### 6.2 数据模型说明

#### 6.2.1 MySQL数据模型

**核心表结构：**

1. **IntentionConfig（意图配置表）**
   - `organizationId`：组织ID
   - `key`：意图键名
   - `data`：意图配置数据（JSON格式）

2. **PromptConfig（提示词配置表）**
   - `organizationId`：组织ID
   - `promptId`：提示词ID
   - `language`：语言代码
   - `template`：提示词模板内容

3. **BasePrompt（基础提示词表）**
   - `setId`：配置集ID（通常等于organizationId）
   - `promptId`：提示词类型ID
   - `language`：语言代码
   - `template`：模板内容
   - `tools`：工具配置（JSON格式）

4. **BusinessData（业务数据表）**
   - `organizationId`：组织ID
   - `key`：数据键名
   - `language`：语言代码
   - `data`：业务数据（JSON格式）

#### 6.2.2 Milvus向量数据模型

**问答对集合（QA Collection）：**
```python
{
    "id": "qa_pair_id",
    "organization_id": 1,
    "language": "zh-CN",
    "question": "用户问题",
    "answer": "标准答案",
    "question_vector": [0.1, 0.2, ...],  # 1024维向量
    "answer_vector": [0.3, 0.4, ...]     # 1024维向量
}
```

**文档块集合（Chunk Collection）：**
```python
{
    "id": "chunk_id",
    "organization_id": 1,
    "language": "zh-CN",
    "content": "文档块内容",
    "source": "文档来源",
    "vector": [0.5, 0.6, ...]  # 1024维向量
}
```

### 6.3 配置管理机制

#### 6.3.1 环境配置层次

系统采用多层次的配置管理：

1. **敏感配置**：`.env.secret`（API密钥等，不提交到版本控制）
2. **环境配置**：`.env.debug`/`.env.release`（根据IS_DEBUG标志选择）
3. **基础配置**：`.env.base`（与环境无关的基础配置）
4. **代码配置**：`app/config.py`（硬编码的默认值）

#### 6.3.2 动态配置热更新

<augment_code_snippet path="app/config.py" mode="EXCERPT">
````python
class Env:
    @classmethod
    def reload(cls):
        """动态重新加载配置"""
        load_env()
        # 重新加载各种配置项
        cls.OPENAI_API_KEY = get_env("OPENAI_API_KEY", "")
        cls.QUESTION_SIMILARITY_THRESHOLD = float(get_env("QUESTION_SIMILARITY_THRESHOLD", 0.67))
        # ... 其他配置项
````
</augment_code_snippet>

#### 6.3.3 组织级配置管理

每个组织可以有独立的配置：
- **LLM配置**：不同的模型和API设置
- **意图配置**：自定义的意图类型和描述
- **提示词配置**：个性化的提示词模板
- **UI配置**：自定义的格式化器和界面配置

## 7. 数据流向分析

### 7.1 完整数据处理流程

从用户输入到最终输出的完整数据流向：

#### 7.1.1 输入数据收集阶段
1. **用户输入文本**：原始的用户查询内容
2. **背景信息**：设备信息、用户偏好等上下文
3. **会话标识**：sessionId、deviceId、organizationId
4. **语言检测**：自动检测或用户指定的语言

#### 7.1.2 数据预处理阶段
1. **语言标准化**：统一语言代码格式
2. **输入清理**：去除无效字符和格式化
3. **上下文构建**：合并各种上下文信息
4. **历史加载**：从Store系统加载会话历史

#### 7.1.3 配置数据加载阶段
1. **组织配置**：从MySQL加载组织级配置
2. **意图配置**：加载意图类型和描述
3. **提示词配置**：加载多语言提示词模板
4. **LLM配置**：加载模型和API配置

#### 7.1.4 意图分析阶段
1. **提示词渲染**：使用Jinja2渲染意图检测提示词
2. **LLM调用**：调用意图检测模型
3. **结果解析**：解析JSON格式的意图和重写结果
4. **意图匹配**：与配置的意图列表进行匹配

#### 7.1.5 知识检索阶段（如果是知识库意图）
1. **向量化**：将问题转换为向量表示
2. **相似度搜索**：在Milvus中搜索相似的问答对和文档块
3. **阈值过滤**：根据相似度阈值过滤结果
4. **结果排序**：按相似度和相关性排序

#### 7.1.6 响应生成阶段
1. **上下文组装**：组装检索到的上下文信息
2. **提示词构建**：构建最终的生成提示词
3. **LLM生成**：调用生成模型产生回答
4. **后处理**：格式化和清理生成的内容

#### 7.1.7 输出数据封装阶段
1. **响应结构化**：封装为标准的Response格式
2. **流式传输**：转换为SSE格式进行流式传输
3. **历史更新**：将对话结果添加到消息历史
4. **日志记录**：记录关键的处理信息和性能指标

### 7.2 数据存储和缓存策略

#### 7.2.1 多层存储架构
- **L1缓存**：内存中的临时缓存（模板、配置等）
- **L2存储**：Store系统的会话级存储
- **L3存储**：MySQL的持久化存储
- **L4存储**：Milvus的向量存储

#### 7.2.2 数据一致性保证
- **事务控制**：数据库操作的事务一致性
- **缓存同步**：配置更新时的缓存失效机制
- **版本控制**：配置变更的版本管理
- **回滚机制**：异常情况下的数据回滚

### 7.3 性能监控和优化

#### 7.3.1 关键性能指标
- **响应时间**：端到端的响应延迟
- **吞吐量**：每秒处理的请求数量
- **缓存命中率**：各级缓存的命中率
- **错误率**：各个环节的错误率

#### 7.3.2 性能优化措施
- **预加载**：系统启动时预加载常用配置
- **批量处理**：数据库操作的批量优化
- **连接复用**：HTTP和数据库连接的复用
- **异步处理**：I/O密集型操作的异步化

## 8. 系统特色和技术亮点

### 8.1 多租户架构设计
- **组织级隔离**：每个组织拥有独立的配置、数据和模型设置
- **资源共享**：底层基础设施和服务在组织间共享，提高资源利用率
- **配置灵活性**：支持组织级别的个性化配置和定制

### 8.2 智能提示词系统
- **模板化管理**：基于Jinja2的强大模板系统
- **多语言支持**：自动语言检测和回退机制
- **动态渲染**：上下文感知的动态提示词生成
- **版本控制**：提示词的版本管理和A/B测试支持

### 8.3 多级知识库查询
- **智能降级**：从精确匹配到模糊检索的多级查询策略
- **阈值控制**：基于相似度阈值的智能决策
- **RAG优化**：检索增强生成的深度优化
- **去重机制**：智能的结果去重和排序

### 8.4 工具调用链支持
- **链式调用**：支持复杂的工具调用链
- **本地/远程**：统一的本地函数和远程API调用接口
- **安全控制**：调用次数限制和异常处理
- **状态管理**：工具调用过程的状态跟踪和清理

### 8.5 高可用性设计
- **多模型支持**：支持多种LLM模型的自动切换
- **重试机制**：智能的重试和降级策略
- **连接池**：数据库和HTTP连接的池化管理
- **监控告警**：完善的性能监控和异常告警

## 9. 部署和运维指南

### 9.1 环境要求
- **Python版本**：>= 3.8（推荐3.10.6）
- **数据库**：MySQL 5.7+
- **向量数据库**：Milvus 2.0+
- **硬件要求**：4核CPU，8GB内存，50GB存储空间

### 9.2 部署步骤
1. **环境准备**：安装uv包管理器
2. **依赖安装**：`uv pip install -e ".[dev]"`
3. **配置文件**：设置.env.secret和.env.debug
4. **数据库初始化**：自动创建表结构
5. **多语言编译**：`python app/lang/compile_translation.py`
6. **服务启动**：`uvicorn app.main:app --host 0.0.0.0 --port 8000`

### 9.3 监控和维护
- **日志监控**：使用结构化日志记录关键事件
- **性能监控**：监控响应时间、吞吐量等关键指标
- **资源监控**：监控CPU、内存、数据库连接等资源使用
- **异常告警**：设置关键异常的告警机制

## 10. 扩展和定制指南

### 10.1 添加新的LLM模型
1. 在`app/config.py`中添加新模型的配置
2. 在`app/services/llm_client.py`中实现模型适配
3. 更新模型列表和重试策略
4. 测试模型的兼容性和性能

### 10.2 扩展工具调用功能
1. 在`app/services/biz_handler.py`中添加新的工具函数
2. 定义工具的输入输出格式
3. 实现工具的错误处理和重试逻辑
4. 更新工具调用的文档和示例

### 10.3 添加新的意图类型
1. 在意图配置中定义新的意图类型
2. 创建对应的处理器类
3. 实现意图的检测逻辑和处理流程
4. 配置相应的提示词模板

### 10.4 多语言支持扩展
1. 在语言检测器中添加新语言的支持
2. 为新语言创建提示词模板
3. 更新语言映射和回退机制
4. 测试新语言的处理效果

## 11. 常见问题和解决方案

### 11.1 性能问题
**问题**：响应时间过长
**解决方案**：
- 检查LLM模型的响应时间
- 优化数据库查询和索引
- 增加缓存层减少重复计算
- 使用更快的向量检索算法

### 11.2 准确性问题
**问题**：意图检测不准确
**解决方案**：
- 优化意图检测的提示词
- 增加更多的意图训练样例
- 调整意图匹配的阈值
- 使用更强的意图检测模型

### 11.3 稳定性问题
**问题**：服务偶尔出现异常
**解决方案**：
- 完善异常处理和重试机制
- 增加服务健康检查
- 优化资源管理和连接池
- 设置合理的超时时间

## 12. 技术发展规划

### 12.1 短期规划（3-6个月）
- **性能优化**：进一步优化响应时间和吞吐量
- **功能增强**：添加更多的工具调用和集成
- **稳定性提升**：完善监控和告警系统
- **用户体验**：优化多语言支持和界面交互

### 12.2 中期规划（6-12个月）
- **AI能力升级**：集成更先进的AI模型和技术
- **知识库增强**：支持更复杂的知识图谱和推理
- **个性化定制**：基于用户行为的个性化推荐
- **企业级功能**：添加更多企业级的管理和安全功能

### 12.3 长期规划（1-2年）
- **智能化升级**：实现更智能的自动化和决策
- **生态系统**：构建完整的AI服务生态系统
- **标准化**：制定行业标准和最佳实践
- **国际化**：支持更多语言和地区的本地化

---

## 附录

### A. 关键配置参数说明

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| QUESTION_SIMILARITY_THRESHOLD | 0.67 | 问答相似度阈值 |
| CHUNK_SIMILARITY_THRESHOLD | 0.5 | 文档块相似度阈值 |
| DIRECT_RETURN_THRESHOLD | 0.98 | 直接返回阈值 |
| RAG_CHUNK_LIMIT | 3 | RAG使用的最大文档块数量 |
| MAX_ATTEMPTS_PER_MODEL | 2 | 每个模型的最大重试次数 |

### B. API接口文档

主要API接口：
- `POST /chat`：聊天对话接口
- `POST /translate`：翻译服务接口
- `POST /history/get`：获取历史记录
- `POST /push_config`：配置推送接口
- `POST /doc2vec`：文档向量化接口

### C. 错误代码说明

| 错误代码 | 说明 |
|----------|------|
| 2000-2999 | 业务逻辑错误 |
| 3000-3999 | 数据访问错误 |
| 4000-4999 | 外部服务错误 |
| 5000-5999 | 系统内部错误 |

### D. 性能基准测试

在标准测试环境下的性能指标：
- **平均响应时间**：< 2秒
- **并发处理能力**：100 QPS
- **缓存命中率**：> 90%
- **系统可用性**：> 99.9%

---

*本文档版本：v1.0*
*最后更新时间：2024年12月*
*文档维护者：AI服务开发团队*


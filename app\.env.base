# 向量搜索相关配置
## 搜索数量配置
QUESTION_TOP_K=5  # 问题搜索返回的最大结果数
CHUNK_TOP_K=10  # 文档块搜索返回的最大结果数

## 相似度阈值配置
QUESTION_SIMILARITY_THRESHOLD=0.6  # 问题相似度最小阈值
ANSWER_SIMILARITY_THRESHOLD=0.45  # 答案相似度最小阈值
CHUNK_SIMILARITY_THRESHOLD=0.45  # 文档块相似度最小阈值
DIRECT_RETURN_THRESHOLD=0.85  # 直接返回结果的相似度阈值

# QA处理配置
QUESTION_EXPAND_COUNT=5  # 问题扩写的目标数量
RAG_CHUNK_LIMIT=3  # RAG使用的最大文档块数量（历史配置，当前未使用）
EMBEDDING_TOKEN_LIMIT=8000  # embedding处理的最大token数

# ---
# 配置各功能使用的模型
# 编写方式：供应商.模型名字
# 供应商列表如下：openai, azure, zhipu, volcano, deepseek, aliyun
# ---

#####################################
# 海外配置 (必须选择其中一个)
#####################################
DEFAULT_BIZ_MODEL="openai.gpt-4o-2024-11-20"  # 业务处理模型
DEFAULT_RAG_MODEL="openai.gpt-4o-2024-11-20"  # RAG检索增强生成模型
DEFAULT_CHAT_MODEL="openai.gpt-4o-2024-11-20"  # 对话模型
DEFAULT_TRANSLATION_MODEL="openai.gpt-4o-2024-11-20"  # 翻译模型
DEFAULT_QUESTION_GENERATOR_MODEL="openai.gpt-4o-mini"  # 问题生成模型
DEFAULT_INTENTION_MODEL="openai.gpt-4o-2024-11-20"  # 意图识别模型
DEFAULT_EMBEDDING_MODEL="openai.text-embedding-3-small" # 文本向量化模型

#####################################
# 国产配置 (必须选择其中一个)
#####################################
# DEFAULT_BIZ_MODEL="qwen.qwen-plus-latest"  # 业务处理模型
# DEFAULT_RAG_MODEL="qwen.qwen-plus-latest"  # RAG检索增强生成模型
# DEFAULT_CHAT_MODEL="qwen.qwen-plus-latest"  # 对话模型
# DEFAULT_TRANSLATION_MODEL="qwen.qwen-plus-latest"  # 翻译模型
# DEFAULT_QUESTION_GENERATOR_MODEL="qwen.qwen-plus-latest"  # 问题生成模型
# DEFAULT_INTENTION_MODEL="qwen.qwen-plus-latest"  # 意图识别模型
# DEFAULT_EMBEDDING_MODEL="openai.text-embedding-3-small" # 文本向量化模型，暂时使用openai


DEFAULT_LLM_TEMPERATURE=0.3  # 模型温度参数

MAX_ATTEMPTS_PER_MODEL=2 #LLM API每个模型最大重试次数

# 调试和日志配置
## 时间统计打印配置
PRINT_EMBEDDING_TIME=True  # 打印嵌入时间
PRINT_SEARCH_TIME=True  # 打印搜索时间
PRINT_GENERATION_TIME=True  # 打印生成时间
PRINT_TOTAL_TIME=True  # 打印总耗时
PRINT_INTENTION_TIME=True  # 打印意图识别时间
PRINT_SESSION_ID=True  # 打印会话ID

## 其他调试配置
PRINT_INTENTION_RESULT=True  # 打印意图识别结果
PRINT_LLM_CONFIG=True  # 打印模型配置
PRINT_FETCH_CONFIG=false # 打印businessconfig配置
PRINT_PROMPT = false # 打印提示词
PRINT_LANGUAGE_DETECT = true # 打印 语言识别结果


# 已弃用的配置（仅作参考）
# RERANKER_QUESTION_THRESHOLD=0.6
# RERANKER_CHUNK_THRESHOLD=-3

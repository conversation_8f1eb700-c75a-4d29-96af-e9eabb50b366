# 该文件接口用于对历史记录进行操作

from fastapi import APIRouter
from app.services.ai_chat import get_ai_chat
from typing import List, Dict
from pydantic import BaseModel

router = APIRouter()

class GetHistoryRequest(BaseModel):
    organizationId: int
    sessionId: str

class SetHistoryRequest(BaseModel):
    organizationId: int
    history: List[Dict]

# 获取历史消息记录
@router.post("/history/get")
async def get_history(request: GetHistoryRequest):
    ai_chat = await get_ai_chat(request.organizationId)
    history = await ai_chat.message_history.get_history(request.sessionId)
    return history

# 设置历史消息记录
"""
history 格式:
[
    {
        "role": "user",
        "content": "你好"
    },
    {
        "role": "assistant",
        "content": "你好，请问有什么可以帮您的吗？"
    }
]
"""
@router.post("/history/set")
async def set_history(request: SetHistoryRequest):
    ai_chat = await get_ai_chat(request.organizationId)
    session_id = await ai_chat.message_history.set_history(request.history)
    return {
        "sessionId": session_id
    }

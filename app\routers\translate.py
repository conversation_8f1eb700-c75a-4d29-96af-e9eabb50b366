from fastapi import APIRouter, HTTPException, Request, Depends
from pydantic import BaseModel
from typing import List, Dict, Union
import asyncio  # 添加这行导入
from app.services.ai_translate import translate_text, translate_qa_pair
from app.utils.language_detect import LanguageDetector
import json
from fastapi.responses import StreamingResponse
from app.utils.net_utils import (
    Response, 
    RESPONSE_CODE_CONTINUE, 
    RESPONSE_CODE_END,
    ERROR_TRANSLATE_SERVICE
)
from app.utils.logger import log_text
from app.services import DEVICE_BIZ_SERVER

router = APIRouter()


class TranslateRequest(BaseModel):
    text: Union[str, List[str]]  # 修改为支持字符串或字符串列表
    target_languages: List[str]
    translation_requirements: str = None


class TranslateResponse(BaseModel):
    detected_language: Union[str, List[str]]
    translations: Union[
        Dict[str, str],  # 用于单个文本的翻译
        List[Dict[str, str]]  # 用于多个文本的翻译
    ]


class QATranslateRequest(BaseModel):
    questions: Union[List[str], str]  # 可以是单个问题或问题列表
    answer: Union[str, List[str], None]  # 修改为支持字符串、字符串列表或None
    target_languages: List[str]
    expand_questions: bool = False
    translation_requirements: str = None

    def __str__(self):
        return f"questions: {self.questions}, answer: {self.answer}, target_languages: {self.target_languages}, expand_questions: {self.expand_questions}, translation_requirements: {self.translation_requirements}"


class QATranslateResponse(BaseModel):
    detected_language: str
    data: Dict[str, Dict[str, Union[List[str], str]]]


class DetectLanguageRequest(BaseModel):
    text: Union[str, List[str]]  # 修改为支持字符串或字符串列表


class DetectLanguageResponse(BaseModel):
    detected_language: Union[str, List[str]]  # 修改为支持单个结果或结果列表


def get_request_language(request: Request) -> str:
    language = request.state.babel.locale
    if language:
        language = language.replace("_", "-")
    return language or "zh-CN"


@router.post("/translate")
async def translate(request: TranslateRequest, language: str = Depends(get_request_language)):
    """
    将文本翻译成多种目标语言，支持单个文本或文本列表，使用SSE流式输出保活信息
    """
    async def generate_response():
        try:
            # 添加语言检测
            detector = LanguageDetector()
            
            # 处理单个文本或文本列表的语言检测
            texts = request.text if isinstance(request.text, list) else [request.text]
            detected_languages = [
                detector.detect_language(text, default_lang=language)
                for text in texts
            ]
            
            detected_language = (
                detected_languages[0] 
                if not isinstance(request.text, list) 
                else detected_languages
            )
            
            # 创建翻译任务
            translation_task = asyncio.create_task(
                translate_text(
                    text=request.text,
                    target_languages=request.target_languages,
                    detected_language=detected_language,
                    translation_requirements=request.translation_requirements
                )
            )
            
            # 每秒发送保活信息，直到翻译完成
            while not translation_task.done():
                yield Response(
                    code=RESPONSE_CODE_CONTINUE,
                    data={"message": "Translation in progress..."}
                ).to_sse()
                await asyncio.sleep(5)
            
            # 获取翻译结果
            result = await translation_task
            
            # 发送最终结果
            yield Response(
                code=RESPONSE_CODE_END,
                data=result
            ).to_sse()
            
        except Exception as e:
            yield Response(
                code=ERROR_TRANSLATE_SERVICE,
                errorMsg=str(e)
            ).to_sse()

    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream"
    )


@router.post("/translate/qa")
async def translate_qa(
    request: QATranslateRequest, 
    language: str = Depends(get_request_language)
):
    """
    翻译问答对，可选择是否扩写问题，使用SSE流式输出保活信息
    """

    log_text(DEVICE_BIZ_SERVER, f"translate_qa request: {request}")
    async def generate_response():
        try:
            # 将questions统一处理为列表格式
            questions = request.questions if isinstance(request.questions, list) else [request.questions]
            
            # 添加语言检测
            detector = LanguageDetector()
            
            # 构建用于检测的文本：将所有问题和答案合并
            detect_text = " ".join(questions)
            if request.answer:
                answers = request.answer if isinstance(request.answer, list) else [request.answer]
                detect_text += " " + " ".join(answers)
                
            # 检测合并后文本的语言
            detected_language = detector.detect_language(detect_text, default_lang=language)
            
            # 创建翻译任务
            translation_task = asyncio.create_task(
                translate_qa_pair(
                    questions=questions,
                    answer=request.answer,
                    target_languages=request.target_languages,
                    source_language=detected_language,
                    expand_questions=request.expand_questions,
                    translation_requirements=request.translation_requirements
                )
            )
            
            # 每秒发送保活信息，直到翻译完成
            while not translation_task.done():
                yield Response(
                    code=RESPONSE_CODE_CONTINUE,
                    data={"message": "QA translation in progress..."}
                ).to_sse()
                await asyncio.sleep(5)
            
            # 获取翻译结果
            result = await translation_task
            
            # 发送最终结果
            response = Response(
                code=RESPONSE_CODE_END,
                data=result
            )
            
            log_text(DEVICE_BIZ_SERVER, f"translate_qa response: {response}")
            yield response.to_sse()
             
        except Exception as e:
            yield Response(
                code=ERROR_TRANSLATE_SERVICE,
                errorMsg=str(e)
            ).to_sse()

    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream"
    )


@router.post("/detect-language", response_model=DetectLanguageResponse)
async def detect_language(
    request: DetectLanguageRequest, 
    language: str = Depends(get_request_language)
):
    """
    检测输入文本的语言，支持单个文本或文本列表
    """
    try:
        detector = LanguageDetector()
        # 处理输入是列表的情况
        if isinstance(request.text, list):
            detected_languages = [
                detector.detect_language(text, default_lang=language)
                for text in request.text
            ]
            return DetectLanguageResponse(detected_language=detected_languages)
        
        # 处理输入是单个字符串的情况
        detected_language = detector.detect_language(
            request.text,
            default_lang=language
        )
        return DetectLanguageResponse(detected_language=detected_language)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
import subprocess
import os
# 更新翻譯文件
# 在工程中有新的_("...")時，运行此脚本，會在app/translations下生成新的messages.po文件
# 对新增文本进行翻译
# 翻译完成后再运行compile_translation.py文件，将po文件编译为mo文件
def update_translations():
    # 獲取項目根目錄
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 設置工作目錄為項目根目錄
    os.chdir(project_root)

    # 提取需要翻譯的字符串
    subprocess.run([
        "pybabel", "extract",
        "-F", "babel.cfg",
        "-o", "app/translations/messages.pot",
        "."
    ])

    # 更新翻譯文件
    subprocess.run([
        "pybabel", "update",
        "-i", "app/translations/messages.pot",
        "-d", "app/translations"
    ])

    print("Translation files updated.")

def init_language(lang_code):
    # 初始化新語言的翻譯文件
    subprocess.run([
        "pybabel", "init",
        "-i", "app/translations/messages.pot",
        "-d", "app/translations",
        "-l", lang_code
    ])
    print(f"Translation file for {lang_code} initialized.")

if __name__ == '__main__':
    update_translations()
    
    # 如果需要初始化新語言，取消註釋並指定語言代碼
    # init_language("en")
    # init_language("zh_CN")
    # init_language("zh_HK")
    # init_language("ar")
    # init_language("ms")


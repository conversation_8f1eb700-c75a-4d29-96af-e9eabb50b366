from typing import Dict, Optional, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from app.base.jinja2_formatter import prompt_format
from fastapi_babel import _
from app.routers.chat import get_request_language
from fastapi import Depends

router = APIRouter()

class PromptRequest(BaseModel):
    template: str # 提示词模板
    context: Dict # 变量字典
    language: Optional[str] = "zh-CN" # 语言

class PromptResponse(BaseModel):
    code: int
    data: dict
    errorMsg: str = ""
    language: str | None = None


@router.post("/prompt/format", response_model=PromptResponse)
async def get_formatted_prompt(request: PromptRequest, language: str = Depends(get_request_language)) -> PromptResponse:
    try:
        result = prompt_format(
            template_str=request.template,
            context=request.context,
            language=language,
        )
        return PromptResponse(code=0, data={"result": result}, language=language)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=_("提示词模板化失败: {}").format(str(e))
        )
from lingua import Language, LanguageDetectorBuilder
from typing import Optional, ClassVar
import re
import opencc
from app.config import Env


class LanguageDetector:
    # 类变量用于存储共享的实例
    _instance: ClassVar[Optional["LanguageDetector"]] = None
    _initialized: bool = False

    def __new__(cls):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化语言检测器，使用单例模式确保只初始化一次"""
        if not self._initialized:
            # 初始化 Lingua 检测器
            self.detector = LanguageDetectorBuilder.from_languages(
                Language.CHINESE,
                Language.ENGLISH,
                Language.ARABIC,
                Language.MALAY
            ).build()

            # 保留中文检测相关的初始化
            self.chinese_range = re.compile(r"[\u4e00-\u9fff]")
            self.converter_t2s = opencc.OpenCC("t2s")
            self.converter_s2t = opencc.OpenCC("s2t")
            
            self._initialized = True

    def detect_language(self, query: str, default_lang: str = "en") -> str:
        """
        检测输入文本的语言类型
        优先处理中文繁简体判断，其他语言使用 Lingua
        
        Args:
            query: 要检测的文本
            default_lang: 默认语言，特别用于：
                         1. 无法确定语言时的返回值
                         2. 中文检测中用户指定的偏好（简/繁体）
        """
        # 空文本或纯标点符号处理
        if not query or not query.strip():
            return default_lang
            
        # 检查是否包含中文字符
        chinese_chars = len(self.chinese_range.findall(query))
        if chinese_chars > 0:
            try:
                # 将原文本转为简体和繁体版本
                traditional_text = self.converter_s2t.convert(query)
                simplified_text = self.converter_t2s.convert(query)
                
                # 1. 检查是否有任何一个字符是繁体特有的
                if query != simplified_text:
                    return "zh-HK"  # 有繁体字符，判定为繁体中文
                
                # 2. 检查是否所有字符在简繁体中都相同
                if traditional_text == simplified_text:
                    # 如果完全相同，遵循用户的语言偏好
                    return default_lang if default_lang in ["zh-CN", "zh-HK"] else "zh-CN"
                    
                # 3. 此时文本都是简体字，且这些字在繁体中有不同写法
                return "zh-CN"
            except Exception:
                # 异常时也遵循用户的语言偏好
                return default_lang if default_lang in ["zh-CN", "zh-HK"] else "zh-CN"

        # 其他语言使用 Lingua 检测
        try:
            result = self.detector.detect_language_of(query)

            # 打印语言检测结果
            if Env.PRINT_LANGUAGE_DETECT:
                print(f"[Language Detect] Lingua Result: {result}")
                confidence_values = self.detector.compute_language_confidence_values(query)
                for confidence in confidence_values:
                    print(f"[Language Confidence] {confidence.language.name}: {confidence.value:.2f}")

            if result is None:
                return default_lang

            # 语言代码映射
            lang_map = {
                Language.CHINESE: (
                    # 如果检测到中文，遵循用户的语言偏好
                    default_lang if default_lang in ["zh-CN", "zh-HK"] else "zh-CN"
                ),
                Language.ENGLISH: "en",
                Language.ARABIC: "ar",
                Language.MALAY: "ms"
            }
            
            # 计算置信度
            confidence = self.detector.compute_language_confidence(query, result)

            # 根据用户默认语言设置不同的置信度阈值
            THRESHOLD = 0.5 if default_lang in ["zh-CN", "zh-HK"] else 0.8

            # 如果默认语言是中文，文本没有汉字，且置信度小于0.5，则返回中文。大于0.5则返回识别出来的西文
            # 就是说文本没有汉字时，中文用户如果用西文输入的话则更倾向于返回识别出来的西文
        
            # 如果默认语言不是中文，文本没有汉字，置信度小于0.8，则返回默认语言
            # 就是说文本没有汉字时，西文用户更倾向于返回自己的默认语言
            return default_lang if confidence < THRESHOLD else lang_map.get(result, default_lang)
            
        except Exception:
            return default_lang

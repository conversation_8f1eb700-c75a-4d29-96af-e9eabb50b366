{"pages": {"1.2-Greeting-Dialogue-Page": {"name": {"zh-CN": "打招呼对话页", "en": "Greeting Dialogue Page"}, "description": {"zh-CN": "在这个页面，自助机将会引导用户登录也可以回答用户的疑问以及解决用户的各种问题。假如你发现历史聊天记录里面assistant曾回复\"签到需要先登录，请问您想使用什么方式登录\"类似的话，说白了就是assistant曾经反问过用户想要用什么方式登录。这种情况下，用户说身份证，身份证登录，我们都不应该选择button而是选择login。", "en": "On this page, the self-service machine will guide users to log in and answer their questions and solve their various problems. If you find that the assistant has replied in the historical chat record that \"You need to log in first to sign in. How do you want to log in?\", it means that the assistant has asked the user how he wants to log in. In this case, if the user says ID card, ID card login, we should not select button but login."}, "buttonList": ["medical_insurance_face_recognition_login", "medical_insurance_card", "electronic_medical_insurance_login", "id_card_login", "medical_visit_card_login", "passport_scan", "electronic_health_card", "hong_kong_and_macau_resident_permit", "medical_visit_barcode", "simplified_chinese", "english"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding", "login"], "directReply": ["sensitive", "irrelevant"]}, "2.1-menu-page": {"name": {"zh-CN": "菜单页", "en": "<PERSON><PERSON>"}, "description": {"zh-CN": "在这个页面里面，自助机将会引导用户挂号也可以回答用户的疑问以及解决用户的各种问题。请注意在这个页面里如果用户表达了想要缴费，打印报告，打印发票和取号的想法。请直接选择对应的按钮。如果是建档，请选择record。如果用户说签到视同于取号。如果用户说我要挂号，或者挂号，请直接选择对应的按钮意图。除非用户询问要挂什么科室才会选择导诊。", "en": "On this page, the self-service machine will guide users to register, answer their questions, and resolve various issues. Please note that on this page, if the user expresses the desire to pay, print report, print invoice and get number, please select the corresponding button directly. If it is for creating a file, please select record. If the user states that check-in is equivalent to taking a number. If the user says \"I want to register\" or \"register,\" directly select the corresponding button intent. Only choose triage if the user asks which department to register for."}, "buttonList": ["register", "payment", "inpatient_recharge", "medical_appointment", "invoice_printing", "report_printing", "self_service_filing", "general_inquiry", "satisfaction_evaluation", "hospital_introduction", "to_do_list", "back", "exit", "ignore", "get_number"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding", "record"], "directReply": ["sensitive", "irrelevant"]}, "3.1-Select-Department-Page": {"name": {"zh-CN": "选择科室页", "en": "Select Department Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户选择挂号的科室", "en": "On this page, the self-service machine will guide users to select the department for registration"}, "buttonList": ["all", "page_up", "page_down", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding"], "directReply": ["sensitive", "irrelevant"]}, "3.2-Select-Time-Page": {"name": {"zh-CN": "选择时间页", "en": "Select Time Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户选择挂号的时间和医生。如果用户只说了周一，周二这种的指令，我们可以当做是点击了按钮。但是如果用户说了具体的医生名字和具体的日期，请走registration意图，因为对具体日期和医生的逻辑处理，我们需要在那里完成。", "en": "On this page, the self-service machine guides users to select appointment times and doctors. If the user only provides instructions like 'Monday' or 'Tuesday,' we can treat it as if they clicked a button. However, if the user specifies a particular doctor's name and a specific date, please proceed with the 'registration' action. This is because the logic processing for specific dates and doctors needs to be completed within that action."}, "buttonList": ["friday", "saturday", "sunday", "monday", "tuesday", "wednesday", "thursday", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "registration", "way-finding"], "directReply": ["sensitive", "irrelevant"], "text": {"en": "The user has selected the {department} department for an appointment on {date} in the Time Selection Page.", "zh-CN": "用户在选择时间页面已选择在{date}前往{department}就诊。", "zh-HK": "用戶在選擇時間頁面已選擇在{date}前往{department}就診。"}}, "3.3-Confirm-Registration-Information-Page": {"name": {"zh-CN": "确认挂号信息页", "en": "Confirm Registration Information Page"}, "description": {"zh-CN": "在这个页面里面，自助机想引导用户确认自己的挂号信息", "en": "On this page, the self-service machine intends to guide users to confirm their registration information"}, "buttonList": ["back", "exit"], "optionalActions": ["knowledge", "chat", "query", "registration", "way-finding"], "directReply": ["sensitive", "irrelevant"]}, "3.4-Registration-Process-Payment-Method-Selection-Page": {"name": {"zh-CN": "挂号流程支付方式选择页", "en": "Registration Process Payment Method Selection Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户选择支付方式", "en": "On this page, the self-service machine will guide users to select a payment method"}, "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "way-finding"], "directReply": ["sensitive", "irrelevant"]}, "3.5-Scan-Code-Page": {"name": {"zh-CN": "扫码页", "en": "Scan Code Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户扫描二维码支付", "en": "On this page, the self-service machine will guide users to scan a QR code for payment"}, "buttonList": ["cancel"], "optionalActions": ["knowledge", "chat", "query", "way-finding"], "directReply": ["sensitive", "irrelevant"]}, "3.6-Registration-Success-Page": {"name": {"zh-CN": "挂号成功页", "en": "Registration Success Page"}, "description": {"zh-CN": "在这个页面里面，自助机会引导用户取走小票，并在用户拿走小票后给用户导航", "en": "On this page, the self-service machine will guide the user to take the receipt, and after the user takes the receipt, it will provide navigation for the user"}, "buttonList": ["confirm", "location_navigation"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding"], "directReply": ["sensitive", "irrelevant"]}, "3.7-Navigation-Page": {"name": {"zh-CN": "导航页", "en": "Navigation Page"}, "description": {"zh-CN": "在这个页面里面，自助机会帮用户导航", "en": "On this page, the self-service machine will help the user navigate"}, "buttonList": ["exit"], "optionalActions": ["knowledge", "chat", "query", "registration", "medical-guidance", "way-finding"], "directReply": ["sensitive", "irrelevant"]}, "3.9-Select-Time-Period-Page": {"name": {"zh-CN": "选时间段页", "en": "Select Time Period Page"}, "description": {"zh-CN": "在这个页面里面，用户可以选择挂号的具体时间段。如果用户要选择具体的时间，或者修改挂号信息，请走registration。", "en": "In this page, users can select the specific time period for registration. If users want to select a specific time or modify registration information, please go to registration."}, "buttonList": ["cancel", "confirm"], "optionalActions": ["knowledge", "chat", "query", "registration"], "directReply": ["sensitive", "irrelevant"], "text": {"en": "The user has selected Dr. {doctor} at the {department} department for an appointment on {date} in the Time Period Selection Page.", "zh-CN": "用户在选择时间段页面已选择在{date}由{doctor}医生在{department}进行诊疗。", "zh-HK": "用戶在選擇時間段頁面已選擇在{date}由{doctor}醫生在{department}進行診療。"}}, "3.10-Confirm-And-Select-Payment-Method-Page": {"name": {"zh-CN": "确认并选支付方式页", "en": "Confirm And Select Payment Method Page"}, "description": {"zh-CN": "在这个页面里面，用户可以确认挂号详细信息，并选择支付方式，在这个页面里面对于支付的方式，我们统一使用Dir-button的逻辑。如果你觉得用户是在表达想用某种方式完成支付，请直接选择对应的按钮。如果用户想要修改挂号信息，例如想要更换医生，科室，时间，请走registration。", "en": "On this page, users can review their appointment details and select a payment method. For payment options, we consistently follow the Dir-button logic. If you believe the user intends to pay using a specific method, directly select the corresponding button. If the user wants to modify appointment details, such as changing the doctor, department, or time, please proceed through the registration process."}, "buttonList": ["cash", "unionpay_card", "alipay", "wechat", "medical_insurance", "face_scan_payment_alipay", "face_scan_payment_wechat", "back", "exit"], "optionalActions": ["knowledge", "chat", "query", "registration"], "directReply": ["sensitive", "irrelevant"], "text": {"en": "The user has booked an appointment with Dr. {doctor} at the {department} department on {date}, {time_slot} in the Payment Confirmation Page.", "zh-CN": "用户在确认支付页面已预约{date} {time_slot}在{department}由{doctor}医生进行诊疗。", "zh-HK": "用戶在確認支付頁面已預約{date} {time_slot}在{department}由{doctor}醫生進行診療。"}}}}
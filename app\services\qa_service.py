from typing import List, Dict, AsyncGenerator
from app.services.milvus_service import QAUpsertItem, batch_upsert_qa_pairs, delete_questions_by_qna_pair_ids, get_organization_qa_pairs
from app.utils.logger import log_exception
from app.services import DEVICE_AI_SERVER
import asyncio
import random
import time
import json
from asyncio import Semaphore
from app.utils.net_utils import (
    wrap_response,
    wrap_error,
    RESPONSE_CODE_END,
    ERROR_AI_SERVER,
    RESPONSE_CODE_SEGMENT_END,
    Response,
)
from app.config import (
    MAX_LANGUAGE_LENGTH,
    MAX_QUESTION_LENGTH,
    MAX_ANSWER_LENGTH,
    MAX_EMBEDDING_TOKENS
)
from app.utils.token_utils import TokenCalculator
from app.services.llm_client import get_embedding
from app.services.mysql_service import get_embedding_config

async def batch_upsert_qa_pairs_service(
    organization_id: int,
    items: List[dict]
) -> AsyncGenerator[Response, None]:
    # 验证数据合法性
    validation_errors = []
    qa_pair_operations = {}  # 记录每个(qa_pair_id, language)组合的操作类型
    
    for item in items:
        item_errors = []  # 收集当前item的所有错误
        qa_pair_id = item["qa_pair_id"]
        language = item["language"]
        key = (qa_pair_id, language)
        operation = "delete" if not item["questions"] else "upsert"
        
        # 1. 检查语言长度
        if len(language.encode('utf-8')) > MAX_LANGUAGE_LENGTH:
            item_errors.append({
                "type": "language_length_exceeded",
                "detail": {
                    "current_bytes": len(language.encode('utf-8')),
                    "limit_bytes": MAX_LANGUAGE_LENGTH,
                    "language": language
                }
            })
        
        # 2. 检查是否存在重复操作
        if key in qa_pair_operations:
            item_errors.append({
                "type": "duplicate_operation",
                "detail": {
                    "current_operation": operation,
                    "existing_operation": qa_pair_operations[key]
                }
            })
        qa_pair_operations[key] = operation
        
        # 3. 检查问题长度
        if item["questions"]:
            invalid_questions = []
            for i, question in enumerate(item["questions"]):
                question_bytes = len(question.encode('utf-8'))
                if question_bytes > MAX_QUESTION_LENGTH:
                    invalid_questions.append({
                        "index": i,
                        "current_bytes": question_bytes,
                        "question": question[:50] + "..." if len(question) > 50 else question
                    })
            
            if invalid_questions:
                item_errors.append({
                    "type": "question_length_exceeded",
                    "detail": {
                        "limit_bytes": MAX_QUESTION_LENGTH,
                        "questions": invalid_questions
                    }
                })
        
        # 4. 检查答案长度
        if item["answer"]:
            answer_bytes = len(item["answer"].encode('utf-8'))
            if answer_bytes > MAX_ANSWER_LENGTH:
                item_errors.append({
                    "type": "answer_length_exceeded",
                    "detail": {
                        "current_bytes": answer_bytes,
                        "limit_bytes": MAX_ANSWER_LENGTH,
                        "answer": item["answer"][:50] + "..." if len(item["answer"]) > 50 else item["answer"]
                    }
                })
        
        # 如果当前item有错误，添加到总错误列表
        if item_errors:
            validation_errors.append({
                "qa_pair_id": qa_pair_id,
                "language": language,
                "errors": item_errors
            })
    
    # 如果存在任何验证错误
    if validation_errors:
        error_message = {
            "code": "VALIDATION_ERROR",
            "message": f"Found {len(validation_errors)} invalid items",
            "total_invalid_items": len(validation_errors),
            "invalid_items": validation_errors
        }
        error_msg = json.dumps(error_message, ensure_ascii=False)
        # 创建一个包含完整错误信息的异常
        validation_error = ValueError(error_msg)
        # 记录验证错误日志
        log_exception(DEVICE_AI_SERVER, validation_error, "QA对数据验证失败")
        yield wrap_error(ERROR_AI_SERVER, error_msg)
        raise validation_error  # 抛出同一个异常实例

    # 修改状态跟踪的初始化
    summary = {
        "success_by_lang": {},    # 用于累积所有成功的更新
        "failed_by_lang": {},     # 用于累积所有失败的更新
        "total_tokens": 0,
        "error_details": [],
        "by_operation": {         
            "upsert": {"success": 0, "failed": 0},
            "delete": {"success": 0, "failed": 0}
        },
        "delete_success_by_lang": {},
        "delete_failed_by_lang": {}
    }
    
    try:
        # 分离删除操作和更新操作
        delete_pairs = []
        update_items = []
        
        for item in items:
            if not item["questions"]:
                delete_pairs.append((item["qa_pair_id"], item["language"]))
            else:
                update_items.append(item)

        # 处理删除操作
        if delete_pairs:
            # 按语言分组需要删除的ID
            delete_by_lang = {}
            for qa_pair_id, language in delete_pairs:
                if language not in delete_by_lang:
                    delete_by_lang[language] = []
                delete_by_lang[language].append(qa_pair_id)

            try:
                # 执行删除并记录结果
                for language, ids in delete_by_lang.items():
                    try:
                        deleted_count = delete_questions_by_qna_pair_ids(
                            organization_id=organization_id,
                            qna_pair_ids=ids,
                            language=language
                        )
                        # 记录到专门的删除成功跟踪中
                        if language not in summary["delete_success_by_lang"]:
                            summary["delete_success_by_lang"][language] = []
                        summary["delete_success_by_lang"][language].extend(ids)
                    except Exception as e:
                        # 记录到专门的删除失败跟踪中
                        if language not in summary["delete_failed_by_lang"]:
                            summary["delete_failed_by_lang"][language] = []
                        summary["delete_failed_by_lang"][language].extend(ids)
                        raise

                # 在处理删除操作时更新操作统计
                if summary["delete_success_by_lang"]:
                    summary["by_operation"]["delete"]["success"] += sum(len(ids) for ids in summary["delete_success_by_lang"].values())
                if summary["delete_failed_by_lang"]:
                    summary["by_operation"]["delete"]["failed"] += sum(len(ids) for ids in summary["delete_failed_by_lang"].values())

                yield wrap_response(
                    {
                        "operation": "delete",
                        "success_ids": summary["delete_success_by_lang"],
                        "failed_ids": summary["delete_failed_by_lang"]
                    },
                    code=RESPONSE_CODE_SEGMENT_END
                )
            except Exception as e:
                yield wrap_error(ERROR_AI_SERVER, str(e))
                raise

        # 处理更新操作
        if not update_items:
            yield wrap_response(
                {
                    "summary": {
                        "delete": {
                            "success_ids": summary["delete_success_by_lang"],
                            "failed_ids": summary["delete_failed_by_lang"]
                        },
                        "upsert": {
                            "success_ids": {},
                            "failed_ids": {}
                        },
                        "stats": {
                            "total_processed": sum(len(ids) for ids in summary["delete_success_by_lang"].values()),
                            "total_failed": sum(len(ids) for ids in summary["delete_failed_by_lang"].values()),
                            "total_tokens": 0
                        }
                    }
                },
                code=RESPONSE_CODE_END
            )
            return

        async def process_batch(batch_items, batch_texts):
            batch_tokens = TokenCalculator.calculate_tokens(batch_texts)
            success_by_lang = {}
            
            try:
                # 获取embeddings
                embedding_config = get_embedding_config(organization_id)
                batch_embeddings = await get_embedding(
                    text=batch_texts, 
                    embedding_config=embedding_config,
                    caller_module="qa_service",
                )
                
                # 构建QA items
                qa_items = []
                text_mapping = {}
                current_idx = 0
                
                for idx, item in enumerate(batch_items):
                    # 构建text_mapping
                    start_idx = current_idx
                    current_idx += len(item["questions"])
                    text_mapping[idx] = {
                        "question_start": start_idx,
                        "question_end": current_idx,
                        "answer_idx": current_idx
                    }
                    current_idx += 1

                    # 构建QA item
                    qa_items.append(QAUpsertItem(
                        qa_pair_id=item["qa_pair_id"],
                        language=item["language"],
                        questions=item["questions"],
                        answer=item["answer"],
                        question_embeddings=batch_embeddings[text_mapping[idx]["question_start"]:text_mapping[idx]["question_end"]],
                        answer_embedding=batch_embeddings[text_mapping[idx]["answer_idx"]]
                    ))

                # 执行批量更新
                batch_upsert_qa_pairs(
                    organization_id=organization_id,
                    items=qa_items
                )

                # 更新成功后才记录成功的ID
                for item in batch_items:
                    lang = item["language"]
                    if lang not in success_by_lang:
                        success_by_lang[lang] = []
                    success_by_lang[lang].append(item["qa_pair_id"])

                # 返回成功结果
                return {
                    "success_ids": success_by_lang,
                    "failed_ids": {},
                    "batch_tokens": batch_tokens
                }
            except Exception as e:
                # 发生错误时，将所有ID按语言分组记录为失败
                failed_by_lang = {}
                for item in batch_items:
                    lang = item["language"]
                    if lang not in failed_by_lang:
                        failed_by_lang[lang] = []
                    failed_by_lang[lang].append(item["qa_pair_id"])
                
                raise Exception(str(e))  # 直接抛出异常，让上层处理

        # 修改批处理逻辑，实现真正的并行
        semaphore = Semaphore(3)
        pending_batches = []
        current_batch = []
        current_texts = []

        for item in update_items:
            item_texts = item["questions"] + [item["answer"]]
            new_texts = current_texts + item_texts
            
            # 同时检查token数量和文本数量限制(embedding model最多支持10个输入)
            if (TokenCalculator.calculate_tokens(new_texts) > MAX_EMBEDDING_TOKENS or 
                len(new_texts) > 10) and current_batch:
                # 将满足条件的批次加入待处理列表
                pending_batches.append((current_batch.copy(), current_texts.copy()))
                current_batch = [item]
                current_texts = item_texts
            else:
                current_batch.append(item)
                current_texts = new_texts

        # 添加最后一个批次
        if current_batch:
            pending_batches.append((current_batch, current_texts))

        # 并行处理所有批次
        async def process_pending_batch(batch_data):
            batch_items, batch_texts = batch_data
            async with semaphore:
                return await process_batch(batch_items, batch_texts)

        # 使用 gather 并行处理所有批次
        batch_results = await asyncio.gather(
            *(process_pending_batch(batch_data) for batch_data in pending_batches),
            return_exceptions=True
        )

        # 处理结果
        for result in batch_results:
            if isinstance(result, Exception):
                yield wrap_error(ERROR_AI_SERVER, str(result))
            else:
                # 更新汇总信息
                for lang, ids in result["success_ids"].items():
                    if lang not in summary["success_by_lang"]:
                        summary["success_by_lang"][lang] = []
                    summary["success_by_lang"][lang].extend(ids)
                    
                for lang, ids in result.get("failed_ids", {}).items():
                    if lang not in summary["failed_by_lang"]:
                        summary["failed_by_lang"][lang] = []
                    summary["failed_by_lang"][lang].extend(ids)
                    
                # 更新总token数
                summary["total_tokens"] += result.get("batch_tokens", 0)
                
                # 更新操作统计
                summary["by_operation"]["upsert"]["success"] += len([
                    id for ids in result["success_ids"].values() for id in ids
                ])
                summary["by_operation"]["upsert"]["failed"] += len([
                    id for ids in result.get("failed_ids", {}).values() for id in ids
                ])
                
                yield wrap_response(
                    {
                        "operation": "upsert",
                        **result
                    },
                    code=RESPONSE_CODE_SEGMENT_END
                )

        # 最终汇总时包含所有累积的结果
        yield wrap_response(
            {
                "summary": {
                    "delete": {
                        "success_ids": summary["delete_success_by_lang"],
                        "failed_ids": summary["delete_failed_by_lang"]
                    },
                    "upsert": {
                        "success_ids": summary["success_by_lang"],
                        "failed_ids": summary["failed_by_lang"]
                    },
                    "stats": {
                        "total_processed": (
                            sum(len(ids) for ids in summary["success_by_lang"].values()) +
                            sum(len(ids) for ids in summary["delete_success_by_lang"].values())
                        ),
                        "total_failed": (
                            sum(len(ids) for ids in summary["failed_by_lang"].values()) +
                            sum(len(ids) for ids in summary["delete_failed_by_lang"].values())
                        ),
                        "total_tokens": summary["total_tokens"]
                    }
                }
            },
            code=RESPONSE_CODE_END
        )

    except Exception as e:
        error_msg = log_exception(DEVICE_AI_SERVER, e, "批量更新QA对失败")
        yield wrap_error(ERROR_AI_SERVER, error_msg)
        raise

async def _test_batch_upsert():
    """测试批量更新QA对服务的非pytest测试函数"""
    # 模拟数据
    organization_id = 1998
    languages = ["en", "zh"]
    test_items = []
    
    # 生成500个QA对，确保会产生多个批次
    for i in range(500):
        num_questions = random.randint(1, 5)
        questions = [
            f"这是第{i}组的第{j}个问题，包含一些随机文本：{''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=50))}"
            for j in range(num_questions)
        ]
        answer = f"这是第{i}组的答案，包含大量随机文本：{''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=200))}"
        
        test_items.append({
            "qa_pair_id": i,
            "language": random.choice(languages),
            "questions": questions,
            "answer": answer
        })
    
    # 添加一些删除操作
    for i in range(5):
        test_items.append({
            "qa_pair_id": 1000 + i,
            "language": random.choice(languages),
            "questions": [],  # 空问题列表表示删除操作
            "answer": ""
        })
    
    print(f"[测试] 总共生成 {len(test_items)} 个测试项")
    
    try:
        start_time = time.time()
        print("\n[开始处理]")
        print("=" * 50)

        async for response in batch_upsert_qa_pairs_service(organization_id, test_items):
            print("\n[响应]")
            if not response.success:
                print(f"错误: {response.errorMsg}")
                continue

            if response.code == RESPONSE_CODE_END:
                print("\n[最终汇总]")
                if response.data and "summary" in response.data:
                    summary = response.data["summary"]
                    print("删除操作:")
                    print(f"- 成功: {json.dumps(summary['delete']['success_ids'], indent=2, ensure_ascii=False)}")
                    print(f"- 失败: {json.dumps(summary['delete']['failed_ids'], indent=2, ensure_ascii=False)}")
                    print("\n更新操作:")
                    print(f"- 成功: {json.dumps(summary['upsert']['success_ids'], indent=2, ensure_ascii=False)}")
                    print(f"- 失败: {json.dumps(summary['upsert']['failed_ids'], indent=2, ensure_ascii=False)}")
                    print("\n统计信息:")
                    print(f"- 总处理数: {summary['stats']['total_processed']}")
                    print(f"- 总失败数: {summary['stats']['total_failed']}")
                    print(f"- 总Token数: {summary['stats']['total_tokens']}")
            else:
                operation = response.data.get("operation", "unknown")
                print(f"\n[批次处理 - {operation}]")
                print("成功结果:")
                print(json.dumps(response.data.get("success_ids", {}), indent=2, ensure_ascii=False))
                print("\n失败结果:")
                print(json.dumps(response.data.get("failed_ids", {}), indent=2, ensure_ascii=False))
                if "batch_tokens" in response.data:
                    print(f"\n批次Token数: {response.data['batch_tokens']}")

            print("=" * 50)

        end_time = time.time()
        print(f"\n[测试] 批量更新完成，耗时: {end_time - start_time:.2f} 秒")
    except Exception as e:
        print(f"[测试] 批量更新失败: {str(e)}")

async def get_organization_qa_pairs_service(organization_id: int) -> Dict[str, List[int]]:
    """获取组织下所有现存的QA pair ID
    
    Args:
        organization_id: 组织ID
        
    Returns:
        Dict[str, List[int]]: 按语言分组的QA pair ID列表
    """
    try:
        return get_organization_qa_pairs(organization_id)
    except Exception as e:
        log_exception(DEVICE_AI_SERVER, e, f"获取组织 {organization_id} 的QA对失败")
        raise

async def _test_get_organization_qa_pairs():
    """测试获取组织QA对服务的函数"""
    try:
        organization_id = 1998
        print(f"\n[测试] 开始获取组织 {organization_id} 的QA对")
        print("=" * 50)
        
        start_time = time.time()
        result = await get_organization_qa_pairs_service(organization_id)
        
        print("\n[查询结果]")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 打印统计信息
        total_pairs = sum(len(pairs) for pairs in result.values())
        print("\n[统计信息]")
        print(f"总QA对数量: {total_pairs}")
        for lang, pairs in result.items():
            print(f"{lang}语言QA对数量: {len(pairs)}")
        
        end_time = time.time()
        print(f"\n[测试] 查询完成，耗时: {end_time - start_time:.2f} 秒")
        
    except Exception as e:
        print(f"[测试] 查询失败: {str(e)}")

async def delete_all_organization_qa_pairs_service(organization_id: int) -> AsyncGenerator[Response, None]:
    """删除组织下的所有QA对
    
    Args:
        organization_id: 组织ID
        
    Yields:
        Response: 包含删除进度和结果的响应
    """
    try:
        # 获取组织的所有QA对
        all_qa_pairs = await get_organization_qa_pairs_service(organization_id)
        
        if not all_qa_pairs:
            yield wrap_response({
                "message": f"组织 {organization_id} 没有任何QA对需要删除",
                "total_deleted": 0,
                "details": {}
            }, code=RESPONSE_CODE_END)
            return
        
        # 统计总数
        total_qa_pairs = sum(len(qa_ids) for qa_ids in all_qa_pairs.values())
        
        yield wrap_response({
            "status": "started",
            "message": f"开始删除组织 {organization_id} 的所有QA对",
            "total_count": total_qa_pairs,
            "languages": list(all_qa_pairs.keys())
        }, code=RESPONSE_CODE_SEGMENT_END)
        
        # 按语言分别删除
        deleted_summary = {}
        total_deleted = 0
        
        for language, qa_pair_ids in all_qa_pairs.items():
            try:
                yield wrap_response({
                    "status": "deleting",
                    "message": f"正在删除语言 {language} 的 {len(qa_pair_ids)} 个QA对",
                    "language": language,
                    "count": len(qa_pair_ids)
                }, code=RESPONSE_CODE_SEGMENT_END)
                
                # 执行删除
                deleted_count = delete_questions_by_qna_pair_ids(
                    organization_id=organization_id,
                    qna_pair_ids=qa_pair_ids,
                    language=language
                )
                
                deleted_summary[language] = {
                    "requested": len(qa_pair_ids),
                    "deleted": deleted_count,
                    "qa_pair_ids": qa_pair_ids
                }
                total_deleted += deleted_count
                
                yield wrap_response({
                    "status": "completed_language",
                    "message": f"语言 {language} 的QA对删除完成",
                    "language": language,
                    "deleted_count": deleted_count,
                    "requested_count": len(qa_pair_ids)
                }, code=RESPONSE_CODE_SEGMENT_END)
                
            except Exception as e:
                error_msg = f"删除语言 {language} 的QA对时发生错误: {str(e)}"
                log_exception(DEVICE_AI_SERVER, e, f"删除组织 {organization_id} 语言 {language} 的QA对")
                
                deleted_summary[language] = {
                    "requested": len(qa_pair_ids),
                    "deleted": 0,
                    "error": str(e),
                    "qa_pair_ids": qa_pair_ids
                }
                
                yield wrap_response({
                    "status": "error_language",
                    "message": error_msg,
                    "language": language,
                    "error": str(e)
                }, code=RESPONSE_CODE_SEGMENT_END)
        
        # 发送最终结果
        yield wrap_response({
            "status": "completed",
            "message": f"组织 {organization_id} 的QA对删除操作完成",
            "total_deleted": total_deleted,
            "total_requested": total_qa_pairs,
            "details": deleted_summary
        }, code=RESPONSE_CODE_END)
        
    except Exception as e:
        error_msg = log_exception(DEVICE_AI_SERVER, e, f"删除组织 {organization_id} 的所有QA对")
        yield wrap_error(ERROR_AI_SERVER, error_msg)
        raise

if __name__ == "__main__":
    # 修改主函数，添加新的测试
    async def run_tests():
        # 可以选择运行哪个测试
        await _test_batch_upsert()
        # await _test_get_organization_qa_pairs()
    
    asyncio.run(run_tests())


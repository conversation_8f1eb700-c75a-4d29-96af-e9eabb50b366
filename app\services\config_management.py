from typing import List, Dict, Any
from app.services.mysql_service import (
    get_biz_data
)
from app.utils.logger import log_exception



def handle_get_biz_data(organization_id: int, key: str, language: str = "zh-CN"):
    """
    获取特定的业务数据
    
    Args:
        organization_id: 组织ID
        key: 数据键
        language: 语言代码，默认为zh-CN
        
    Returns:
        Dict: 业务数据，如果不存在则返回None
    """
    try:
        try:
            biz_data = get_biz_data(organization_id, key, language)
            return {
                "key": biz_data.key,
                "data": biz_data.data,
                "language": biz_data.language
            }
        except Exception as e:
            # 如果数据不存在，返回None
            if "DoesNotExist" in str(e):
                return None
            # 其他异常则抛出
            raise
    except Exception as e:
        log_exception(f"org_{organization_id}", e, "handle_get_biz_data")
        raise

def handle_get_appointment_slots(organization_id, language: str = "zh-CN"):
    """
    获取预约号源信息
    
    Args:
        organization_id: 组织ID，默认为316
        language: 语言代码，默认为zh-CN
        
    Returns:
        Any: 预约号源信息数据内容
    """
    try:
        # 使用固定的key获取号源信息
        key = "appointment-slot-information-original"
        result = handle_get_biz_data(organization_id, key, language)
        return result["data"] if result else None
    except Exception as e:
        log_exception(f"org_{organization_id}", e, "handle_get_appointment_slots")
        raise
from app.services.mysql_service import db, PromptConfig, IntentionConfig, retry_on_db_error
import json

#====================================================#
#                     2.1.0                          #
#====================================================#

@db.connection_context()
@retry_on_db_error(is_write=True)
def change_character_id_from_number_to_name():  # 2.1.0
    """
    修改 PromptConfig 表中的 promptId 字段，检测 promptId 是否以 character_ 开头，是的话进行如下操作
    1. 将 character_ 改为 Character_
    2. 替换数字为对应的角色名称
    3. 如果没有语言后缀，根据 language 字段添加相应的后缀
    """
    # 数字到角色名称的映射
    number_to_name = {
        "0": "Cosmetic.CharacterName.Mike",
        "1": "Cosmetic.CharacterName.Kiko",
        "2": "Cosmetic.CharacterName.Panda",
        "3": "Cosmetic.CharacterName.Qiu",
    }

    # 支持的语言后缀
    language_suffixes = ["_zh-CN", "_zh-HK", "_ms", "_ar", "_en"]

    try:
        # 查询所有以 character_ 开头的记录
        records = PromptConfig.select().where(
            PromptConfig.promptId.startswith("character_")
        )

        updated_count = 0
        changes = []  # 记录所有修改的详细信息

        with db.atomic():
            for record in records:
                original_prompt_id = record.promptId
                new_prompt_id = original_prompt_id

                # 检查是否已经有语言后缀
                has_language_suffix = any(
                    new_prompt_id.endswith(suffix) for suffix in language_suffixes
                )

                # 如果没有语言后缀，根据 language 字段添加后缀
                if not has_language_suffix and record.language:
                    language_suffix = f"_{record.language}"
                    new_prompt_id = new_prompt_id + language_suffix
                    print(f"[信息] 添加语言后缀: {record.language}")

                # 将 character_ 改为 Character_
                if new_prompt_id.startswith("character_"):
                    new_prompt_id = new_prompt_id.replace("character_", "Character_", 1)

                # 替换数字为角色名称
                for number, name in number_to_name.items():
                    if number in new_prompt_id:
                        new_prompt_id = new_prompt_id.replace(number, name)

                # 如果有变化，则更新记录
                if new_prompt_id != original_prompt_id:
                    record.promptId = new_prompt_id
                    record.save()
                    updated_count += 1
                    
                    # 记录修改详情
                    changes.append({
                        "table": "PromptConfig",
                        "record_id": record.id,
                        "old_value": original_prompt_id,
                        "new_value": new_prompt_id,
                        "language": record.language
                    })
                    print(f"[信息] 已更新: {original_prompt_id} -> {new_prompt_id}")

        print(f"[信息] 共更新了 {updated_count} 条记录")
        return {
            "updated_count": updated_count,
            "changes": changes,
            "script_name": "change_character_id_from_number_to_name"
        }

    except Exception as e:
        print(f"[错误] 更新 promptId 时发生错误: {str(e)}")
        db.safe_rollback()
        raise


@db.connection_context()
@retry_on_db_error(is_write=True)
def remove_unavailable_intention_config():
    """
    删除 intentionconfig 表中 config 字段的value为空的记录
    清理 config 字段中值为空字符串的键值对，例如：
    {
        "chat": "",
        "business": "",
        "knowledge": "知识问答,只是闲聊"
    }
    会被清理为：
    {
        "knowledge": "知识问答,只是闲聊"
    }
    """
    
    def _is_valid_value(value):
        """检查值是否有效（非空且非纯空格）"""
        if isinstance(value, str):
            return bool(value.strip())
        return bool(value)
    
    try:
        # 查询所有的 IntentionConfig 记录
        records = IntentionConfig.select()
        
        updated_count = 0
        changes = []  # 记录所有修改的详细信息
        
        with db.atomic():
            for record in records:
                if not record.config:
                    continue
                    
                original_config = record.config
                
                # 过滤掉值为空字符串的键值对
                filtered_config = {
                    k: v for k, v in original_config.items() 
                    if _is_valid_value(k) and _is_valid_value(v)
                }
                
                # 如果有变化，则更新记录
                if filtered_config != original_config:
                    record.config = filtered_config
                    record.save()
                    updated_count += 1
                    
                    # 记录变化详情
                    removed_keys = set(original_config.keys()) - set(filtered_config.keys())
                    changes.append({
                        "table": "IntentionConfig",
                        "record_id": record.id,
                        "organization_id": record.organizationId,
                        "language": record.language,
                        "removed_keys": list(removed_keys),
                        "original_config": original_config,
                        "filtered_config": filtered_config
                    })
                    print(f"[信息] 组织ID {record.organizationId} 语言 {record.language}: "
                          f"已删除空值键: {list(removed_keys)}")
        
        print(f"[信息] 共清理了 {updated_count} 条意图配置记录")
        return {
            "updated_count": updated_count,
            "changes": changes,
            "script_name": "remove_unavailable_intention_config"
        }
        
    except Exception as e:
        print(f"[错误] 清理意图配置时发生错误: {str(e)}")
        db.safe_rollback()
        raise


# Chinese (Simplified, China) translations for PROJECT.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-06-11 16:55+0800\n"
"PO-Revision-Date: 2024-10-26 10:23+0800\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_Hans_CN\n"
"Language-Team: zh_Hans_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: app/routers/__init__.py:83
#, python-brace-format
msgid "接口 {} 总处理时间超出限制，已超过 {} 秒"
msgstr "接口 {} 总处理时间超出限制，已超过 {} 秒"

#: app/routers/__init__.py:91
#, python-brace-format
msgid "接口 {} 处理异常: {}"
msgstr "接口 {} 处理异常: {}"

#: app/routers/__init__.py:97
#, python-brace-format
msgid "接口 {} 服务器内部错误: {}"
msgstr "接口 {} 服务器内部错误: {}"

#: app/routers/__init__.py:133
#, python-brace-format
msgid "接口 {} 流式数据块响应超时，单次响应已超过 {} 秒"
msgstr "接口 {} 流式数据块响应超时，单次响应已超过 {} 秒"

#: app/routers/chat.py:162 app/routers/chat.py:210 app/routers/chat.py:243
#: app/routers/doc2vec.py:270 app/routers/push_config.py:480
#, python-brace-format
msgid "服务器处理请求时遇到错误: {}"
msgstr "服务器处理请求时遇到错误: {}"

#: app/routers/chat.py:182
msgid "messages字段非法，必须为JSON Array"
msgstr "messages字段非法，必须为JSON Array"

#: app/routers/chat.py:188
msgid "role字段非法，必须为 user 或 assistant"
msgstr "messages字段非法，必须为JSON Array"

#: app/routers/chat.py:192
msgid "content字段非法，必须为字符串"
msgstr "messages字段非法，必须为JSON Array"

#: app/routers/prompt.py:33
#, python-brace-format
msgid "提示词模板化失败: {}"
msgstr "提示词模板化失败: {}"

#: app/routers/push_config.py:101
msgid "无效的intention数据格式"
msgstr "无效的intention数据格式"

#: app/routers/push_config.py:105
msgid "Intention配置推送成功"
msgstr "Intention配置推送成功"

#: app/routers/push_config.py:110
msgid "无效的prompt数据格式"
msgstr "无效的prompt数据格式"

#: app/routers/push_config.py:114
#, python-brace-format
msgid "Prompt配置推送成功。{} 项更新。"
msgstr "Prompt配置推送成功。{} 个提示更新。"

#: app/routers/push_config.py:160
#, fuzzy
msgid "无效的bizData项格式"
msgstr "无效的bizData格式"

#: app/routers/push_config.py:200
msgid "无效的bizData格式"
msgstr "无效的bizData格式"

#: app/routers/push_config.py:204
#, python-brace-format
msgid "业务数据推送成功。{} 项更新。"
msgstr "业务数据推送成功。{} 项更新。"

#: app/routers/push_config.py:210
#, fuzzy
msgid "无效的uiConfig数据格式"
msgstr "无效的intention数据格式"

#: app/routers/push_config.py:213
#, fuzzy, python-brace-format
msgid "UI配置推送成功。{} 项更新。"
msgstr "Prompt配置推送成功。{} 个提示更新。"

#: app/routers/push_config.py:216
#, fuzzy
msgid "无效的配置名称，仅支持intention, prompt, bizData, button, directReply, page"
msgstr "无效的配置名称，仅支持 qa、intention、prompt、bizData"

#: app/routers/push_config.py:220
#, python-brace-format
msgid "配置推送失败: {}"
msgstr "配置推送失败: {}"

#: app/routers/push_config.py:264
#, fuzzy
msgid "无效的LLM配置数据格式"
msgstr "无效的prompt数据格式"

#: app/routers/push_config.py:269
#, python-brace-format
msgid "配置项 {} 必须是字典格式"
msgstr ""

#: app/routers/push_config.py:272
#, python-brace-format
msgid ""
"配置项 {} 的格式不正确。每个配置项必须满足以下条件之一：1. 同时没有model, api_key, base_url, "
"此时将使用默认的model, api_key, base_url。2. 只有model, 同时没有api_key, base_url, "
"此时将使用配置的model和默认的api_key, base_url。3. model, api_key, base_url 都拥有, "
"此时将使用配置的model, api_key, base_url。"
msgstr ""

#: app/routers/push_config.py:280
msgid "LLM配置推送成功"
msgstr ""

#: app/routers/push_config.py:283
#, fuzzy, python-brace-format
msgid "LLM配置推送失败: {}"
msgstr "配置推送失败: {}"

#: app/routers/push_config.py:291
msgid "无效的Embedding配置数据格式"
msgstr ""

#: app/routers/push_config.py:295
msgid "配置项 embedding_config 必须是字典格式"
msgstr ""

#: app/routers/push_config.py:298
msgid ""
"配置项 embedding_config 的格式不正确。每个配置项必须满足以下条件之一：1. 同时没有model, api_key, "
"base_url, 此时将使用默认的model, api_key, base_url。2. 只有model, 同时没有api_key, "
"base_url, 此时将使用配置的model和默认的api_key, base_url。3. model, api_key, base_url "
"都拥有, 此时将使用配置的model, api_key, base_url。"
msgstr ""

#: app/routers/push_config.py:306
msgid "Embedding配置推送成功"
msgstr ""

#: app/routers/push_config.py:309
#, fuzzy, python-brace-format
msgid "Embedding配置推送失败: {}"
msgstr "配置推送失败: {}"

#: app/routers/push_config.py:324
#, python-brace-format
msgid "启动 {} 配置推送任务时出错: {}"
msgstr "启动 {} 配置推送任务时出错: {}"

#: app/routers/push_config.py:341
#, fuzzy, python-brace-format
msgid "配置删除失败: {}"
msgstr "配置推送失败: {}"

#: app/routers/push_config.py:356
#, fuzzy, python-brace-format
msgid "启动 {} LLM配置推送任务时出错: {}"
msgstr "启动 {} 配置推送任务时出错: {}"

#: app/routers/push_config.py:370
#, fuzzy, python-brace-format
msgid "启动 {} Embedding配置推送任务时出错: {}"
msgstr "启动 {} 配置推送任务时出错: {}"

#: app/routers/push_config.py:503
#, python-brace-format
msgid "获取组织QA数据时发生错误: {}"
msgstr "获取组织QA数据时发生错误: {}"

#: app/services/ai_chat.py:105
msgid "抱歉，我无法处理您的问题"
msgstr "抱歉，我无法处理您的问题"

#: app/services/ai_chat.py:140
#, python-brace-format
msgid "意图配置缺失: {}"
msgstr "意图配置缺失"

#: app/services/ai_chat.py:721
#, python-brace-format
msgid "未配置{}提示词"
msgstr "未配置{}提示词"

#: app/services/ai_chat.py:725
msgid "配置检查失败"
msgstr "配置检查失败"

#: app/services/biz_handler.py:396
msgid "正在帮您处理请求，请稍等一下哦"
msgstr "正在帮您处理请求，请稍等一下哦"

#: app/services/knowledge_handler.py:454
msgid "我正在找答案呢，稍等一下"
msgstr "我正在找答案呢，稍等一下"

#: app/services/knowledge_handler.py:527
msgid "正在找相关资料，稍微等下哦"
msgstr "正在找相关资料，稍微等下哦"

#: app/services/knowledge_handler.py:602
msgid "正在整理资料，稍等片刻"
msgstr "正在整理资料，稍等片刻"

#: app/services/knowledge_handler.py:649
msgid "我正在组织回答，稍等一下哦"
msgstr "我正在组织回答，稍等一下哦"

#: app/services/knowledge_handler.py:698
msgid "嗯，这个问题我暂时没法回答哦"
msgstr "嗯，这个问题我暂时没法回答哦"

#: app/services/llm_client.py:113
#, python-brace-format
msgid "未知的模型: {}"
msgstr "未知的模型: {}"

#: app/services/llm_client.py:236 app/services/llm_client.py:347
msgid "未提供有效的模型"
msgstr ""

#: app/services/llm_client.py:301
msgid "未响应"
msgstr "未响应"

#~ msgid "处理器 '{}' 配置错误: {}"
#~ msgstr "启动 {} 配置推送任务时出错: {}"

#~ msgid "提示词配置缺失"
#~ msgstr "提示词配置缺失"

#~ msgid "提示词格式化失败: {}"
#~ msgstr ""


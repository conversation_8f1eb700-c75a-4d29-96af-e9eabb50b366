import asyncio
import json
from typing import Optional
from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from app.utils.net_utils import wrap_response, wrap_error, ERROR_AI_SERVER
from app.utils.logger import log_exception
from app.services.sql_update import get_latest_version, sql_update_stream

router = APIRouter()


class SQLUpdateRequest(BaseModel):
    version: Optional[str] = None  # 指定的版本号
    enable: Optional[bool] = None  # 是否更新最新版本，填写后就不用填写version


@router.post("/sql-update")
async def sql_update(request: SQLUpdateRequest):
    """
    执行SQL脚本（流式返回）
    """
    try:
        target_version = None

        if request.enable:
            # 使用最新版本
            latest_version = get_latest_version()
            if latest_version is None:
                return wrap_error(ERROR_AI_SERVER, "No SQL update versions found")
            target_version = latest_version

        elif request.version:
            target_version = request.version
        else:
            return wrap_error(ERROR_AI_SERVER, "请指定版本号或启用最新版本更新")

        # 创建流式响应生成器
        async def stream_generator():
            async for message in sql_update_stream(target_version):
                # 将字典转换为Server-Sent Events格式
                yield f"data: {json.dumps(message, ensure_ascii=False)}\n\n"

        # 返回流式响应
        return StreamingResponse(
            stream_generator(),
            media_type="text/plain; charset=utf-8",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream; charset=utf-8",
            },
        )

    except Exception as e:
        err_msg = log_exception("system", e, "sql_update")
        return wrap_error(ERROR_AI_SERVER, err_msg)

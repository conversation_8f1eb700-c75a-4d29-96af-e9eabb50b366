from setuptools import setup, Extension, find_packages
from Cython.Build import cythonize

setup(
    packages=find_packages(include=["app", "app.services", "app.base", "app.utils", "app.services.*"]),
    ext_modules=cythonize(["app/services/ai_chat.py",
                           "app/services/knowledge_handler.py",
                           "app/services/intention_detect.py",
                           "app/services/llm_client.py",
                           "app/services/biz_handler.py",
                          "app/base/jinja2_formatter.py"], compiler_directives={'language_level': "3"}),
    zip_safe=False,
)
from functools import lru_cache
from typing import Dict, Any

from app.services.mysql_service import get_business_config, upsert_business_config
from app.utils.logger import log_exception
from app.services import DEVICE_AI_SERVER
from app.config import Env

# 默认完整配置
DEFAULT_CONFIG = {
    "timeout_config": {
        "/chat": 300.0,
        "/doc2vec": 100000.0,
        "/pushConfig": 100000.0,
        "/pushQA": 7200.0,
        "/translate": 600.0,
        "/translate/qa": 1200.0,
        "/detect-language": 30.0,
    },
    "stream_timeout_config": {
        "/doc2vec": 600.0,
        "/chat": 30.0,
        "/translate": 120.0,
        "/pushConfig": 60.0,
        "/pushQA": 600.0,
    },
    "base_prompt_set_id": 0,
}

# LRU缓存大小
CACHE_SIZE = 100


class ConfigService:
    """
    全局业务配置服务

    提供基于组织ID的配置管理，支持LRU缓存，并在配置缺失时提供回退机制。
    """

    def __init__(self):
        # 初始化LRU缓存
        self._get_config = lru_cache(maxsize=CACHE_SIZE)(self._fetch_config)

    def _fetch_config(self, organization_id: int) -> Dict[str, Any]:
        """
        从数据库获取配置，此方法会被LRU缓存装饰
        """
        try:
            # 尝试获取当前组织的配置
            config = get_business_config(organization_id)
            if config:
                if Env.PRINT_FETCH_CONFIG:
                    print(f"[Config] 加载配置 - 组织ID: {organization_id}, 值: {config.configValue}")
                return config.configValue

            # 如果没有找到当前组织的配置且组织ID不是0，使用默认组织(0)的配置
            if organization_id != 0:
                default_config = get_business_config(0)
                if default_config:
                    print(f"[Debug] 使用默认组织配置, 值: {default_config.configValue}")
                    return default_config.configValue

            # 返回硬编码默认配置
            print(f"[Debug] 使用硬编码默认配置, 值: {DEFAULT_CONFIG}")
            return DEFAULT_CONFIG

        except Exception as e:
            log_exception(DEVICE_AI_SERVER, e, "获取配置失败")
            return DEFAULT_CONFIG

    def get_config(self, organization_id: int) -> Dict[str, Any]:
        """
        获取完整配置
        """
        try:
            return self._get_config(organization_id)
        except Exception as e:
            log_exception(DEVICE_AI_SERVER, e, "获取配置失败并回退到默认值")
            return DEFAULT_CONFIG

    def get_specific_config(
        self, organization_id: int, config_path: str = None, default_value=None
    ):
        """
        根据配置路径获取特定配置项

        Args:
            organization_id: 组织ID
            config_path: 配置路径，例如 "timeout_config./chat" 或 "stream_timeout_config"，
                         如果为None则返回完整配置
            default_value: 找不到配置时的默认返回值

        Returns:
            指定路径的配置值，如果未找到则返回默认值
        """
        try:
            config = self._get_config(organization_id)

            # 处理空路径，返回完整配置
            if not config_path:
                return config

            # 按路径导航
            path_parts = config_path.split(".")
            current = config

            for part in path_parts:
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    # 路径不存在，尝试在默认配置中查找
                    return self._find_in_default_config(config_path, default_value)

            return current
        except Exception as e:
            log_exception(
                DEVICE_AI_SERVER,
                e,
                f"根据路径'{config_path}'获取配置失败并回退到默认值",
            )
            return self._find_in_default_config(config_path, default_value)

    def _find_in_default_config(self, config_path: str, default_value=None):
        """
        在默认配置中查找指定路径的值
        """
        if not config_path:
            return DEFAULT_CONFIG

        path_parts = config_path.split(".")
        current = DEFAULT_CONFIG

        for part in path_parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return default_value

        return current

    def update_config(self, organization_id: int, config_data: Dict[str, Any]) -> bool:
        """
        更新配置并清除缓存
        """
        try:
            # 如果config_data为None或空字符串，直接删除配置
            if config_data is None or config_data == "":
                upsert_business_config(organization_id, None)
                # 清除缓存
                self.clear_cache()
                return True
            
            # 如果只更新部分配置，先获取完整配置
            if organization_id != 0:  # 非默认组织
                current_config = self._get_config(organization_id)
            else:  # 默认组织
                # 尝试获取现有默认配置
                default_config = get_business_config(0)
                current_config = (
                    default_config.configValue if default_config else DEFAULT_CONFIG
                )

            # 更新配置
            for key, value in config_data.items():
                current_config[key] = value

            # 保存更新后的配置
            upsert_business_config(organization_id, current_config)
            # 清除缓存
            self.clear_cache()
            return True
        except Exception as e:
            log_exception(DEVICE_AI_SERVER, e, "更新配置失败")
            return False

    def clear_cache(self):
        """
        清除所有缓存
        """
        self._get_config.cache_clear()


# 全局配置服务实例
config_service = ConfigService()


# 初始化系统默认配置（如果不存在）
def init_default_configs():
    """初始化系统默认配置（组织ID为0）"""
    try:
        # 检查默认配置是否存在
        default_config = get_business_config(0)
        if not default_config:
            # 初始化默认配置
            upsert_business_config(0, DEFAULT_CONFIG)
            print(f"[Info] 已初始化默认配置")
        return True
    except Exception as e:
        log_exception(DEVICE_AI_SERVER, e, "初始化默认配置失败")
        return False

    
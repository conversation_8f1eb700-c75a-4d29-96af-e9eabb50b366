from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Any
import app.services.ai_chat as ai_chat
from app.utils.logger import log_exception
from app.utils.net_utils import wrap_response
from fastapi_babel import _
from app.services.qa_service import batch_upsert_qa_pairs_service, get_organization_qa_pairs_service
from fastapi.responses import StreamingResponse
import asyncio
from app.utils.net_utils import (
    wrap_response,
    RESPONSE_CODE_SEGMENT_END,
)
from app.services.mysql_service import delete_all_ui_configs
from app.utils.logger import log_text
from app.services.qa_service import get_organization_qa_pairs_service, delete_all_organization_qa_pairs_service

router = APIRouter()


class QueueTracker:
    def __init__(self):
        self._current_position = 0
        self._queue_length = 0
        self._lock = asyncio.Lock()
    
    async def enter_queue(self):
        async with self._lock:
            self._queue_length += 1
            return self._queue_length - 1
    
    async def leave_queue(self):
        async with self._lock:
            self._queue_length -= 1
            self._current_position += 1
            
    async def get_position(self, initial_position: int) -> int:
        async with self._lock:
            return max(0, initial_position - self._current_position)


_queue_tracker = QueueTracker()
_push_qa_semaphore = asyncio.Semaphore(1)


'''
{
    organizationId: int,
    data: {
        service_name: {
            model: str
            api_key: str
            base_url: str
        }
    }
}
'''
class PushLLMConfigRequest(BaseModel):
    organizationId: int
    data: Any


'''
{
    organizationId: int,
    data: {
        model: str
        api_key: str
        base_url: str
    }
}
'''
class PushEmbeddingConfigRequest(BaseModel):
    organizationId: int
    data: Any

class PushConfigRequest(BaseModel):
    name: str
    organizationId: int
    data: Any

    def __str__(self):
        return f"PushConfigRequest(name={self.name}, organizationId={self.organizationId}, data={self.data})"


class DeleteConfigRequest(BaseModel):
    name: str
    organizationId: int
    data: List[dict]


# def clear_ai_chat_cache(organization_id: int):
#     """清除指定组织的AI聊天实例缓存"""
#     if organization_id in ai_chat.get_ai_chat.cache._dict:
#         del ai_chat.get_ai_chat.cache._dict[organization_id]
#         del ai_chat.get_ai_chat.cache._cache[organization_id]


# 根据name用不同的方式处理配置更新
async def process_push_config(request: PushConfigRequest):
    try:
        log_text(
            f"org_{request.organizationId}",
            f"PushConfig Start: {str(request)}"
        )
        msg = ""
        if request.name == "intention":
            if not isinstance(request.data, dict):
                raise ValueError(_("无效的intention数据格式"))

            await ai_chat.handle_push_intention(request.organizationId, request.data)
            # clear_ai_chat_cache(request.organizationId)
            msg = _("Intention配置推送成功")

        elif request.name == "prompt":
            if not isinstance(request.data, List):
                raise ValueError(_("无效的prompt数据格式"))

            await ai_chat.handle_push_prompt(request.organizationId, request.data)
            # clear_ai_chat_cache(request.organizationId)
            msg = _("Prompt配置推送成功。{} 项更新。").format(len(request.data))

        elif request.name == "bizData":
            # 检查数据格式
            if isinstance(request.data, dict) and "key" in request.data:
                # 单个数据项的情况
                data_items = [{
                    "key": request.data.get("key"),
                    "value": request.data.get("data", request.data.get("value", ""))
                }]
                
                # 特殊处理appointment-slot-information
                if data_items[0]["key"] == "appointment-slot-information" and "data" in request.data:
                    # 保存原始版本
                    data_items.append({
                        "key": "appointment-slot-information-original",
                        "value": request.data["data"]
                    })
                    
                    # 创建不带时间的版本
                    data_items[0]["value"] = format_appointment_slot_information(request.data["data"], include_time=False)
                    
                    # 添加带时间的版本
                    data_items.append({
                        "key": "appointment-slot-information-with-time",
                        "value": format_appointment_slot_information(request.data["data"], include_time=True)
                    })
                    
                    # 添加科室信息版本
                    data_items.append({
                        "key": "departments-info",
                        "value": format_departments_info(request.data["data"])
                    })
                    
                    # 添加日期和星期版本
                    data_items.append({
                        "key": "appointment-slot-information-date",
                        "value": format_appointment_date_weekday(request.data["data"])
                    })
                    
            elif isinstance(request.data, list):
                # 多个数据项的情况
                data_items = []
                for item in request.data:
                    if not isinstance(item, dict) or "key" not in item:
                        raise ValueError(_("无效的bizData项格式"))
                        
                    data_item = {
                        "key": item.get("key")
                    }
                    
                    # 处理appointment-slot-information特殊情况
                    if data_item["key"] == "appointment-slot-information" and "data" in item:
                        # 保存原始版本
                        data_items.append({
                            "key": "appointment-slot-information-original",
                            "value": item["data"]
                        })
                        
                        # 创建不带时间的版本
                        data_item["value"] = format_appointment_slot_information(item["data"], include_time=False)
                        data_items.append(data_item)
                        
                        # 添加带时间的版本
                        data_items.append({
                            "key": "appointment-slot-information-with-time",
                            "value": format_appointment_slot_information(item["data"], include_time=True)
                        })
                        
                        # 添加科室信息版本
                        data_items.append({
                            "key": "departments-info",
                            "value": format_departments_info(item["data"])
                        })
                        
                        # 添加日期和星期版本
                        data_items.append({
                            "key": "appointment-slot-information-date",
                            "value": format_appointment_date_weekday(item["data"])
                        })
                    else:
                        # 对于其他类型的数据，保留原始值
                        data_item["value"] = item.get("data", item.get("value", ""))
                        data_items.append(data_item)
            else:
                raise ValueError(_("无效的bizData格式"))

            await ai_chat.handle_push_biz_data(request.organizationId, data_items)
            # clear_ai_chat_cache(request.organizationId)
            msg = _("业务数据推送成功。{} 项更新。").format(len(data_items))
    
        elif request.name == "uiConfig":
            if not isinstance(request.data, dict):
                raise ValueError(_("无效的uiConfig数据格式"))

            await ai_chat.handle_push_ui_config(request.organizationId, request.data)
            msg = _("UI配置推送成功。{} 项更新。").format(len(request.data))
        else:
            raise ValueError(_("无效的配置名称，仅支持intention, prompt, bizData, uiConfig"))

        response = wrap_push_config_response(msg, request.organizationId)
        log_text(
            f"org_{request.organizationId}",
            f"PushConfig End: {str(response)}"
        )
        return response

    except Exception as e:
        err_msg = log_exception(f"org_{request.organizationId}", e, "pushConfig")
        raise ValueError(_("配置推送失败: {}").format(err_msg))


def wrap_push_config_response(message: str, organization_id: int):
    result = ai_chat.check_org_config(organization_id)
    data = {"message": message, "extra": "\n".join(result)}
    return wrap_response(data)

def wrap_push_llm_config_response(message: str, organization_id: int):
    data = {"message": message}
    return wrap_response(data)

def wrap_push_embedding_config_response(message: str, organization_id: int):
    data = {"message": message}
    return wrap_response(data)


def validate_llm_service_config(config_item: dict) -> bool:
    """
    验证LLM配置项是否符合规则：
    1. 同时没有model, api_key, base_url
    2. 只有model, 同时没有api_key, base_url
    3. model, api_key, base_url 都拥有
    """
    has_model = "model" in config_item
    has_api_key = "api_key" in config_item
    has_base_url = "base_url" in config_item
    
    # 情况1：同时没有这三个字段
    if not has_model and not has_api_key and not has_base_url:
        return True
        
    # 情况2：只有model
    if has_model and not has_api_key and not has_base_url:
        return True
        
    # 情况3：三个字段都有
    if has_model and has_api_key and has_base_url:
        return True
        
    return False

async def process_push_llm_config(request: PushLLMConfigRequest):
    if not isinstance(request.data, dict):
        raise ValueError(_("无效的LLM配置数据格式"))
        
    # 验证每个配置项
    for key, config_item in request.data.items():
        if not isinstance(config_item, dict):
            raise ValueError(_("配置项 {} 必须是字典格式").format(key))
            
        if not validate_llm_service_config(config_item):
            raise ValueError(_("配置项 {} 的格式不正确。每个配置项必须满足以下条件之一："
                            "1. 同时没有model, api_key, base_url, 此时将使用默认的model, api_key, base_url。"
                            "2. 只有model, 同时没有api_key, base_url, 此时将使用配置的model和默认的api_key, base_url。"
                            "3. model, api_key, base_url 都拥有, 此时将使用配置的model, api_key, base_url。").format(key))

    try:
        await ai_chat.handle_push_llm_config(request.organizationId, request.data)
        # clear_ai_chat_cache(request.organizationId)
        msg = _("LLM配置推送成功")
    except Exception as e:
        err_msg = log_exception(f"org_{request.organizationId}", e, "pushLLMConfig")
        raise ValueError(_("LLM配置推送失败: {}").format(err_msg))
    return wrap_push_llm_config_response(msg, request.organizationId)

'''
Embedding配置推送
'''
async def process_push_embedding_config(request: PushEmbeddingConfigRequest):
    if not isinstance(request.data, dict):
        raise ValueError(_("无效的Embedding配置数据格式"))
        
    # 验证每个配置项
    if not isinstance(request.data, dict):
        raise ValueError(_("配置项 embedding_config 必须是字典格式"))
        
    if not validate_llm_service_config(request.data):
        raise ValueError(_("配置项 embedding_config 的格式不正确。每个配置项必须满足以下条件之一："
                        "1. 同时没有model, api_key, base_url, 此时将使用默认的model, api_key, base_url。"
                        "2. 只有model, 同时没有api_key, base_url, 此时将使用配置的model和默认的api_key, base_url。"
                        "3. model, api_key, base_url 都拥有, 此时将使用配置的model, api_key, base_url。"))

    try:
        await ai_chat.handle_push_embedding_config(request.organizationId, request.data)
        # clear_ai_chat_cache(request.organizationId)
        msg = _("Embedding配置推送成功")
    except Exception as e:
        err_msg = log_exception(f"org_{request.organizationId}", e, "pushEmbeddingConfig")
        raise ValueError(_("Embedding配置推送失败: {}").format(err_msg))
    return wrap_push_embedding_config_response(msg, request.organizationId)


@router.post("/pushConfig")
async def push_config(request: PushConfigRequest):
    try:
        result = await process_push_config(request)
        return result
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        err_msg = log_exception(f"org_{request.organizationId}", e, "pushConfig")
        raise HTTPException(
            status_code=500,
            detail=_("启动 {} 配置推送任务时出错: {}").format(request.name, err_msg),
        )

@router.get("/config/{name}")
async def get_config(name: str, organizationId: int):
    try:
        result = await ai_chat.handle_get_config(name, organizationId)
        return wrap_response(result)
    except Exception as e:
        err_msg = log_exception(f"org_{organizationId}", e, "getConfig")
        raise HTTPException(
            status_code=500,
            detail=_("获取配置时发生错误: {}").format(err_msg)
        )

@router.post("/delete_config")
async def delete_config(request: DeleteConfigRequest):
    """
    删除配置
    """
    try:
        delete_all_ui_configs(request.organizationId)
        return wrap_response({"message": "UI配置删除成功"})

    except Exception as e:
        err_msg = log_exception(f"org_{request.organizationId}", e, "deleteConfig")
        raise HTTPException(
            status_code=400,
            detail=_("配置删除失败: {}").format(err_msg)
        )


@router.post("/pushLLMConfig")
async def push_llm_config(request: PushLLMConfigRequest):
    try:
        result = await process_push_llm_config(request)
        return result
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        err_msg = log_exception(f"org_{request.organizationId}", e, "pushConfig")
        raise HTTPException(
            status_code=500,
            detail=_("启动 {} LLM配置推送任务时出错: {}").format(request.name, err_msg),
        )
    
@router.post("/pushEmbeddingConfig")
async def push_embedding_config(request: PushEmbeddingConfigRequest):
    try:
        result = await process_push_embedding_config(request)
        return result
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        err_msg = log_exception(f"org_{request.organizationId}", e, "pushConfig")
        raise HTTPException(
            status_code=500,
            detail=_("启动 {} Embedding配置推送任务时出错: {}").format(request.name, err_msg),
        )
    

class QAPair(BaseModel):
    qaPairId: int
    language: str
    questions: List[str]
    answer: str
    
    def __str__(self):
        questions_str = ", ".join(self.questions)
        return f"ID:{self.qaPairId}({self.language}) Q:[{questions_str}] A:{self.answer}"


class PushQARequest(BaseModel):
    organizationId: int
    data: List[QAPair]
    
    def __str__(self):
        qa_info = [str(qa) for qa in self.data]
        return f"PushQARequest(orgId={self.organizationId}, qaInfo=[{', '.join(qa_info)}])"



@router.post("/pushQA")
async def push_qa(request: PushQARequest):
    try:
        # 记录请求开始
        log_text(
            f"org_{request.organizationId}",
            f"PushQA Start: {str(request)}"
        )
        
        queue_position = await _queue_tracker.enter_queue()
        
        async def response_generator():
            if queue_position == 0:
                # 直接处理，无需排队
                start_response = wrap_response({
                    'status': 'processing', 
                    'message': 'Start processing request'
                }, code=RESPONSE_CODE_SEGMENT_END)
                yield start_response.to_sse()
            else:
                # 需要排队的情况
                last_position = queue_position
                while True:
                    current_position = await _queue_tracker.get_position(queue_position)
                    if current_position == 0:
                        break
                        
                    if last_position != current_position:
                        queue_response = wrap_response({
                            'status': 'queued', 
                            'position': current_position, 
                            'message': f'Queuing, {current_position} requests ahead'
                        }, code=RESPONSE_CODE_SEGMENT_END)
                        yield queue_response.to_sse()
                        last_position = current_position
                    await asyncio.sleep(1)
                
                # 轮到处理时
                start_response = wrap_response({
                    'status': 'processing', 
                    'message': 'Start processing request'
                }, code=RESPONSE_CODE_SEGMENT_END)
                yield start_response.to_sse()
            
            try:
                async with _push_qa_semaphore:
                    qa_items = [
                        {
                            "qa_pair_id": qa_pair.qaPairId,
                            "language": qa_pair.language,
                            "questions": qa_pair.questions,
                            "answer": qa_pair.answer
                        }
                        for qa_pair in request.data
                    ]
                    
                    async for response in batch_upsert_qa_pairs_service(
                        request.organizationId, 
                        qa_items
                    ):
                        # 记录每次response的处理结果
                        log_text(
                            f"org_{request.organizationId}",
                            f"PushQA Response: {str(response)}"
                        )
                        
                        yield response.to_sse()
                        
            finally:
                await _queue_tracker.leave_queue()

        return StreamingResponse(
            response_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
    except Exception as e:
        # 发生错误时确保离开队列
        await _queue_tracker.leave_queue()
        err_msg = log_exception(f"org_{request.organizationId}", e, "pushQA")
        raise HTTPException(
            status_code=500, 
            detail=_("服务器处理请求时遇到错误: {}").format(err_msg)
        )


@router.get("/qa/organization/{organization_id}")
async def get_organization_qa_pairs(
    organization_id: int
):
    """获取组织下所有现存的QA pair ID
    
    Args:
        organization_id: 组织ID
        
    Returns:
        Dict[str, List[int]]: 按语言分组的QA pair ID列表
    """
    try:
        result = await get_organization_qa_pairs_service(organization_id)
        return wrap_response(result)
    except Exception as e:
        err_msg = log_exception(f"org_{organization_id}", e, "getOrganizationQAPairs")
        raise HTTPException(
            status_code=500,
            detail=_("获取组织QA数据时发生错误: {}").format(err_msg)
        )


class DeleteAllQARequest(BaseModel):
    organizationId: int


@router.post("/deleteQA")
async def delete_all_qa_pairs(request: DeleteAllQARequest):
    """
    删除组织下所有QA对
    """
    try:
        # 记录请求开始
        log_text(
            f"org_{request.organizationId}",
            f"DeleteAllQA Start: organizationId={request.organizationId}"
        )
        
        async def response_generator():
            try:
                async for response in delete_all_organization_qa_pairs_service(request.organizationId):
                    yield response.to_sse()
            except ValueError as ve:
                error_response = wrap_response({
                    'status': 'error',
                    'message': str(ve)
                }, code=RESPONSE_CODE_SEGMENT_END)
                yield error_response.to_sse()
            except Exception as e:
                err_msg = log_exception(f"org_{request.organizationId}", e, "deleteAllQAPairs")
                error_response = wrap_response({
                    'status': 'error',
                    'message': _("删除所有QA对时出错: {}").format(err_msg)
                }, code=RESPONSE_CODE_SEGMENT_END)
                yield error_response.to_sse()

        return StreamingResponse(
            response_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
    except Exception as e:
        err_msg = log_exception(f"org_{request.organizationId}", e, "deleteAllQAPairs")
        raise HTTPException(
            status_code=500,
            detail=_("启动删除所有QA对任务时出错: {}").format(err_msg),
        )


def format_appointment_slot_information(value, include_time=False):
    """
    将号源信息JSON格式化为LLM友好的精简文本
    
    Args:
        value: 原始预约时间槽信息(JSON格式)
        include_time: 是否包含具体时间段信息
        
    Returns:
        str: 格式化后的预约时间槽信息文本
    """
    try:
        if not isinstance(value, list):
            return str(value)
            
        result_parts = []
        
        # 遍历所有科室
        for dept in value:
            dept_id = dept.get("departmentId", "")
            dept_name = dept.get("departmentName", "")
            doctors = dept.get("doctors", [])
            
            # 只处理有医生的科室
            if not doctors:
                continue
                
            # 添加科室信息
            dept_info = f"{dept_name}({dept_id})"
            result_parts.append(dept_info)
            
            # 添加医生信息
            for doctor in doctors:
                doc_id = doctor.get("id", "")
                doc_name = doctor.get("name", "")
                appointments = doctor.get("appointments", [])
                
                # 如果没有排班信息，继续下一个医生
                if not appointments:
                    continue
                
                if include_time:
                    # 包含时间的版本 - 按日期和时间段组织数据
                    date_time_info = {}
                    for appt in appointments:
                        date_str = appt.get("date", "")
                        time_slots = appt.get("timeSlots", [])
                        
                        if not date_str or len(date_str) < 10:
                            continue
                        
                        if date_str not in date_time_info:
                            date_time_info[date_str] = []
                        
                        if time_slots:
                            date_time_info[date_str].extend(time_slots)
                    
                    # 按年月分组收集日期
                    date_groups = {}
                    for date_str, time_slots in date_time_info.items():
                        # 分解日期为年月和日
                        year_month = date_str[:7]  # 例如 "2025-03"
                        day = int(date_str[8:10])  # 转换为数字，例如 4
                        
                        # 将日和时间段添加到对应年月组
                        if year_month not in date_groups:
                            date_groups[year_month] = {}
                        
                        # 去重时间段
                        unique_time_slots = list(set(time_slots))
                        # 排序时间段
                        unique_time_slots.sort()
                        
                        date_groups[year_month][day] = unique_time_slots
                    
                    # 格式化日期组为更易读的形式
                    formatted_dates = []
                    for year_month, days_info in sorted(date_groups.items()):
                        year, month = year_month.split("-")
                        # 年份取后两位
                        short_year = year[2:]
                        
                        # 遍历每个日期及其时间段
                        for day, time_slots in sorted(days_info.items()):
                            time_slots_str = ", ".join(time_slots)
                            # 格式: "25-3-10（08:00-09:00, 14:00-15:00）"
                            date_str = f"{short_year}-{int(month)}-{day}（{time_slots_str}）"
                            formatted_dates.append(date_str)
                    
                    # 添加医生和日期信息
                    dates_text = "，".join(formatted_dates)
                    doc_info = f"{doc_name}({doc_id}) {dates_text}"
                    result_parts.append(doc_info)
                else:
                    # 不包含时间的版本
                    # 按年月分组收集日期
                    date_groups = {}
                    for appt in appointments:
                        date_str = appt.get("date", "")
                        if not date_str or len(date_str) < 10:
                            continue
                            
                        # 分解日期为年月和日
                        year_month = date_str[:7]  # 例如 "2025-03"
                        day = int(date_str[8:10])  # 转换为数字，例如 4
                        
                        # 将日添加到对应年月组
                        if year_month not in date_groups:
                            date_groups[year_month] = []
                        date_groups[year_month].append(day)
                    
                    # 格式化日期组为更易读的形式
                    formatted_dates = []
                    for year_month, days in sorted(date_groups.items()):
                        year, month = year_month.split("-")
                        # 年份取后两位
                        short_year = year[2:]
                        
                        # 去重并排序日期
                        days = sorted(set(days))
                        
                        # 每天单独生成一条记录，数字格式
                        for day in days:
                            date_str = f"{short_year}-{int(month)}-{day}"
                            formatted_dates.append(date_str)
                    
                    # 添加医生和日期信息
                    dates_text = "，".join(formatted_dates)
                    doc_info = f"{doc_name}({doc_id}) {dates_text}"
                    result_parts.append(doc_info)
            
            # 添加空行分隔不同科室
            result_parts.append("")
        
        # 组合所有部分
        result = "\n".join(result_parts)
        return result
    except Exception as e:
        log_exception("format_appointment_slot", e, "formatAppointmentSlot")
        # 如果格式化失败，返回原始值的字符串表示
        return str(value)


def format_departments_info(value):
    """
    从号源信息中提取科室信息
    
    Args:
        value: 原始预约时间槽信息(JSON格式)
        
    Returns:
        str: 格式化后的科室信息文本
    """
    try:
        if not isinstance(value, list):
            return str(value)
            
        result_parts = []
        
        # 只提取有医生的科室信息
        for dept in value:
            dept_id = dept.get("departmentId", "")
            dept_name = dept.get("departmentName", "")
            doctors = dept.get("doctors", [])
            
            # 只添加有医生的科室
            if doctors:
                dept_info = f"{dept_name}({dept_id})"
                result_parts.append(dept_info)
        
        # 组合所有部分
        result = "\n".join(result_parts)
        return result
    except Exception as e:
        log_exception("format_departments_info", e, "formatDepartmentsInfo")
        # 如果格式化失败，返回原始值的字符串表示
        return str(value)


def format_appointment_date_weekday(value):
    """
    从号源信息中提取所有出现的日期和对应的星期几
    
    Args:
        value: 原始预约时间槽信息(JSON格式)
        
    Returns:
        str: 格式化后的日期和星期信息文本
    """
    try:
        from datetime import datetime
        
        if not isinstance(value, list):
            return str(value)
            
        # 收集所有不重复的日期
        all_dates = set()
        
        # 遍历所有科室和医生的预约信息
        for dept in value:
            doctors = dept.get("doctors", [])
            if not doctors:
                continue
                
            for doctor in doctors:
                appointments = doctor.get("appointments", [])
                for appt in appointments:
                    date_str = appt.get("date", "")
                    if date_str and len(date_str) >= 10:
                        all_dates.add(date_str[:10])  # 只取日期部分 YYYY-MM-DD
        
        # 星期几的中文表示
        weekday_names = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
        
        # 转换日期并添加星期信息
        date_info = []
        for date_str in sorted(all_dates):
            try:
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                # datetime的weekday()返回0-6，其中0是星期一
                weekday = weekday_names[date_obj.weekday()]
                # 格式: "2025-03-10 星期一"
                date_info.append(f"{date_str} {weekday}")
            except:
                # 如果解析失败，只添加日期字符串
                date_info.append(date_str)
        
        # 组合所有日期信息
        result = "\n".join(date_info)
        return result
    except Exception as e:
        log_exception("format_appointment_date_weekday", e, "formatAppointmentDateWeekday")
        # 如果格式化失败，返回原始值的字符串表示
        return str(value)
# AI Service
> ⚠️ **注意**：本文使用uv代替pip安装依赖，使用pyproject.toml代替requirements.txt管理依赖。建议在新checkout的工程采用本文说明的操作流程

## 环境要求
- Python >= 3.8 (目前开发人员使用3.10.6)
- CUDA >= 11.8（如果需要 GPU 支持）

## 安装步骤

### 1. 安装 uv（本机已有uv可跳过这步）

Windows:
```powershell
# 使用 PowerShell 通过官方安装脚本安装 uv

方案1：
(Invoke-WebRequest -Uri https://astral.sh/uv/install.ps1 -UseBasicParsing).Content | python -

方案2：
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

Linux:
```bash
# 使用 curl 通过官方安装脚本安装 uv
curl -LsSf https://astral.sh/uv/install.sh | sh
```

验证安装：
```bash
uv --version
```

### 2. 安装依赖
> ⚠️ **注意**：由于 uv 虚拟环境和 pip 虚拟环境不太一样，需要新 checkout 的工程里这么干

方案1：
```powershell
# 创建虚拟环境并安装所有依赖（包括开发依赖）
uv venv
uv pip install -e ".[dev]"

# 如果不需要开发依赖，使用：
uv pip install -e .
```

方案2：
使用conda安装虚拟环境和依赖，先安装conda，然后执行如下指令
```powershell
uv pip install -e ".[dev]"
```

### 3. 配置.env.secret和.env.debug
拷贝[app/.env.secret.example](app/.env.secret.example)文件为新的.env.secret文件
在其中填写api-key、密码等敏感信息
然后选择.env.debug里面的测试服务器


### 4. 编译多语言文件
执行一次以下脚本，生成多语言文件
```
python app/lang/compile_translation.py
```
### 5. 验证服务器启动
执行以下指令启动服务器，验证是否能成功启动
```
uvicorn app.main:app --host 0.0.0.0 --port 8000
```


## 项目结构
```
ai-service/
├── app/                # 主应用目录
├── tests/             # 测试目录
├── .vscode/           # VSCode 配置
├── pyproject.toml     # 项目配置和依赖管理
└── README.md          # 项目说明文档
```

## 配置说明

项目使用 `pyproject.toml` 管理所有配置：

1. **依赖管理**：
   - 基础依赖在 `[project.dependencies]` 中定义
   - 开发依赖在 `[project.optional-dependencies]` 中定义

2. **构建配置**：
   - 使用 `hatchling` 作为构建后端
   - 支持 PEP 621 标准的项目元数据

3. **测试配置**：
   - 使用 pytest 进行测试
   - 测试文件位于 `tests` 目录

4. **预提交**
   - 使用 pre-commit 管理预提交钩子
   - 配置在 `.pre-commit-config.yaml` 中定义，包括 formatter、linter 等等

### 附：pre-commit 简单用法

1. 安装 pre-commit 及提交钩子

```shell
uv tool install pre-commit  # 或使用 pip
pre-commit install
```

安装提交钩子后，每次提交就会自动执行钩子。如果钩子自动执行了修复（如格式化），请暂存新修复的文件重新提交。

2. 手动检查

可以使用以下命令手动对暂存的文件进行检查

```shell
# after git add
pre-commit
```

3. 忽略钩子进行提交

如果钩子检查没过，也不想修复，可以使用以下命令强制提交：

```shell
git commit --no-verify -m "xxx"
```

import logging
import os
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path
import traceback
import uuid
import sys
from app.utils.lark import send_lark_message
from app.config import SERVER_NAME  # 添加这行导入


"""
根据device_id、日期分目录，日志路径格式：
{LOG_BASE_DIR}/{device_id}/{date}/llm_log.log
通常情况下device_id是一体机的设备id
pushConfig这类由业务服务器发起的请求，device_id是"biz_server"
AI服务器自测的device_id，可能是"ai_server"或"knowledge_handler_test"或其他
如果接口调用方没有传，会被汇总到"default_device"
"""


# 全局变量控制是否输出到控制台
LOG_TO_CONSOLE = False


def get_log_base_dir() -> str:
    # 首先检查环境变量
    env_log_dir = os.environ.get("AI_SERVICE_LOG_DIR")
    if env_log_dir:
        return env_log_dir

    # 如果环境变量未设置，尝试基于项目结构确定目录
    try:
        # 假设 logger.py 位于 app/utils/ 目录下
        project_root = Path(__file__).parent.parent.parent
        return str(project_root / "log")
    except Exception:
        # 如果上述方法失败，使用当前工作目录
        return os.path.join(os.getcwd(), "log")


# 设置日志目录
LOG_BASE_DIR = get_log_base_dir()


class LLMLogger:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LLMLogger, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        self.base_log_dir = LOG_BASE_DIR
        # 确保日志基础目录存在
        os.makedirs(self.base_log_dir, exist_ok=True)
        print(f"[Info] Log directory initialized at: {self.base_log_dir}")

    def get_logger(self, device_id: str) -> logging.Logger:
        today = datetime.now().strftime("%Y-%m-%d")
        log_dir = os.path.join(self.base_log_dir, str(device_id), today)
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, "llm_log.log")

        logger_name = f"llm_logger_{device_id}_{today}"  # 添加日期到logger名称
        logger = logging.getLogger(logger_name)
        if not logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

            # 控制台处理器（根据全局变量决定）
            if LOG_TO_CONSOLE:
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setFormatter(formatter)
                logger.addHandler(console_handler)

            logger.setLevel(logging.INFO)
            logger.propagate = False  # 防止日志传播到父logger

        return logger

    def log_llm_request(
        self,
        device_id: str,
        model_info,
        messages: List,
        functions: List,
        response_format=None,
    ):
        logger = self.get_logger(device_id)
        log_message = f"""
LLM Request:
Model Info: {model_info}
Messages: {messages}
Functions: {functions}
Response Format: {response_format}
        """
        logger.info(log_message)

    def log_embedding_request(
        self,
        device_id: str,
        model_info,
        text,
        caller_module: str, # 调用者模块，区分是QA请求，RAG请求还是文档上传请求
    ):
        logger = self.get_logger(device_id)
        log_message = f"""
Embedding Request:
Model Info: {model_info}
Caller Module: {caller_module}
Text: {text}
        """
        logger.info(log_message)

    def log_llm_response(self, device_id: str, response: Any):
        logger = self.get_logger(device_id)
        log_message = f"""
LLM Response:
{response}
        """
        logger.info(log_message)

    # 新增一个打印任意文本的方法
    def log_text(self, device_id: str, text: str):
        logger = self.get_logger(device_id)
        logger.info(text)

    def _analyze_exception_chain(self, exception: Exception) -> str:
        """分析异常链并提取原始异常和详细调用链"""
        root_cause_info = ""
        try:
            # 递归追踪异常链
            def trace_exception_chain(exception, depth=0, is_top_level=False):
                if exception is None or depth > 5:  # 增加深度限制，防止过深递归
                    return ""
                
                # 获取异常的完整类型名称（包括模块名）
                module_name = exception.__class__.__module__
                class_name = exception.__class__.__name__
                full_exc_name = f"{module_name}.{class_name}" if module_name != "builtins" else class_name
                
                # 顶层异常在Message行已经显示，这里就不重复显示完整内容了
                if is_top_level:
                    exc_info = f"{'  '*depth}[{full_exc_name}]"
                else:
                    exc_info = f"{'  '*depth}[{full_exc_name}] {str(exception)}"
                
                # 同时检查 __cause__ 和 __context__
                if hasattr(exception, '__cause__') and exception.__cause__:
                    exc_info += f"\n{'  '*depth}Caused by:\n"
                    exc_info += trace_exception_chain(exception.__cause__, depth+1, False)
                
                if hasattr(exception, '__context__') and exception.__context__ and exception.__context__ is not exception.__cause__:
                    exc_info += f"\n{'  '*depth}During handling of:\n"
                    exc_info += trace_exception_chain(exception.__context__, depth+1, False)
                
                return exc_info
                
            # 始终添加异常链信息，即使没有__cause__或__context__
            root_cause_info = f"\n\nException Chain:\n{trace_exception_chain(exception, is_top_level=True)}"
            
        except Exception as ex:
            root_cause_info = f"\n\nFailed to trace exception chain: {str(ex)}"
        
        return root_cause_info

    def log_exception(self, device_id: str, e: Exception, context: str = "") -> str:
        logger = self.get_logger(device_id)
        error_id = str(uuid.uuid4())
        error_message = f"Error ID {error_id} occurred{' in ' + context if context else ''}: {type(e).__name__}"
        stack_trace = traceback.format_exc()

        log_message = f"""
Exception:
{error_message}
Stack Trace:
{stack_trace}
        """
        logger.error(log_message)

        # 分析异常链提取原始异常和详细调用链
        root_cause_info = self._analyze_exception_chain(e)

        # 飞书消息发送部分
        lark_message = f"""🚨 Error Alert
Server: {SERVER_NAME}
Device: {device_id}
Error ID: {error_id}
Type: {type(e).__name__}
Context: {context if context else 'N/A'}
Message: {str(e)}{root_cause_info}"""
        
         # 当SERVER_NAME不是DEV_LOCAL时才发送飞书通知
        if SERVER_NAME.upper() != "DEV_LOCAL":
            try:
                send_lark_message(lark_message)
            except Exception as lark_e:
                logger.error(f"Failed to send Lark message: {str(lark_e)}")
        else:
            logger.info("SERVER_NAME is DEV_LOCAL, skipping Lark notification.")

        return error_message

    def log_knowledge_handler(
        self,
        device_id: str,
        query: str,
        rewrite_query: str,
        output_source: str,
        vector_search_results: List[Dict],
        final_result: str,
    ):
        logger = self.get_logger(device_id)
        log_message = f"""
Knowledge Handler:
Query: {query}
Rewrite Query: {rewrite_query if rewrite_query else 'None'}
Output Source: {output_source}
Vector Search:
{self._format_vector_search_results(vector_search_results)}
Final Result: {final_result}
        """
        logger.info(log_message)

    def _format_vector_search_results(self, results: List[Dict]) -> str:
        formatted_results = []
        for result in results:
            if "question" in result:
                formatted_results.append(
                    f"Question: {result['question']}, ID: {result['question_id']}, QnA Pair ID: {result['qna_pair_id']}, Similarity: {result['score']}"
                )
            elif "chunk_content" in result:
                content = result["chunk_content"]
                if len(content) > 50:
                    content = content[:20] + "..." + content[-20:]
                formatted_results.append(
                    f"Chunk: {content}, ID: {result['chunk_id']}, Document ID: {result['document_id']}, Similarity: {result['score']}"
                )
        return "\n".join(formatted_results)

    def log_retry(self, device_id: str, retry_count: int, error: Exception, context: str = "") -> str:
        logger = self.get_logger(device_id)
        retry_id = str(uuid.uuid4())
        retry_message = f"Retry ID {retry_id} occurred{' in ' + context if context else ''}: {type(error).__name__}"
        stack_trace = traceback.format_exc()

        log_message = f"""
Retry Attempt #{retry_count}:
{retry_message}
Stack Trace:
{stack_trace}
        """
        logger.warning(log_message)

        # 分析异常链提取原始异常和详细调用链
        root_cause_info = self._analyze_exception_chain(error)

        # 发送飞书消息
        lark_message = f"""⚠️ Retry Alert
Server: {SERVER_NAME}
Device: {device_id}
Retry ID: {retry_id}
Attempt: #{retry_count}
Type: {type(error).__name__}
Context: {context if context else 'N/A'}
Message: {str(error)}{root_cause_info}"""
        
        # 当SERVER_NAME不是DEV_LOCAL时才发送飞书通知
        if SERVER_NAME.upper() != "DEV_LOCAL":
            try:
                send_lark_message(lark_message)
            except Exception as lark_e:
                logger.error(f"Failed to send Lark message for retry: {str(lark_e)}")
        else:
            logger.info("SERVER_NAME is DEV_LOCAL, skipping Lark retry notification.")

        return retry_message

    def log_chat_request(
        self,
        device_id: str,
        request: Any,
        language: str = "zh-CN"
    ):
        logger = self.get_logger(device_id)
        log_message = f"""
Chat Request:{str(request)}
Language: {language}
        """
        logger.info(log_message)

    def log_chat_response(self, device_id: str, response: Any):
        logger = self.get_logger(device_id)
        log_message = f"""
Chat Response:
{response}
        """
        logger.info(log_message)



    def log_sql_script_execution(
        self,
        device_id: str,
        version: str,
        script_name: str,
        changes: List[Dict],
        total_count: int,
        execution_time: float = None
    ):
        """记录SQL脚本执行的详细修改信息"""
        logger = self.get_logger(device_id)
        
        # 格式化修改详情
        change_details = []
        for change in changes:
            if change.get("table") == "PromptConfig":
                change_details.append(
                    f"  - promptId: {change['old_value']} → {change['new_value']}"
                )
            elif change.get("table") == "IntentionConfig":
                org_id = change.get("organization_id", "N/A")
                language = change.get("language", "N/A")
                removed_keys = change.get("removed_keys", [])
                change_details.append(
                    f"  - 组织ID {org_id} 语言 {language}: 删除空值键 {removed_keys}"
                )
            else:
                # 通用格式
                change_details.append(f"  - {change}")
        
        change_summary = "\n".join(change_details) if change_details else "无具体变更记录"
        
        log_message = f"""
========== SQL脚本执行记录 ==========
版本: {version}
脚本名称: {script_name}
执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'执行耗时: %.2f秒' % execution_time if execution_time else ''}
总修改数量: {total_count}

=== 修改详情 ===
{change_summary}

执行状态: 成功完成
=====================================
        """
        logger.info(log_message)

    def log_sql_script_error(
        self,
        device_id: str,
        version: str,
        script_name: str,
        error: Exception,
        execution_time: float = None
    ):
        """记录SQL脚本执行错误"""
        logger = self.get_logger(device_id)
        
        log_message = f"""
========== SQL脚本执行失败 ==========
版本: {version}
脚本名称: {script_name}
失败时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'执行耗时: %.2f秒' % execution_time if execution_time else ''}
错误信息: {str(error)}
执行状态: 失败
=====================================
        """
        logger.error(log_message)


# 创建全局单例实例
_llm_logger = LLMLogger()


# 添加模块级别的函数
def log_llm_request(
    device_id: str,
    model_info,
    messages: List,
    functions: List = None,
    response_format=None,
):
    _llm_logger.log_llm_request(
        device_id, model_info, messages, functions, response_format
    )

def log_embedding_request(
    device_id: str,
    model_info,
    text,
    caller_module: str, # 调用者模块，区分是QA请求，RAG请求还是文档上传请求
):
    _llm_logger.log_embedding_request(device_id, model_info, text, caller_module)


def log_llm_response(device_id: str, response: Any):
    _llm_logger.log_llm_response(device_id, response)


def log_knowledge_handler(
    device_id: str,
    query: str,
    rewrite_query: str,
    output_source: str,
    vector_search_results: List[Dict],
    final_result: str,
):
    _llm_logger.log_knowledge_handler(
        device_id,
        query,
        rewrite_query,
        output_source,
        vector_search_results,
        final_result,
    )


def log_exception(device_id: str, e: Exception, context: str = "") -> str:
    return _llm_logger.log_exception(device_id, e, context)


def log_text(device_id: str, text: str):
    _llm_logger.log_text(device_id, text)


def log_retry(device_id: str, retry_count: int, error: Exception, context: str = "") -> str:
    return _llm_logger.log_retry(device_id, retry_count, error, context)


def log_chat_request(
    device_id: str,
    request: Any,
    language: str = "zh-CN"
):
    _llm_logger.log_chat_request(
        device_id, request, language
    )


def log_chat_response(device_id: str, response: Any):
    _llm_logger.log_chat_response(device_id, response)




def log_sql_script_execution(
    device_id: str,
    version: str,
    script_name: str,
    changes: List[Dict],
    total_count: int,
    execution_time: float = None
):
    """记录SQL脚本执行的详细修改信息"""
    _llm_logger.log_sql_script_execution(
        device_id, version, script_name, changes, total_count, execution_time
    )


def log_sql_script_error(
    device_id: str,
    version: str,
    script_name: str,
    error: Exception,
    execution_time: float = None
):
    """记录SQL脚本执行错误"""
    _llm_logger.log_sql_script_error(
        device_id, version, script_name, error, execution_time
    )


# 修改使用示例
def example_usage():
    # 使用模块级别的函数记录 LLM 请求
    log_llm_request(
        device_id="device_002",
        model_info={"model": "gpt-3.5-turbo", "temperature": 0.7},
        messages=[{"role": "user", "content": "你好，最近如何？"}],
        functions=[{"name": "get_weather", "description": "获取当前天气"}],
    )

    # 添加异常测试
    try:
        # 故意制造一个异常
        result = 1 / 0
    except Exception as e:
        log_exception(device_id="test_device", e=e, context="测试异常日志和飞书通知")

    print("异常测试完成。请检查日志文件和飞书通知。")


def test_retry_and_error():
    device_id = "test_device"
    
    # 测试retry日志
    try:
        raise ConnectionError("连接数据库失败")
    except Exception as e:
        log_retry(device_id=device_id, retry_count=1, error=e, context="数据库连接")
        log_retry(device_id=device_id, retry_count=2, error=e, context="数据库连接")
    
    # 测试error日志
    try:
        raise ValueError("无效的输入参数")
    except Exception as e:
        log_exception(device_id=device_id, e=e, context="参数验证")


if __name__ == "__main__":
    example_usage()
    test_retry_and_error()

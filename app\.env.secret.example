###########################################
# 敏感配置示例文件
# 使用说明：
# 1. 复制本文件并重命名为 .env.secret
# 2. 根据实际情况填写以下配置项
# 3. .env.secret 文件不要提交到 git
###########################################

###########################################
# 动态配置（支持热更新）
###########################################

# OpenAI API密钥 
OPENAI_API_KEY=

# 智谱AI API密钥
ZHIPU_API_KEY=

# Deepseek API密钥 
DEEPSEEK_API_KEY=

# 阿里云Deepseek API秘钥
DEEPSEEK_API_KEY=

# Azure API秘钥
AZURE_API_KEY=

###########################################
# 静态配置（修改后需重启服务）
###########################################

# 调试模式开关 (必填)
# 开发环境设置为 True
# 生产环境必须删除此项或设为 False
IS_DEBUG=True

# MySQL数据库账号 (必填)
MYSQL_USER=root

# MySQL数据库密码 (必填)
# 注意：确保密码强度符合安全要求
MYSQL_PASSWORD=

# 服务器标识 (必填)
# 用于区分不同的部署环境，例如：
# - PROD_BJ (北京生产环境)
# - PROD_SG (新加坡生产环境)
# - DEV_LOCAL (本地开发环境)
# - TEST_4080 (4080测试环境)
SERVER_NAME=
import asyncio
import json
import threading
import time
from app.utils.sql_script import *
from app.utils.logger import log_sql_script_execution, log_sql_script_error


def get_latest_version():
    """
    获取最新的SQL更新版本
    """
    # 获取所有sql_update_v开头的函数名
    sql_update_functions = [
        name for name in globals() if name.startswith("sql_update_v")
    ]

    if not sql_update_functions:
        return None

    # 提取版本号并排序
    versions = []
    for func_name in sql_update_functions:
        # 从函数名中提取版本号，如sql_update_v2_1_0 -> 2.1.0
        version_part = func_name.replace("sql_update_v", "")
        version = version_part.replace("_", ".")
        versions.append(version)

    # 按版本号排序，获取最新版本
    def version_key(v):
        return tuple(map(int, v.split(".")))

    versions.sort(key=version_key, reverse=True)
    return versions[0] if versions else None


def sql_update_execute(version: str):
    """
    执行SQL更新脚本并记录日志
    """
    start_time = time.time()
    device_id = "sql_update_server"  # 固定的服务器标识
    
    # 把版本号中的小数点替换为下划线
    version_str = version.replace(".", "_")
    func_name = f"sql_update_v{version_str}"

    # 检查函数是否存在并调用
    if func_name in globals():
        func = globals()[func_name]
        try:
            result = func()
            execution_time = time.time() - start_time
            
            # 处理返回结果
            if isinstance(result, dict):
                # 新格式的返回结果
                script_name = result.get("script_name", func_name)
                changes = result.get("changes", [])
                updated_count = result.get("updated_count", 0)
                
                # 记录成功日志
                log_sql_script_execution(
                    device_id=device_id,
                    version=version,
                    script_name=script_name,
                    changes=changes,
                    total_count=updated_count,
                    execution_time=execution_time
                )
                
                return updated_count
            else:
                # 兼容旧格式的返回结果
                log_sql_script_execution(
                    device_id=device_id,
                    version=version,
                    script_name=func_name,
                    changes=[],
                    total_count=result or 0,
                    execution_time=execution_time
                )
                return result
                
        except Exception as e:
            execution_time = time.time() - start_time
            log_sql_script_error(
                device_id=device_id,
                version=version,
                script_name=func_name,
                error=e,
                execution_time=execution_time
            )
            raise
    else:
        error = ValueError(f"Unsupported version: {version}")
        log_sql_script_error(
            device_id=device_id,
            version=version,
            script_name=func_name,
            error=error
        )
        raise error


async def sql_update_stream(version: str):
    """
    流式执行SQL更新
    生成器函数，产生更新状态消息
    """
    try:
        # 开始执行前发送开始消息
        yield {"status": "started", "message": "开始执行SQL更新"}
        await asyncio.sleep(1)

        # 用于存储执行结果的变量
        result = {"value": None, "error": None, "done": False}

        def execute_sql():
            try:
                result["value"] = sql_update_execute(version)
                result["done"] = True
            except Exception as e:
                result["error"] = str(e)
                result["done"] = True

        # 在后台线程中执行SQL更新
        thread = threading.Thread(target=execute_sql)
        thread.start()

        # 持续发送进度消息直到执行完成
        while not result["done"]:
            yield {"status": "updating", "message": "正在更新数据"}
            await asyncio.sleep(1)

        # 等待线程完成
        thread.join()

        # 发送最终结果
        if result["error"]:
            error_msg = result["error"]
            yield {"status": "error", "message": f"更新失败: {error_msg}"}
        else:
            update_count = result["value"]
            yield {
                "status": "completed",
                "message": f"SQL脚本执行成功。{update_count} 条数据已更新。当前版本: {version}",
            }

    except Exception as e:
        error_str = str(e)
        yield {"status": "error", "message": f"更新过程中发生错误: {error_str}"}


def execute_functions_and_merge_results(script_name: str, functions: list):
    """
    执行函数列表并整合结果
    
    Args:
        script_name: 脚本名称
        functions: 要执行的函数列表
    
    Returns:
        dict: 整合后的结果
    """
    all_changes = []
    total_updated = 0
    
    for func in functions:
        try:
            result = func()
            
            # 处理不同的返回格式
            if isinstance(result, dict):
                # 新格式：包含 changes, updated_count 等
                if "changes" in result:
                    all_changes.extend(result.get("changes", []))
                if "updated_count" in result:
                    total_updated += result.get("updated_count", 0)
                    
            else:
                # 旧格式：直接返回数字或其他值
                if isinstance(result, (int, float)):
                    total_updated += result
                    
        except Exception as e:
            print(f"[错误] 执行函数 {func.__name__} 时发生错误: {str(e)}")
            # 可以选择继续执行其他函数，或者抛出异常
            raise
    
    return {
        "script_name": script_name,
        "changes": all_changes,
        "updated_count": total_updated,
        "executed_functions": [func.__name__ for func in functions]
    }


def sql_update_v2_1_0():
    """
    通用的函数执行器，支持执行函数列表并整合结果
    """
    # 定义要执行的函数列表
    functions = [
        remove_unavailable_intention_config,
        change_character_id_from_number_to_name,
        # 可以添加更多函数，包括其他版本的 sql_update 函数
        # sql_update_v2_0_0,  # 示例：调用其他版本
    ]
    
    return execute_functions_and_merge_results("sql_update_v2_1_0", functions)
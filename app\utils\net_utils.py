# 1. 导入区
import json
import time
import httpx
import httpcore
from typing import Union, Callable, TypeVar, Any, Awaitable
from pydantic import BaseModel
from collections import OrderedDict
from functools import wraps
from app.config import IS_DEBUG
import asyncio

# 在导入区添加OpenAI的错误类
from openai import (
    APIConnectionError,
    RateLimitError, 
    InternalServerError,
    APIError,  # 新增
    APIStatusError  # 新增
)


# 2. 常量定义
RESPONSE_CODE_END = 0
RESPONSE_CODE_SEGMENT_END = 1
RESPONSE_CODE_CONTINUE = 2
ERROR_CODE_START = 300

# AI服务器错误
ERROR_AI_SERVER = 1000
# AI组织配置错误
ERROR_ORG_CONFIG = 1001
# message字段非法
ERROR_MESSAGE_INVALID_ARRAY = 1002
# message的role字段非法
ERROR_MESSAGE_INVALID_ROLE = 1003
# message的content字段非法
ERROR_MESSAGE_INVALID_CONTENT = 1004
# 业务请求错误
ERROR_BIZ_REQUEST = 2000
# LLM服务错误
ERROR_LLM_SERVER = 3000
# 翻译服务错误
ERROR_TRANSLATE_SERVICE = 4000

# 添加新的常量定义
NO_RETRY_ERROR_TYPES = {
    "data_inspection_failed",  # 内容审核失败
    "invalid_request_error",   # 无效请求错误
    "permission_denied",       # 权限被拒绝
}

# 3. 配置相关
NET_CONFIG = {
    "timeout": {
        "default": 20,        # 普通请求超时时间（秒）
        "embedding": 5,      # embedding请求超时时间（秒）
        "stream": 10,        # 流式请求超时时间（秒）
    },
    "retry": {
        "default": {
            "max_time": 30,      # 普通请求最大重试时长（秒）
            "min_interval": 3,    # 普通请求最小重试间隔（秒）
        },
        "stream": {
            "max_time": 30,      # 流式请求最大重试时长（秒）
            "min_interval": 3,    # 流式请求最小重试间隔（秒）
        },
        "embedding": {
            "max_time": 30,      # embedding最大重试时长（秒）
            "min_interval": 1,    # embedding最小重试间隔（秒）
        }
    }
}

# 替换原有的常量定义
REQUEST_TIMEOUT = NET_CONFIG["timeout"]["default"]
REQUEST_EMBEDDING_TIMEOUT = NET_CONFIG["timeout"]["embedding"]
STREAM_TIMEOUT = NET_CONFIG["timeout"]["stream"]

# 重试配置需要根据请求类型动态选择
def get_retry_config(request_type: str = "default"):
    """获取指定类型请求的重试配置"""
    retry_config = NET_CONFIG["retry"].get(request_type, NET_CONFIG["retry"]["default"])
    return retry_config["max_time"], retry_config["min_interval"]

def should_retry_error(exception):
    """判断是否应该重试该错误"""
    if isinstance(exception, APIStatusError):
        # 检查是否包含特定的错误类型
        try:
            error_type = exception.response.json().get('error', {}).get('type')
            return error_type not in NO_RETRY_ERROR_TYPES
        except Exception:
            pass
    return True

# 修改 retry_decorator 函数
def retry_decorator(before_sleep, request_type: str = "default", retry_all: bool = False):
    max_time, min_interval = get_retry_config(request_type)
    
    def keep_min_wait(retry_state: RetryCallState):
        # 如果不是需要等待的错误，立即重试
        if not getattr(retry_state, 'need_wait', False):
            return 0
        
        if retry_state.attempt_number == 1:
            last_outcome_time = retry_state.start_time
        else:
            last_outcome_time = retry_state.outcome_timestamp
        elapsed = time.monotonic() - last_outcome_time
        return max(min_interval - elapsed, 0)

    # 自定义重试条件
    def custom_retry_condition(retry_state):
        if retry_state.outcome is None:
            return False
            
        exception = retry_state.outcome.exception()

        if not exception:
            return False
        
        RETRY_EXCEPTIONS = (
            TimeoutError,
            APIConnectionError,
            ClientConnectorError,
            ServerTimeoutError,
            InternalServerError,
            APIError,
            httpx.TransportError,
            httpcore.RemoteProtocolError,
            httpcore.TimeoutException,
            httpcore.NetworkError,
        )
        
        need_wait = isinstance(exception, RETRY_EXCEPTIONS) or should_retry_error(exception)
        retry_state.need_wait = need_wait
        
        # 根据 retry_all 参数决定是否所有异常都重试
        not_cancelled = not isinstance(exception, asyncio.CancelledError)
        return not_cancelled and (retry_all or need_wait)

    return retry(
        stop=stop_after_delay(max_time),
        wait=keep_min_wait,
        retry=custom_retry_condition,
        before_sleep=before_sleep,
        reraise=True,
    )


# 4. 基础类定义
class Response(BaseModel):
    code: int
    data: str | dict | list | None = None
    errorMsg: str = ""
    language: str | None = None  # 新增语言字段

    @property
    def success(self):
        return self.code < ERROR_CODE_START

    def __str__(self):
        if self.data:
            return f"Response(code={self.code}, data={self.data}, errorMsg='{self.errorMsg}', language='{self.language}')"
        else:
            return f"Response(code={self.code}, errorMsg='{self.errorMsg}', language='{self.language}')"

    def toJson(self):
        return json.dumps(self.model_dump(), ensure_ascii=False)

    def toStream(self) -> bytes:
        return f"data: {self.toJson()}\n\n".encode("utf-8")

    def to_sse(self) -> str:
        """
        将 Response 对象转换为 SSE (Server-Sent Events) 格式的字符串
        返回格式：data: {"code": xxx, "data": xxx, "errorMsg": "xxx"}
        """
        return f"data: {self.toJson()}\n\n"


# 将响应包装成异常，方便抛出处理
class ResponseException(Exception):
    def __init__(self, response: Response):
        self.response = response
        super().__init__(str(response))


# 将数据包装成Response
def wrap_response(data, code=0, language=None) -> Response:
    return Response(code=code, data=data, language=language)


# 将聊天数据包装成Response的data
def wrap_chat_data(
    sessionId: str = "", source: int = 0, message: str = "", extra: dict = None
):
    return {
        "sessionId": sessionId,
        "source": source,
        "message": message,
        "extra": extra,
    }


# 从obj中获取key的值，如果obj是dict，按dict方式获取，如果obj是string，先解析成json，再获取
def get_value(obj, key):
    if isinstance(obj, dict):
        return obj.get(key)
    elif isinstance(obj, str):
        try:
            json_obj = json.loads(obj)
            return json_obj.get(key) if isinstance(json_obj, dict) else None
        except json.JSONDecodeError:
            return None
    else:
        # 处理其他类型（包括None, int, list等）
        return None


from tenacity import (
    retry,
    stop_after_delay,
    retry_if_exception_type,
    RetryCallState,
)
from aiohttp import ClientConnectorError, ServerTimeoutError, ClientOSError


# 5. 工具函数
def wrap_error(code: int, error: Union[Exception, str], path: str = None, language=None) -> Response:
    if IS_DEBUG and isinstance(error, Exception):
        raise error

    if isinstance(error, Exception):
        errorMsg = str(error)
    else:
        errorMsg = error

    if path:
        errorMsg = f"path:{path} error: {errorMsg}"

    return Response(code=code, errorMsg=errorMsg, language=language)


def add_sys_prompt(messages, sys_prompt: str):
    """
    添加系统消息到消息列表的开头
    """
    messages.insert(0, {"role": "system", "content": sys_prompt})
    return messages


def remove_sys_prompt(messages):
    """
    从消息列表的开头删除系统消息
    """
    if messages and messages[0]["role"] == "system":
        messages.pop(0)
    return messages


def gen_assistant_content(content: str | dict):
    if not isinstance(content, str):
        try:
            content = json.dumps(content, ensure_ascii=False)
        except Exception:
            content = str(content)
    return {"role": "assistant", "content": content or ""}


def process_stream_response(content: str, response: Response) -> Union[str, Response]:
    """
    处理流式响应内容，用于拼接和管理流式输出的文本片段。

    Args:
        content (str): 现有的缓冲区文本内容
        response (Response): 当前收到的响应对象，包含新的文本片段或结束标志
            结构: {
                "code": int,
                "data": {"message": str}
            }

    Returns:
        Union[str, Response]:
        - str: 中间片段，返回拼接后的字符串
        - Response: 结束标志或错误响应，返回完整的响应对象

    Note:
        响应码 RESPONSE_CODE_END 和 RESPONSE_CODE_SEGMENT_END 表示结束标志
    """
    # 如果是错误码，直接返回response
    if not response.success:
        return response

    # 获取当前片段的文本内容
    current_content = get_value(response.data, "message") or ""

    # 处理结束标志
    if response.code in [RESPONSE_CODE_END, RESPONSE_CODE_SEGMENT_END]:
        if content:
            cloned_response = response.model_copy()
            cloned_response.data["message"] = content + current_content
            return cloned_response
        else:
            return response

    # 处理中间片段
    if content:
        return content + current_content
    else:
        return current_content


import re


def safe_format(template: str, mapping: dict) -> str:
    """
    安全地格式化字符串模板，处理嵌套的花括号和不存在的键。

    此函数通过迭代替换过程来处理模板中的占位符。它可以安全地处理嵌套的花括号，
    并在遇到映射中不存在的键时保留原始占位符。

    :param template: 要格式化的字符串模板
    :param mapping: 包含用于替换的键值对的字典
    :return: 格式化后的字符串

    示例:
        template = "Hello {name}, you are {age} years old. {nonexistent}"
        mapping = {"name": "Alice", "age": "30"}
        result = safe_format(template, mapping)
        # 结果: "Hello Alice, you are 30 years old. {nonexistent}"
    """

    def replace(match):
        key = match.group(1)
        if "{" in key or "}" in key:
            return match.group(0)  # 保持原样
        value = mapping.get(key, match.group(0))
        return str(value)

    pattern = r"\{([^{}]+)\}"
    try:
        while True:
            new_template = re.sub(pattern, replace, template)
            if new_template == template:
                break
            template = new_template
        return template
    except Exception as e:
        if IS_DEBUG:
            raise e

        print(f"Error in safe_format: {str(e)}")
        return template


from collections import OrderedDict
from functools import wraps
from typing import Callable, TypeVar, Any

T = TypeVar("T")


class LRUCache:
    """
    LRU (Least Recently Used) 缓存装饰器

    用于缓存函数调用结果，当缓存达到容量上限时，移除最久未使用的项。
    使用 OrderedDict 维护访问顺序，使用额外的字典存储实际值。

    写这个方法而不是使用functools.lru_cache是因为functools.lru_cache在未命中时会创建实例
    而我们需要一个peek方法，在未命中时返回None，而不创建实例，
    对应情况是PushConfig时，有可能配置还不齐全，如果直接创建实例会报错。
    使用peek方法，只在缓存中有的时候使用，缓存中没有的时候不创建实例

    用法示例:
    @LRUCache(capacity=100)
    def get_user_info(user_id: str) -> dict:
        return fetch_user_from_db(user_id)
    """

    def __init__(self, capacity):
        self.capacity = capacity
        self._cache = OrderedDict()  # 仅用于追踪访问顺序
        self._dict = {}  # 存储实际的键值对

    def __call__(self, func: Callable[..., Union[T, Awaitable[T]]]) -> Callable[..., Union[T, Awaitable[T]]]:
        # 缓存查找和更新逻辑
        def get_cached_value(args):
            key = args[0] if args else None
            if key in self._cache:
                self._cache.move_to_end(key)  # 移动到最新位置
                return key, True, self._dict[key]
            return key, False, None
            
        def update_cache(key, value):
            if value is not None:
                self._dict[key] = value
                self._cache[key] = None
                # 超出容量时，移除最久未使用的项
                if len(self._cache) > self.capacity:
                    oldest = next(iter(self._cache))
                    del self._cache[oldest]
                    del self._dict[oldest]
            
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> T:
            key, hit, value = get_cached_value(args)
            if hit:
                return value
                
            # 缓存未命中：调用原函数并更新缓存
            value = await func(*args, **kwargs)
            update_cache(key, value)
            return value

        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> T:
            key, hit, value = get_cached_value(args)
            if hit:
                return value
                
            # 缓存未命中：调用原函数并更新缓存
            value = func(*args, **kwargs)
            update_cache(key, value)
            return value

        # 根据函数类型选择合适的包装器
        wrapper = async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

        def peek(key) -> T:
            """
            查看缓存中的值，如果未命中返回None，而不会创建
            """
            if key in self._cache:
                self._cache.move_to_end(key)
                return self._dict[key]
            return None

        # 为装饰后的函数添加额外的方法
        wrapper.peek = peek  # 添加查看方法
        wrapper.cache = self  # 暴露缓存实例
        return wrapper

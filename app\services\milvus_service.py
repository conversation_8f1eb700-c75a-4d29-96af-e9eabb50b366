# 标准库导入
import asyncio
import functools
import time
from dataclasses import dataclass
from typing import List, Dict, Tuple

# 第三方库导入
from pymilvus import (
    connections,
    Collection,
    FieldSchema,
    CollectionSchema,
    DataType,
    utility,
    exceptions as milvus_exceptions,
)
from pymilvus.client.types import LoadState

# 本地导入
from app.services.llm_client import get_embedding
from app.services.mysql_service import get_embedding_config
from app.services import DEVICE_AI_SERVER
from app.utils.logger import log_exception
from app.config import (
    MILVUS_HOST,
    MILVUS_PORT,
    MILVUS_PARAMS,
    VECTOR_DIMENSION,
    MAX_LANGUAGE_LENGTH,
    MAX_QUESTION_LENGTH,
    MAX_ANSWER_LENGTH,
    MAX_CHUNK_CONTENT_LENGTH,
    Env,
)
from app.utils.logger import log_text


def retry_on_milvus_error(max_retries=3, retry_delay=1, collection_type: str = None):
    """重试装饰器，处理Milvus操作失败的情况

    Args:
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）
        collection_type: 集合类型，必须是 "question" 或 "chunk"
    """
    if collection_type not in ["question", "chunk", None]:
        raise ValueError("collection_type must be either 'question' or 'chunk'")

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if asyncio.iscoroutinefunction(func):
                return async_wrapper(*args, **kwargs)
            else:
                return sync_wrapper(*args, **kwargs)

        async def async_wrapper(*args, **kwargs):
            # 从参数中获取 organization_id
            organization_id = kwargs.get("organization_id") or (
                args[0] if args else None
            )
            if organization_id is None:
                raise ValueError("organization_id is required")

            # 获取对应的集合信息
            _, _, model_type = get_collection_for_organization(organization_id)
            question_col_name, chunk_col_name = get_collection_names_by_type(model_type)

            # 根据装饰器参数确定使用哪个集合
            effective_collection_name = (
                question_col_name if collection_type == "question" else chunk_col_name
            )

            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except milvus_exceptions.MilvusException as e:
                    error_msg = f"Milvus operation failed (attempt {attempt + 1}/{max_retries}): {func.__name__}"
                    print(error_msg)
                    log_exception(DEVICE_AI_SERVER, e, error_msg)

                    if attempt == 0:
                        # 第一次尝试时，重新加载指定的集合
                        reload_success = await asyncio.to_thread(
                            reload_collections, effective_collection_name
                        )
                        if reload_success:
                            continue

                    if attempt == max_retries - 1:
                        raise

                    await asyncio.sleep(retry_delay)
                    if not await asyncio.to_thread(reconnect_milvus):
                        reconnect_error = (
                            f"Reconnect failed, skipping retry: {func.__name__}"
                        )
                        print(reconnect_error)
                        log_exception(DEVICE_AI_SERVER, e, reconnect_error)
                        raise
                except Exception as e:
                    unexpected_error = f"Unexpected error: {func.__name__}"
                    print(unexpected_error)
                    log_exception(DEVICE_AI_SERVER, e, unexpected_error)
                    raise

        def sync_wrapper(*args, **kwargs):
            # 从参数中获取 organization_id
            organization_id = kwargs.get("organization_id") or (
                args[0] if args else None
            )
            if organization_id is None:
                raise ValueError("organization_id is required")

            # 获取对应的集合信息
            _, _, model_type = get_collection_for_organization(organization_id)
            question_col_name, chunk_col_name = get_collection_names_by_type(model_type)

            # 根据装饰器参数确定使用哪个集合
            effective_collection_name = (
                question_col_name if collection_type == "question" else chunk_col_name
            )

            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except milvus_exceptions.MilvusException as e:
                    error_msg = f"Milvus operation failed (attempt {attempt + 1}/{max_retries}): {func.__name__}"
                    print(error_msg)
                    log_exception(DEVICE_AI_SERVER, e, error_msg)

                    if attempt == 0:
                        # 第一次尝试时，重新加载指定的集合
                        if reload_collections(effective_collection_name):
                            continue

                    if attempt == max_retries - 1:
                        raise

                    time.sleep(retry_delay)
                    if not reconnect_milvus():
                        reconnect_error = (
                            f"Reconnect failed, skipping retry: {func.__name__}"
                        )
                        print(reconnect_error)
                        log_exception(DEVICE_AI_SERVER, e, reconnect_error)
                        raise
                except Exception as e:
                    unexpected_error = f"Unexpected error: {func.__name__}"
                    print(unexpected_error)
                    log_exception(DEVICE_AI_SERVER, e, unexpected_error)
                    raise

        return wrapper

    return decorator


# 模型前缀到集合名称的映射
MODEL_COLLECTIONS = {
    "openai": {
        "question_collection_name": "question_collection_v3",
        "chunk_collection_name": "chunk_collection_v3",
        "question_collection": None,  # 运行时初始化
        "chunk_collection": None,  # 运行时初始化
    },
    "qwen": {
        "question_collection_name": "question_collection_qwen_v1",
        "chunk_collection_name": "chunk_collection_qwen_v1",
        "question_collection": None,  # 运行时初始化
        "chunk_collection": None,  # 运行时初始化
    },
    # 以后可以在这里添加更多模型的集合配置
}

# 默认模型，用于兼容性
DEFAULT_EMBEDDING_MODEL_TYPE = "openai"


def get_collection_for_organization(
    organization_id: int,
) -> Tuple[Collection, Collection, str]:
    """根据组织ID获取对应的集合

    Args:
        organization_id: 组织ID

    Returns:
        Tuple[Collection, Collection, str]: 返回(问题集合, 文档块集合, 集合类型前缀)
    """
    embedding_config = get_embedding_config(organization_id)
    model_name = embedding_config.get("model", "")

    # 确定模型类型
    model_type = DEFAULT_EMBEDDING_MODEL_TYPE
    for prefix in MODEL_COLLECTIONS.keys():
        if model_name.startswith(prefix):
            model_type = prefix
            break

    collections = MODEL_COLLECTIONS[model_type]
    return (
        collections["question_collection"],
        collections["chunk_collection"],
        model_type,
    )


def get_collection_names_by_type(model_type: str) -> Tuple[str, str]:
    """根据集合类型获取对应的集合名称

    Args:
        model_type: 集合类型前缀，如"openai"或"qwen"

    Returns:
        Tuple[str, str]: 返回(问题集合名称, 文档块集合名称)
    """
    if model_type not in MODEL_COLLECTIONS:
        model_type = DEFAULT_EMBEDDING_MODEL_TYPE

    collections = MODEL_COLLECTIONS[model_type]
    return (
        collections["question_collection_name"],
        collections["chunk_collection_name"],
    )


def connect_milvus():
    # 检查是否已经连接
    if connections.has_connection("default"):
        print("[Info] Already connected to Milvus")
    else:
        connections.connect(host=MILVUS_HOST, port=MILVUS_PORT)
        print("[Info] Connected to Milvus")

    # 初始化所有模型的集合
    for model_type, collections in MODEL_COLLECTIONS.items():
        question_col_name = collections["question_collection_name"]
        chunk_col_name = collections["chunk_collection_name"]

        # 初始化问题集合
        if not utility.has_collection(question_col_name):
            print(f"[Info] Creating {model_type} question collection...")
            collections["question_collection"] = create_question_collection(
                question_col_name
            )
        else:
            print(f"[Info] Loading {model_type} question collection...")
            collections["question_collection"] = Collection(question_col_name)

        # 初始化文档块集合
        if not utility.has_collection(chunk_col_name):
            print(f"[Info] Creating {model_type} chunk collection...")
            collections["chunk_collection"] = create_chunk_collection(chunk_col_name)
        else:
            print(f"[Info] Loading {model_type} chunk collection...")
            collections["chunk_collection"] = Collection(chunk_col_name)

        # 加载集合
        collections["question_collection"].load()
        collections["chunk_collection"].load()

    print("[Info] All collections initialized")
    return True


def disconnect_milvus():
    # 释放所有集合
    for _, collections in MODEL_COLLECTIONS.items():
        if collections["question_collection"]:
            collections["question_collection"].release()
        if collections["chunk_collection"]:
            collections["chunk_collection"].release()

        # 重置集合引用
        collections["question_collection"] = None
        collections["chunk_collection"] = None


def reconnect_milvus():
    start_time = time.time()

    # 重新连接
    try:
        if connections.has_connection("default"):
            print("[Info] Connection exists, skipping reconnect")
        else:
            connections.connect(host=MILVUS_HOST, port=MILVUS_PORT)
            print("[Info] New connection established")

        # 重新加载所有模型的集合
        for model_type, collections in MODEL_COLLECTIONS.items():
            question_col_name = collections["question_collection_name"]
            chunk_col_name = collections["chunk_collection_name"]

            # 重新加载问题集合
            if utility.has_collection(question_col_name):
                collection_start = time.time()
                collections["question_collection"] = Collection(question_col_name)
                collection_time = time.time() - collection_start
                print(
                    f"[Info] {model_type.capitalize()} question collection init time: {collection_time:.3f}s"
                )

                load_start = time.time()
                collections["question_collection"].load()
                load_time = time.time() - load_start
                print(
                    f"[Info] {model_type.capitalize()} question collection load time: {load_time:.3f}s"
                )

            # 重新加载文档块集合
            if utility.has_collection(chunk_col_name):
                collection_start = time.time()
                collections["chunk_collection"] = Collection(chunk_col_name)
                collection_time = time.time() - collection_start
                print(
                    f"[Info] {model_type.capitalize()} chunk collection init time: {collection_time:.3f}s"
                )

                load_start = time.time()
                collections["chunk_collection"].load()
                load_time = time.time() - load_start
                print(
                    f"[Info] {model_type.capitalize()} chunk collection load time: {load_time:.3f}s"
                )

        total_time = time.time() - start_time
        print(f"[Info] All collections reloaded in {total_time:.3f}s")
        return True
    except Exception as e:
        total_time = time.time() - start_time
        print(f"[Error] Milvus reconnect failed: {str(e)} in {total_time:.3f}s")
        return False


def reload_collections(collection_name=None):
    if collection_name is None:
        # 重新加载所有集合
        collections_to_reload = []
        for _, collections in MODEL_COLLECTIONS.items():
            collections_to_reload.append(
                (
                    collections["question_collection"],
                    collections["question_collection_name"],
                )
            )
            collections_to_reload.append(
                (collections["chunk_collection"], collections["chunk_collection_name"])
            )
    else:
        # 查找特定集合
        collections_to_reload = []
        for _, collections in MODEL_COLLECTIONS.items():
            if collection_name == collections["question_collection_name"]:
                collections_to_reload = [
                    (collections["question_collection"], collection_name)
                ]
                break
            elif collection_name == collections["chunk_collection_name"]:
                collections_to_reload = [
                    (collections["chunk_collection"], collection_name)
                ]
                break

        if not collections_to_reload:
            print(f"[Error] Unknown collection name: {collection_name}")
            return False

    for coll, coll_name in collections_to_reload:
        try:
            start_time = time.time()
            coll.load()
            load_time = time.time() - start_time

            load_state = utility.load_state(collection_name=coll_name)
            if load_state == LoadState.Loaded:
                print(
                    f"[Info] Successfully reloaded collection: {coll_name}, time: {load_time:.3f}s"
                )
            else:
                print(
                    f"[Error] Collection {coll_name} load state incorrect: {load_state}, time: {load_time:.3f}s"
                )
                return False
        except Exception as e:
            load_time = time.time() - start_time
            print(
                f"[Error] Failed to reload collection {coll_name}: {str(e)}, time: {load_time:.3f}s"
            )
            return False

    return True


def create_question_collection(collection_name: str):
    fields = [
        FieldSchema(
            name="question_id", dtype=DataType.INT64, is_primary=True, auto_id=True
        ),
        FieldSchema(name="qna_pair_id", dtype=DataType.INT64),
        FieldSchema(name="organization_id", dtype=DataType.INT64),
        FieldSchema(
            name="language", dtype=DataType.VARCHAR, max_length=MAX_LANGUAGE_LENGTH
        ),
        FieldSchema(
            name="question", dtype=DataType.VARCHAR, max_length=MAX_QUESTION_LENGTH
        ),
        FieldSchema(
            name="answer", dtype=DataType.VARCHAR, max_length=MAX_ANSWER_LENGTH
        ),
        FieldSchema(name="is_active", dtype=DataType.BOOL, description="是否启用"),
        FieldSchema(
            name="question_embedding", dtype=DataType.FLOAT_VECTOR, dim=VECTOR_DIMENSION
        ),
        FieldSchema(
            name="answer_embedding", dtype=DataType.FLOAT_VECTOR, dim=VECTOR_DIMENSION
        ),
    ]
    schema = CollectionSchema(fields=fields, description="QA pairs collection v2")
    collection = Collection(name=collection_name, schema=schema)

    setup_collection(collection, has_dual_embeddings=True)

    print(f"Successfully created collection and added index: {collection_name}")
    return collection


def create_chunk_collection(collection_name: str):
    fields = [
        FieldSchema(
            name="chunk_id", dtype=DataType.INT64, is_primary=True, auto_id=True
        ),
        FieldSchema(name="organization_id", dtype=DataType.INT64),
        FieldSchema(name="document_id", dtype=DataType.INT64),
        FieldSchema(
            name="language", dtype=DataType.VARCHAR, max_length=MAX_LANGUAGE_LENGTH
        ),
        FieldSchema(
            name="chunk_content",
            dtype=DataType.VARCHAR,
            max_length=MAX_CHUNK_CONTENT_LENGTH,
        ),
        FieldSchema(name="is_active", dtype=DataType.BOOL, description="是否启用"),
        FieldSchema(
            name="embedding", dtype=DataType.FLOAT_VECTOR, dim=VECTOR_DIMENSION
        ),
    ]
    schema = CollectionSchema(fields=fields, description="Document chunks collection")
    collection = Collection(name=collection_name, schema=schema)

    setup_collection(collection)

    print(f"Successfully created collection and added index: {collection_name}")
    return collection


def setup_collection(collection: Collection, has_dual_embeddings=False):
    # 创建分区键的索引
    collection.create_index(field_name="organization_id", index_name="idx_org_id")

    # 创建向量索引
    if has_dual_embeddings:
        # 为问题和答案分别创建向量索引
        collection.create_index(
            field_name="question_embedding",
            index_params={
                "index_type": MILVUS_PARAMS["index_type"],
                "metric_type": MILVUS_PARAMS["metric_type"],
                "params": MILVUS_PARAMS["index_params"],
            },
        )
        collection.create_index(
            field_name="answer_embedding",
            index_params={
                "index_type": MILVUS_PARAMS["index_type"],
                "metric_type": MILVUS_PARAMS["metric_type"],
                "params": MILVUS_PARAMS["index_params"],
            },
        )
    else:
        collection.create_index(
            field_name="embedding",
            index_params={
                "index_type": MILVUS_PARAMS["index_type"],
                "metric_type": MILVUS_PARAMS["metric_type"],
                "params": MILVUS_PARAMS["index_params"],
            },
        )


def get_organization_partition(collection: Collection, organization_id: int):
    partition_name = f"org_{organization_id}"
    if not collection.has_partition(partition_name):
        collection.create_partition(partition_name)
    return collection.partition(partition_name)


@dataclass
class QAUpsertItem:
    """单条QA数据"""

    qa_pair_id: int
    language: str
    questions: List[str]  # 修改为问题列表
    answer: str
    question_embeddings: List[List[float]]  # 修改为问题向量列表
    answer_embedding: List[float]


@retry_on_milvus_error(collection_type="question")
def batch_upsert_qa_pairs(organization_id: int, items: List[QAUpsertItem]) -> None:
    """批量更新或插入QA对

    每个QA对可以包含多个问题，共享同一个答案。
    """
    # 根据组织ID获取对应的集合
    question_coll, _, model_type = get_collection_for_organization(organization_id)
    partition = get_organization_partition(question_coll, organization_id)

    if not items:
        return

    # 验证embedding维度
    question_vector_dim = question_coll.schema.fields[-2].params["dim"]
    answer_vector_dim = question_coll.schema.fields[-1].params["dim"]

    # 验证所有items的embedding维度
    for idx, item in enumerate(items):
        # 验证每个问题的embedding维度
        if len(item.questions) != len(item.question_embeddings):
            raise ValueError(
                f"Questions and embeddings count mismatch at index {idx}. "
                f"Questions: {len(item.questions)}, Embeddings: {len(item.question_embeddings)}"
            )

        for q_idx, q_embedding in enumerate(item.question_embeddings):
            if len(q_embedding) != question_vector_dim:
                raise ValueError(
                    f"Question embedding dimension mismatch at item {idx}, question {q_idx}. "
                    f"Expected {question_vector_dim}, got {len(q_embedding)}"
                )

        if len(item.answer_embedding) != answer_vector_dim:
            raise ValueError(
                f"Answer embedding dimension mismatch at index {idx}. "
                f"Expected {answer_vector_dim}, got {len(item.answer_embedding)}"
            )

    # 按照qa_pair_id和language组合收集需要删除的记录
    delete_conditions = [
        f'(qna_pair_id == {item.qa_pair_id} and language == "{item.language}")'
        for item in items
    ]

    # 使用OR连接所有条件
    expr = " or ".join(delete_conditions)
    delete_result = partition.delete(expr)
    total_deleted = delete_result.delete_count

    # 展开所有问题准备插入
    flattened_data = []
    for item in items:
        for question, question_embedding in zip(
            item.questions, item.question_embeddings
        ):
            flattened_data.append(
                {
                    "qa_pair_id": item.qa_pair_id,
                    "organization_id": organization_id,
                    "language": item.language,
                    "question": question,
                    "answer": item.answer,
                    "question_embedding": question_embedding,
                    "answer_embedding": item.answer_embedding,
                }
            )

    # 构建插入数据
    if flattened_data:
        data = [
            [d["qa_pair_id"] for d in flattened_data],
            [d["organization_id"] for d in flattened_data],
            [d["language"] for d in flattened_data],
            [d["question"] for d in flattened_data],
            [d["answer"] for d in flattened_data],
            [True] * len(flattened_data),  # 默认全部启用
            [d["question_embedding"] for d in flattened_data],
            [d["answer_embedding"] for d in flattened_data],
        ]

        insert_result = partition.insert(data)
        total_inserted = insert_result.insert_count

        print(
            f"[Info] 已删除 {total_deleted} 条旧记录，插入 {total_inserted} 条新记录 (使用 {model_type} 集合)"
        )


def verify_qa_pairs_deletion(organization_id: int, qna_pair_ids: list) -> Dict[str, List[int]]:
    """验证QA对是否被完全删除
    
    Args:
        organization_id: 组织ID
        qna_pair_ids: 要验证的QA对ID列表
        
    Returns:
        Dict[str, List[int]]: 按语言分组的未删除的QA对ID列表
    """
    question_coll, _, model_type = get_collection_for_organization(organization_id)
    partition_name = f"org_{organization_id}"
    
    if not question_coll.has_partition(partition_name):
        return {}
        
    partition = question_coll.partition(partition_name)
    
    # 构建查询表达式
    expr = f"qna_pair_id in {qna_pair_ids}"
    
    # 查询所有匹配的记录
    results = partition.query(
        expr=expr,
        output_fields=["qna_pair_id", "language"],
        consistency_level="Strong"
    )
    
    # 按语言分组未删除的QA对ID
    remaining_by_lang = {}
    for record in results:
        lang = record["language"]
        qna_pair_id = record["qna_pair_id"]
        if lang not in remaining_by_lang:
            remaining_by_lang[lang] = []
        remaining_by_lang[lang].append(qna_pair_id)
    
    return remaining_by_lang


@retry_on_milvus_error(collection_type="question")
def delete_questions_by_qna_pair_ids(
    organization_id: int, qna_pair_ids: list, language: str = None
):
    """删除指定组织下的QA对

    Args:
        organization_id: 组织ID
        qna_pair_ids: QA对ID列表
        language: 可选，指定语言。如果提供，则只删除该语言的QA对

    Returns:
        int: 删除的记录数量

    Raises:
        ValueError: 如果删除操作有任何错误
    """
    from app.utils.logger import log_exception

    # 根据组织ID获取对应的集合
    question_coll, _, model_type = get_collection_for_organization(organization_id)
    partition_name = f"org_{organization_id}"
    if not question_coll.has_partition(partition_name):
        print(f"组织 ID {organization_id} 没有问答分区")
        return 0

    partition = question_coll.partition(partition_name)

    # 批量大小，根据实际性能调整
    batch_size = 50000
    total_deleted = 0

    # 分批处理ID列表
    for i in range(0, len(qna_pair_ids), batch_size):
        batch_ids = qna_pair_ids[i : i + batch_size]

        # 构建删除表达式
        expr = f"qna_pair_id in {batch_ids}"
        if language:
            expr = f"{expr} and language == '{language}'"

        result = partition.delete(expr, consistency_level="Strong") # 使用强一致性确保删除立即可见

        log_text(partition_name, f"delete_qa_pairs: expr={expr}, result={result}")

        # 验证删除结果的错误计数
        if result.err_count > 0:
            error_msg = f"删除QA对出现错误: 组织ID={organization_id}, 批次={i//batch_size+1}, IDs={batch_ids}, 语言={language}, 错误计数={result.err_count}"
            log_exception("ai_server", ValueError(error_msg), context="删除QA对")
            raise ValueError(error_msg)

        total_deleted += result.delete_count

    print(f"[Info] 已从 {model_type} 集合中删除 {total_deleted} 条QA对记录")

    # 在删除操作完成后，添加验证步骤
    remaining = verify_qa_pairs_deletion(organization_id, qna_pair_ids)
    
    if remaining:
        log_text(
            partition_name,
            f"发现未完全删除的QA对: {remaining}"
        )
    else:
        log_text(
            partition_name,
            f"验证通过：所有指定的QA对已完全删除"
        )
    
    return total_deleted


@retry_on_milvus_error(collection_type="chunk")
def delete_chunks_by_document_ids(organization_id: int, document_ids: list):
    # 根据组织ID获取对应的集合
    _, chunk_coll, model_type = get_collection_for_organization(organization_id)
    partition_name = f"org_{organization_id}"
    if chunk_coll.has_partition(partition_name):
        partition = chunk_coll.partition(partition_name)
        expr = f"document_id in {document_ids}"
        result = partition.delete(expr)
        print(
            f"[Info] Deleted {result.delete_count} chunks from {model_type} collection"
        )
    else:
        print(
            f"[Warning] Organization ID {organization_id} doesn't have any chunk partition in {model_type} collection"
        )


@retry_on_milvus_error(collection_type="question")
async def search_question_collection(
    organization_id: int,
    search_type: str,
    language: str,
    embedding: List[float],
):
    """
    搜索问答集合

    Args:
        organization_id: 组织ID
        search_type: 搜索类型，"question" 表示基于问题搜索，"answer" 表示基于答案搜索
        language: 语言过滤（必需）
        embedding: 查询的embedding向量（必需）
    """
    # 根据组织ID获取对应的集合
    question_coll, _, model_type = get_collection_for_organization(organization_id)
    question_col_name, _ = get_collection_names_by_type(model_type)

    partition_name = f"org_{organization_id}"
    if not question_coll.has_partition(partition_name):
        print(
            f"[Warning] Org {organization_id} doesn't have any preset questions in {model_type} collection"
        )
        return []

    # 确保分区数据已加载
    load_state = utility.load_state(
        collection_name=question_col_name, partition_names=[partition_name]
    )
    if load_state != LoadState.Loaded:
        start_time = time.time()
        question_coll.load(partition_names=[partition_name])
        end_time = time.time()
        load_time = end_time - start_time
        print(
            f"[Warning] Reloaded question partition '{partition_name}' in {load_time:.3f}s"
        )

    partition = question_coll.partition(partition_name)

    search_params = {
        "metric_type": MILVUS_PARAMS["metric_type"],
        "params": MILVUS_PARAMS["search_params"],
    }

    # 添加语言过滤条件
    expr = f'language == "{language}"'

    # 根据搜索类型选择搜索字段
    anns_field = (
        "question_embedding" if search_type == "question" else "answer_embedding"
    )

    future = partition.search(
        data=[embedding],
        anns_field=anns_field,  # 使用对应的embedding字段
        param=search_params,
        limit=Env.QUESTION_TOP_K,
        expr=expr,
        output_fields=[
            "question_id",
            "qna_pair_id",
            "question",
            "answer",
            "language",
        ],
        consistency_level="Session",  # 使用 Session 一致性
        _async=True,
    )
    # 使用 asyncio.to_thread 来异步等待 SearchFuture 的结果
    # TODO: 遇到一次情况是，milvus的docker还在线，partition的状态是loaded，但是attu连接不上，并且future.result会卡住，无法返回
    results = await asyncio.to_thread(future.result)

    return [
        {
            "question_id": hit.entity.get("question_id"),
            "qna_pair_id": hit.entity.get("qna_pair_id"),
            "question": hit.entity.get("question"),
            "answer": hit.entity.get("answer"),
            "language": hit.entity.get("language"),
            "score": hit.score,
            "search_type": search_type,  # 添加搜索类型到返回结果
        }
        for hit in results[0]
    ]


@retry_on_milvus_error(collection_type="chunk")
async def search_chunk_collection(
    organization_id: int,
    language: str,
    embedding: List[float],
):
    """
    搜索文档块集合

    Args:
        organization_id: 组织ID
        language: 语言过滤（必需）
        embedding: 查询的embedding向量（必需）
    """
    # 根据组织ID获取对应的集合
    _, chunk_coll, model_type = get_collection_for_organization(organization_id)
    _, chunk_col_name = get_collection_names_by_type(model_type)

    partition_name = f"org_{organization_id}"
    if not chunk_coll.has_partition(partition_name):
        print(
            f"[Warning] Org {organization_id} doesn't have any preset documents in {model_type} collection"
        )
        return []

    # 确保分区数据已加载
    load_state = utility.load_state(
        collection_name=chunk_col_name, partition_names=[partition_name]
    )
    if load_state != LoadState.Loaded:
        start_time = time.time()
        chunk_coll.load(partition_names=[partition_name])
        end_time = time.time()
        load_time = end_time - start_time
        print(
            f"[Warning] Reloaded chunk partition '{partition_name}' in {load_time:.3f}s"
        )

    # 获取相关的分区
    partition = chunk_coll.partition(partition_name)

    search_params = {
        "metric_type": MILVUS_PARAMS["metric_type"],
        "params": MILVUS_PARAMS["search_params"],
    }

    # 添加语言过滤条件
    expr = f'language == "{language}"'

    future = partition.search(
        data=[embedding],
        anns_field="embedding",
        param=search_params,
        limit=Env.CHUNK_TOP_K,
        expr=expr,
        output_fields=[
            "chunk_id",
            "document_id",
            "chunk_content",
            "language",
        ],
        _async=True,
    )
    # 使用 asyncio.to_thread 来异步等待 SearchFuture 的结果
    results = await asyncio.to_thread(future.result)

    return [
        {
            "chunk_id": hit.entity.get("chunk_id"),
            "document_id": hit.entity.get("document_id"),
            "chunk_content": hit.entity.get("chunk_content"),
            "language": hit.entity.get("language"),  # 返回语言信息
            "score": hit.score,
        }
        for hit in results[0]
    ]


@dataclass
class ChunkUpsertItem:
    """单条文档块数据"""

    document_id: int
    language: str
    content: str
    embedding: List[float] = None


@retry_on_milvus_error(collection_type="chunk")
async def insert_batch_document_chunks(
    organization_id: int, items: List[ChunkUpsertItem]
) -> None:
    """批量插入文档块

    Args:
        organization_id: 组织ID
        items: 文档块列表,每个文档块包含document_id、language、content和可选的embedding
    """
    # 根据组织ID获取对应的集合
    _, chunk_coll, model_type = get_collection_for_organization(organization_id)

    # 验证数据非空
    if not items:
        return

    partition = get_organization_partition(chunk_coll, organization_id)

    # 获取所有需要embedding的内容
    contents = [item.content for item in items if item.embedding is None]
    if contents:
        # 批量获取embeddings
        embedding_start = time.time()
        embedding_config = get_embedding_config(organization_id)
        embeddings = await get_embedding(
            text=contents,
            embedding_config=embedding_config,
            deviceId=f"question_generator_{organization_id}",
            caller_module="milvus_service",
        )
        embedding_time = time.time() - embedding_start
        if Env.PRINT_EMBEDDING_TIME:
            print(
                f"[Debug] Batch chunks embedding time: {embedding_time:.3f}s for {len(contents)} chunks using {model_type} model"
            )

        # 将embeddings填充回items
        embedding_idx = 0
        for item in items:
            if item.embedding is None:
                item.embedding = embeddings[embedding_idx]
                embedding_idx += 1

    # 准备批量插入数据
    data = [
        [organization_id] * len(items),
        [item.document_id for item in items],
        [item.language for item in items],
        [item.content for item in items],
        [True] * len(items),  # 默认全部启用
        [item.embedding for item in items],
    ]

    # 执行批量插入
    start_time = time.time()
    result = partition.insert(data)
    print(
        f"[Info] Inserted {result.insert_count} chunks in {time.time() - start_time:.3f}s using {model_type} collection"
    )


@retry_on_milvus_error(collection_type="question")
def get_organization_qa_pairs(organization_id: int) -> Dict[str, List[int]]:
    """获取组织下所有现存的QA pair ID，按语言分类

    Args:
        organization_id: 组织ID

    Returns:
        Dict[str, List[int]]: 按语言分组的QA pair ID列表
    """
    # 根据组织ID获取对应的集合
    question_coll, _, model_type = get_collection_for_organization(organization_id)
    question_col_name, _ = get_collection_names_by_type(model_type)

    partition_name = f"org_{organization_id}"

    # 如果分区不存在，返回空字典
    if not question_coll.has_partition(partition_name):
        print(
            f"[Info] Organization {organization_id} doesn't have any QA pairs in {model_type} collection"
        )
        return {}

    # 确保分区数据已加载
    load_state = utility.load_state(
        collection_name=question_col_name, partition_names=[partition_name]
    )
    if load_state != LoadState.Loaded:
        start_time = time.time()
        question_coll.load(partition_names=[partition_name])
        load_time = time.time() - start_time
        print(
            f"[Info] Loaded question partition '{partition_name}' in {load_time:.3f}s from {model_type} collection"
        )

    partition = question_coll.partition(partition_name)

    # 使用基于ID的分页查询而不是offset
    batch_size = 10000  # 每批次查询的记录数
    qa_pairs_by_lang = {}
    current_id = -1  # 起始ID为-1，因为question_id是自增主键，从0开始

    while True:
        # 使用ID范围查询替代offset分页
        expr = f"question_id > {current_id}"
        results = partition.query(
            expr=expr,
            output_fields=["question_id", "qna_pair_id", "language"],
            limit=batch_size,
        )

        # 如果没有更多记录，退出循环
        if not results:
            break

        # 更新当前最大ID
        max_id = current_id

        # 处理当前批次的结果
        for record in results:
            lang = record["language"]
            qa_pair_id = record["qna_pair_id"]
            record_id = record["question_id"]

            # 更新最大ID
            if record_id > max_id:
                max_id = record_id

            if lang not in qa_pairs_by_lang:
                qa_pairs_by_lang[lang] = set()  # 使用set避免重复
            qa_pairs_by_lang[lang].add(qa_pair_id)

        # 如果返回的记录数小于batch_size，说明已经读取完所有记录
        if len(results) < batch_size:
            break

        # 更新当前ID为本批次的最大ID，下一次查询从这个ID之后开始
        current_id = max_id

    # 将set转换为排序后的list
    return {
        lang: sorted(list(qa_pair_ids))
        for lang, qa_pair_ids in qa_pairs_by_lang.items()
    }

import re
from typing import List, Optional, Any
from langchain.text_splitter import RecursiveCharacterTextSplitter
from docx import Document
from pptx import Presentation
import fitz  # PyMuPDF
from app.config import CHUNK_SIZE, CHUNK_OVERLAP, CHUNK_SIZE_PARAGRAPH
import os
from app.utils.token_utils import TokenCalculator
from openpyxl import load_workbook


def read_pdf(file_path: str) -> str:
    doc = fitz.open(file_path)
    text = ""
    for page_num in range(doc.page_count):
        page = doc.load_page(page_num)
        text += page.get_text("text")
    return text


def read_txt(file_path: str) -> str:
    encodings = ["utf-8", "gbk", "big5", "utf-16"]
    for encoding in encodings:
        try:
            with open(file_path, "r", encoding=encoding) as file:
                return file.read()
        except UnicodeDecodeError:
            continue
    raise ValueError("Failed to read the text file with provided encodings")


def read_docx(file_path: str) -> str:
    doc = Document(file_path)
    text = ""
    for paragraph in doc.paragraphs:
        text += paragraph.text + "\n"
    return text


def read_pptx(file_path: str) -> str:
    prs = Presentation(file_path)
    text = ""
    for slide in prs.slides:
        for shape in slide.shapes:
            if hasattr(shape, "text"):
                text += shape.text + "\n"
    return text


def read_xlsx(file_path: str) -> str:
    wb = load_workbook(file_path, data_only=True)
    text = ""
    
    for sheet in wb.worksheets:
        # 获取标题行
        headers = []
        for cell in next(sheet.iter_rows(values_only=True)):
            if cell is not None:
                headers.append(str(cell))
        
        text += f"以下是工作表{sheet.title}的内容：\n\n"
        
        for row_idx, row in enumerate(sheet.iter_rows(min_row=2, values_only=True)):
            row_data = []
            for header, cell in zip(headers, row):
                if cell is not None:
                    # 将数字转换为更自然的文本表示
                    if isinstance(cell, (int, float)):
                        row_data.append(f"{header}是{cell}")
                    else:
                        row_data.append(f"{header}是{cell}")
            
            if row_data:
                text += "第{}行：".format(row_idx + 1) + "，".join(row_data) + "。\n"
        
        text += "\n"
    
    return text

class ChineseRecursiveTextSplitter(RecursiveCharacterTextSplitter):
    def __init__(
        self,
        separators: Optional[List[str]] = None,
        keep_separator: bool = True,
        is_separator_regex: bool = True,
        **kwargs: Any,
    ) -> None:
        def calculate_length(text: str) -> int:
            return TokenCalculator.calculate_tokens([text])

        kwargs["length_function"] = calculate_length
        super().__init__(keep_separator=keep_separator, **kwargs)
        self._separators = separators or [
            "#{3}[^#]+",  # 修改为匹配单个#号的标题
            "\n\n",
            "\n",
            "。|！|？",
            "\.\s|\!\s|\?\s",
            "；|;\s",
            "，|,\s",
        ]
        self._is_separator_regex = is_separator_regex

    def _split_text(self, text: str, separators: List[str]) -> List[str]:
        # 检查是否有 ### 格式的标题
        if re.search(r"#{3}[^#]+", text):
            # 按标题分割
            chunks = re.split(r"(#{3}[^#]+)", text)
            final_chunks = []
            current_chunk = ""

            for i, chunk in enumerate(chunks):
                if chunk.strip():
                    if re.match(r"#{3}[^#]+", chunk):
                        if current_chunk:
                            # 检查当前chunk是否超过最大长度
                            if (
                                self._length_function(current_chunk)
                                > CHUNK_SIZE_PARAGRAPH
                            ):
                                # 如果超过，使用父类的分割方法进行进一步分割
                                sub_chunks = super()._split_text(
                                    current_chunk, separators[1:]
                                )
                                final_chunks.extend(sub_chunks)
                            else:
                                final_chunks.append(current_chunk.strip())
                        current_chunk = chunk
                    else:
                        current_chunk += chunk

            if current_chunk:
                # 对最后一个chunk也进行长度检查
                if self._length_function(current_chunk) > CHUNK_SIZE_PARAGRAPH:
                    sub_chunks = super()._split_text(current_chunk, separators[1:])
                    final_chunks.extend(sub_chunks)
                else:
                    final_chunks.append(current_chunk.strip())

            return [
                re.sub(r" +", " ", chunk) for chunk in final_chunks if chunk.strip()
            ]

        # 如果没有标题，使用原来的分割逻辑
        return super()._split_text(text, separators)


FILE_HANDLERS = {
    ".pdf": read_pdf,
    ".txt": read_txt,
    ".docx": read_docx,
    ".pptx": read_pptx,
    ".xlsx": read_xlsx,
}


def split_document(file_path):
    text_splitter = ChineseRecursiveTextSplitter(
        keep_separator=True,
        is_separator_regex=True,
        chunk_size=500,
        chunk_overlap=50,
    )

    file_extension = os.path.splitext(file_path)[1].lower()
    if file_extension not in FILE_HANDLERS:
        raise ValueError(f"Unsupported file type: {file_extension}")

    text = FILE_HANDLERS[file_extension](file_path)
    print(f"Processing {file_path}")
    chunks = text_splitter.split_text(text)
    return chunks

# 添加测试代码
if __name__ == "__main__":
    test_file = r"C:\Users\<USER>\Documents\GitHub\ai-service-project\local_test\导流业务问答.xlsx"
    chunks = split_document(test_file)
    
    print(f"\n总共分割成 {len(chunks)} 个片段")
    for i, chunk in enumerate(chunks, 1):
        print(f"\n--- 第 {i} 个片段 ---")
        print(f"Token 数量: {TokenCalculator.calculate_tokens([chunk])}")
        print(chunk[:100] + "..." if len(chunk) > 100 else chunk)

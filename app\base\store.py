import asyncio
from typing import Dict, Any, Optional, Tuple, Protocol
from jsonpointer import resolve_pointer, JsonPointerException, set_pointer
from cachetools import LRUCache
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class StoreBackend(ABC):
    """存储后端抽象基类"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """从后端存储获取数据"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any) -> None:
        """将数据保存到后端存储"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> None:
        """从后端存储删除数据"""
        pass

class BaseStore:
    """基础存储类，处理具体的存储逻辑"""
    
    def __init__(self, max_size: int = 10000, backend: Optional[StoreBackend] = None):
        self._cache = LRUCache(maxsize=max_size)
        self._lock = asyncio.Lock()
        self._backend = backend
    
    async def get(self, item_id: str, path: str = "/", default: Any = None) -> Any:
        """获取指定项目和路径的值
        
        Args:
            item_id: 项目ID
            path: JSON指针路径（以/开头）
            default: 未找到时的默认值
            
        Returns:
            获取的值或默认值
        """
        async with self._lock:
            try:
                # 先从缓存获取
                data = self._cache.get(item_id)
                
                # 如果缓存未命中且有后端存储，则从后端获取
                if data is None and self._backend is not None:
                    data = await self._backend.get(item_id)
                    if data is not None:
                        self._cache[item_id] = data
                
                if data is None:
                    return default
                
                # 空路径或根路径，直接返回整个数据
                if not path or path == "/":
                    return data
                
                # 使用给定的路径，不再额外处理
                return resolve_pointer(data, path, default)
            except JsonPointerException as e:
                logger.warning(f"Failed to resolve path {path}: {e}")
                return default

    async def set(self, item_id: str, value: Any, path: str = "/") -> None:
        """设置指定项目和路径的值
        
        Args:
            item_id: 项目ID
            value: 要设置的值
            path: JSON指针路径（以/开头）
        """
        async with self._lock:
            try:
                # 空路径或根路径，直接设置整个数据
                if not path or path == "/":
                    self._cache[item_id] = value
                    if self._backend is not None:
                        await self._backend.set(item_id, value)
                    return
                
                data = self._cache.get(item_id, {})
                
                # 手动创建中间节点
                current = data
                parts = path.strip('/').split('/')
                # 遍历除最后一个部分外的所有部分
                for i, part in enumerate(parts[:-1]):
                    if part not in current or not isinstance(current[part], dict):
                        current[part] = {}
                    current = current[part]
                
                # 最后使用标准set_pointer设置值
                set_pointer(data, path, value)
                
                self._cache[item_id] = data
                
                if self._backend is not None:
                    await self._backend.set(item_id, data)
            except Exception as e:
                logger.error(f"Failed to set path {path}: {e}")
                raise

    async def delete(self, item_id: str, path: str = "/") -> None:
        """删除指定项目和路径的值
        
        Args:
            item_id: 项目ID
            path: JSON指针路径（以/开头）
        """
        async with self._lock:
            try:
                # 空路径或根路径，删除整个数据
                if not path or path == "/":
                    self._cache.pop(item_id, None)
                    if self._backend is not None:
                        await self._backend.delete(item_id)
                    return
                
                data = self._cache.get(item_id)
                if data is None and self._backend is not None:
                    data = await self._backend.get(item_id)
                
                if data is None:
                    return
                
                # 使用给定的路径，不再额外处理
                set_pointer(data, path, None)
                self._cache[item_id] = data
                
                if self._backend is not None:
                    await self._backend.set(item_id, data)
            except JsonPointerException as e:
                logger.error(f"Failed to delete path {path}: {e}")
                raise

class Store:
    """统一的存储入口，根据路径前缀自动选择存储实例"""
    
    _stores: Dict[str, BaseStore] = {}
    _lock = asyncio.Lock()
    
    @classmethod
    def _parse_full_path(cls, full_path: str) -> Tuple[str, str, str]:
        """解析完整路径为命名空间、ID和路径
        
        Args:
            full_path: 完整路径，如 "/sessions/123/user/name"
            
        Returns:
            Tuple[str, str, str]: 返回 (namespace, item_id, relative_path)
            例如：("sessions", "123", "/user/name")
            
        Raises:
            ValueError: 当路径格式无效时，例如少于两部分 (namespace/id)
        """
        # 去除首尾斜杠并分割
        parts = full_path.strip("/").split("/", 2)
        
        # 确保至少有命名空间和ID两部分
        if len(parts) < 2:
            raise ValueError(f"Invalid path format: {full_path}, path must have at least namespace/id parts")
        
        namespace = parts[0]
        item_id = parts[1]
        # 确保返回的路径带有前导斜杠
        relative_path = "/" + (parts[2] if len(parts) > 2 else "")
        
        return namespace, item_id, relative_path
    
    @classmethod
    async def _get_store(cls, namespace: str) -> BaseStore:
        """获取指定命名空间的存储实例"""
        async with cls._lock:
            if namespace not in cls._stores:
                raise ValueError(f"No store configured for namespace: {namespace}")
            return cls._stores[namespace]
    
    @classmethod
    async def configure(cls, namespace: str, max_size: int = 10000, backend: Optional[StoreBackend] = None) -> None:
        """配置存储实例"""
        async with cls._lock:
            if namespace in cls._stores:
                logger.warning(f"Overwriting existing store for namespace: {namespace}")
            cls._stores[namespace] = BaseStore(max_size=max_size, backend=backend)
    
    @classmethod
    async def get(cls, full_path: str, default: Any = None) -> Any:
        """获取存储的值
        
        Args:
            full_path: 完整路径，如 "/sessions/123/user/name"
            default: 未找到时的默认值
            
        Returns:
            获取的值或默认值
        """
        try:
            namespace, item_id, relative_path = cls._parse_full_path(full_path)
            store = await cls._get_store(namespace)
            return await store.get(item_id, relative_path, default)
        except ValueError as e:
            logger.warning(f"Invalid path format or namespace not configured: {e}")
            return default
    
    @classmethod
    async def set(cls, full_path: str, value: Any) -> None:
        """设置存储的值
        
        Args:
            full_path: 完整路径，如 "/sessions/123/user/name"
            value: 要设置的值
            
        Raises:
            ValueError: 当路径格式无效或命名空间未配置时
        """
        namespace, item_id, relative_path = cls._parse_full_path(full_path)
        store = await cls._get_store(namespace)
        await store.set(item_id, value, relative_path)
    
    @classmethod
    async def delete(cls, full_path: str) -> None:
        """删除存储的值
        
        Args:
            full_path: 完整路径，如 "/sessions/123/user/name"
            
        Raises:
            ValueError: 当路径格式无效或命名空间未配置时
        """
        namespace, item_id, relative_path = cls._parse_full_path(full_path)
        store = await cls._get_store(namespace)
        await store.delete(item_id, relative_path)


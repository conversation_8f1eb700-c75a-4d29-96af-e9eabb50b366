from app.config import Env
from fastapi import APIRouter, HTTPException
from app.utils.net_utils import wrap_response
from app.services import base_prompt
from app.services.base_prompt import BasePromptData
from pydantic import BaseModel, validator
from typing import List, Optional, Union, Dict, Any
from app.services.config_service import config_service

router = APIRouter()

#获取配置详情
@router.post("/env/detail")
async def get_env_detail():
    detail = Env.get_detail()
    return wrap_response(detail)

#重新加载配置，在更新.env文件后，调用这个接口更新动态配置。静态的还是需要重启生效
@router.post("/env/reload")
async def reload_env():
    Env.reload()
    return wrap_response("配置重新加载成功")

class PromptRequest(BaseModel):
    setId: int
    data: List[dict]  # 每个dict包含promptId, language, template, tools

@router.post("/pushBasePrompt")
async def push_base_prompt(request: PromptRequest):
    # 预定义允许的提示词ID类别
    allowed_prompt_ids = ["intentDetect", "knowledge", "knowledgeQA", "chat"]
    
    # 检查所有提示词ID是否在允许范围内
    for item in request.data:
        if item["promptId"] not in allowed_prompt_ids:
            raise HTTPException(
                status_code=400, 
                detail=f"无效的提示词ID: {item['promptId']}。允许的ID为: {', '.join(allowed_prompt_ids)}"
            )
    
    # 使用BasePromptData的from_list方法
    prompt_data = BasePromptData.from_list(request.data)
    
    # 调用服务函数更新数据库和缓存
    base_prompt.push_base_prompt(request.setId, prompt_data)
    
    return wrap_response("基础提示词配置更新成功")

class BusinessConfigRequest(BaseModel):
    organizationId: int
    data: Optional[Union[Dict[str, Any], str]] = None  # 允许字典、字符串或None

    @validator('data')
    def validate_data(cls, v):
        # 将空字符串转换为None
        if isinstance(v, str) and v == "":
            return None
        return v

@router.post("/pushBusinessConfig")
async def push_business_config(request: BusinessConfigRequest):
    """
    更新、创建或删除业务配置
    
    此接口接收组织ID和配置数据，进行如下操作：
    - 如果data为None或空字符串，则删除该组织的业务配置
    - 如果组织配置不存在且data有效，则创建新配置
    - 如果配置已存在且data有效，则更新现有配置
    
    配置更新后会清除缓存，确保后续请求使用最新配置。
    """    
    success = config_service.update_config(request.organizationId, request.data)
    
    if not success:
        raise HTTPException(status_code=500, detail="业务配置操作失败")
    
    if request.data is None or request.data == "":
        return wrap_response("业务配置删除成功")
    else:
        return wrap_response("业务配置更新成功")
from fastapi import FastAP<PERSON>, Request
from app.routers import (
    env,
    push_config,
    doc2vec,
    chat,
    sql_update,
    translate,
    history,
    prompt,
)
from app.services.milvus_service import connect_milvus, disconnect_milvus
from app.services.mysql_service import initialize_database, close_database_connection
from app.services.config_service import init_default_configs
from contextlib import asynccontextmanager
from app.config import (
    MYSQL_HOST,
    MYSQL_PORT,
    MYSQL_USER,
    MYSQL_PASSWORD,
    MYSQL_DATABASE,
)
from app.routers import TimeoutMiddleware, CustomBabelMiddleware

from fastapi_babel import Babel, BabelConfigs


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 加载翻译文本
    connect_milvus()
    print("[Info] Connected to Mi<PERSON><PERSON><PERSON> successfully")

    # Initialize MySQL service
    db = initialize_database(
        MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE, MYSQL_PORT
    )
    if not db:
        print("[Error] Failed to initialize MySQL database")
        raise Exception("Failed to initialize MySQL database")
    else:
        print("[Info] Connected to MySQL database successfully")

    # 初始化默认业务配置
    init_result = init_default_configs()
    if init_result:
        print("[Info] Default business configurations initialized successfully")
    else:
        print("[Warning] Failed to initialize default business configurations")

    yield

    # Disconnect from Milvus and MySQL when the application shuts down
    disconnect_milvus()
    close_database_connection()


app = FastAPI(lifespan=lifespan)
babel_configs = BabelConfigs(
    ROOT_DIR=__file__,
    BABEL_DEFAULT_LOCALE="zh_CN",
    BABEL_TRANSLATION_DIRECTORY="translations",
)

# 创建中间件实例并保存为全局变量
babel_middleware = CustomBabelMiddleware(app, babel_configs)
app.add_middleware(TimeoutMiddleware)
app.add_middleware(CustomBabelMiddleware, babel_configs=babel_configs)


# Include routing modules
app.include_router(push_config.router)
app.include_router(doc2vec.router)
app.include_router(chat.router)
app.include_router(env.router)
app.include_router(translate.router)
app.include_router(history.router)
app.include_router(prompt.router)
app.include_router(sql_update.router)

import pymysql
from peewee import *
from playhouse.pool import PooledMySQLDatabase
from jsonfield.jsonfield import <PERSON><PERSON><PERSON><PERSON>
from app.utils.logger import log_exception, log_retry
from app.services import DEVICE_AI_SERVER
import time
import functools
import datetime
import json
from functools import wraps
from typing import List, Optional, Union, Dict, Any
from peewee import DoesNotExist


class CustomPooledMySQLDatabase(PooledMySQLDatabase):
    def _connect(self):
        conn = super()._connect()
        setattr(conn, "json_ensure_ascii", False)
        return conn

    def safe_rollback(self):
        try:
            self.rollback()
        except Exception:
            pass

    def safe_close(self):
        if not self.is_closed():
            try:
                self.close()
            except OperationalError:
                self.safe_rollback()
                try:
                    self.close()
                except Exception:
                    pass


db = CustomPooledMySQLDatabase(None, max_connections=32, stale_timeout=300, timeout=30)


# 定义模型
class BaseModel(Model):
    class Meta:
        database = db


class UIConfig(BaseModel):
    id = BigAutoField(primary_key=True)
    organizationId = BigIntegerField(null=False)
    key = CharField(max_length=256, null=False)
    data = JSONField()

    class Meta:
        indexes = (
            # 创建 organizationId 和 key 的联合唯一索引
            (('organizationId', 'key'), True),
        )



class IntentionConfig(BaseModel):
    id = BigAutoField(primary_key=True)
    organizationId = BigIntegerField(null=False)
    language = CharField(max_length=36, null=True)
    config = JSONField()


class PromptConfig(BaseModel):
    id = BigAutoField(primary_key=True)
    organizationId = BigIntegerField(null=False)
    language = CharField(max_length=36, null=True)
    promptId = CharField(max_length=36, null=False)
    template = TextField()
    tools = JSONField()


class BusinessData(BaseModel):
    id = BigAutoField(primary_key=True)
    organizationId = BigIntegerField(null=False)
    language = CharField(max_length=36, null=True)
    key = CharField(max_length=36, null=False)
    data = JSONField()


class BasePrompt(BaseModel):
    id = BigAutoField(primary_key=True)
    organizationId = BigIntegerField(null=False)
    language = CharField(max_length=36, null=True)
    config = JSONField()


def update_field_lengths(max_length=512):
    """
    更新数据库表中的字段长度到指定值
    
    Args:
        max_length: 要设置的最大长度，默认为512
    """
    print(f"[信息] 开始更新数据库字段长度至 {max_length}...")
    
    # 要更新的表和字段映射
    tables_fields = {
        IntentionConfig._meta.table_name: ['language'],
        PromptConfig._meta.table_name: ['language', 'promptId'],
        BusinessData._meta.table_name: ['key', 'language'],
        BasePrompt._meta.table_name: ['language', 'promptId'],
    }
    
    with db.atomic():
        try:
            for table_name, fields in tables_fields.items():
                for field in fields:
                    # 检查字段是否存在
                    cursor = db.execute_sql(f"SHOW COLUMNS FROM {table_name} LIKE '{field}'")
                    column_info = cursor.fetchone()
                    
                    if column_info:
                        # 提取当前的类型定义，例如: "varchar(36)"
                        current_type = column_info[1].decode('utf-8') if isinstance(column_info[1], bytes) else column_info[1]
                        
                        # 如果是VARCHAR类型并且长度不等于目标长度
                        if "varchar" in current_type.lower():
                            # 提取当前长度
                            current_length = int(current_type.lower().split('varchar(')[1].split(')')[0])
                            
                            if current_length != max_length:
                                print(f"[信息] 更新表 {table_name} 的 {field} 字段，从 VARCHAR({current_length}) 到 VARCHAR({max_length})")
                                db.execute_sql(
                                    f"ALTER TABLE {table_name} MODIFY COLUMN `{field}` VARCHAR({max_length})"
                                )
            
            print(f"[信息] 字段长度更新完成")
        except Exception as e:
            print(f"[错误] 更新字段长度时发生错误: {str(e)}")
            db.safe_rollback()
            raise



class BusinessConfig(BaseModel):
    id = BigAutoField(primary_key=True)
    organizationId = BigIntegerField(null=False)
    configValue = JSONField()
    
    class Meta:
        indexes = (
            # 创建组织ID唯一索引
            (('organizationId',), True),
        )


def initialize_database(
    host, user, password, database, port=3306, max_retries=3, retry_delay=1
):
    for attempt in range(max_retries):
        try:
            if db.is_connection_usable():
                current_db = db.database
                current_port = db.connect_params.get("port", 3306)
                if current_db == database and current_port == port:
                    print(
                        "[信息] 数据库已连接且数据库名和端口匹配，跳过初始化。"
                    )
                    return db
                else:
                    print(
                        "[信息] 数据库已连接但数据库名或端口不匹配，重新初始化。"
                    )
                    db.close()

            # 创建数据库（如果不存在）
            conn = pymysql.connect(host=host, user=user, password=password, port=port)
            cursor = conn.cursor()
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {database}")
            conn.close()

            # 连接到数据库
            db.init(database, host=host, user=user, password=password, port=port)
            db.connect()
            
            # 首先创建所有表，避免表不存在的错误
            print("[信息] 开始创建/验证所有数据表...")
            tables_to_create = [IntentionConfig, PromptConfig, BusinessData, BasePrompt, BusinessConfig, UIConfig]
            
            # 先检查每个表是否存在
            existing_tables = []
            for model in tables_to_create:
                table_name = model._meta.table_name
                try:
                    cursor = db.execute_sql(f"SHOW TABLES LIKE '{table_name}'")
                    if cursor.fetchone():
                        print(f"[信息] 表 '{table_name}' 已存在")
                        existing_tables.append(table_name)
                    else:
                        print(f"[信息] 表 '{table_name}' 不存在，将创建")
                except Exception as e:
                    print(f"[警告] 检查表 '{table_name}' 时出错: {str(e)}")

            # 创建不存在的表
            try:
                db.create_tables(tables_to_create, safe=True)
                print(f"[信息] 表创建/验证完成")
            except Exception as e:
                print(f"[错误] 创建表时发生错误: {str(e)}")
                # 尝试单独创建每个表
                for model in tables_to_create:
                    table_name = model._meta.table_name
                    if table_name not in existing_tables:
                        try:
                            db.create_tables([model], safe=True)
                            print(f"[信息] 成功创建表 '{table_name}'")
                        except Exception as table_e:
                            print(f"[错误] 创建表 '{table_name}' 失败: {str(table_e)}")

            # 检查并添加字段（只对已存在的表进行操作）
            with db.atomic():
                try:
                    # 检查 BasePrompt 表的 setId 字段
                    cursor = db.execute_sql(
                        f"SHOW COLUMNS FROM {BasePrompt._meta.table_name} LIKE 'setId'"
                    )
                    if not cursor.fetchone():
                        print(
                            f"[Info] Adding setId column to {BasePrompt._meta.table_name}"
                        )
                        # 添加 setId 字段，默认值设为 0
                        db.execute_sql(
                            f"ALTER TABLE {BasePrompt._meta.table_name} "
                            f"ADD COLUMN setId BIGINT NOT NULL DEFAULT 0"
                        )

                    # 语言字段处理
                    for table in [IntentionConfig, PromptConfig, BusinessData]:
                        table_name = table._meta.table_name
                        if table_name in existing_tables:
                            cursor = db.execute_sql(
                                f"SHOW COLUMNS FROM {table_name} LIKE 'language'"
                            )
                            if not cursor.fetchone():
                                print(f"[信息] 向 {table_name} 添加 language 列")
                                db.execute_sql(
                                    f"ALTER TABLE {table_name} ADD COLUMN language VARCHAR(36) NULL"
                                )
                                db.execute_sql(
                                    f"UPDATE {table_name} SET language = 'zh-CN' WHERE language IS NULL"
                                )

                    
                    # BusinessData 表处理
                    if BusinessData._meta.table_name in existing_tables:
                        cursor = db.execute_sql(
                            f"SHOW COLUMNS FROM {BusinessData._meta.table_name} LIKE 'language'"
                        )
                        if not cursor.fetchone():
                            print(f"[信息] 向 {BusinessData._meta.table_name} 添加 language 列")
                            db.execute_sql(
                                f"ALTER TABLE {BusinessData._meta.table_name} "
                                f"ADD COLUMN language VARCHAR(36) NULL"
                            )
                            db.execute_sql(
                                f"UPDATE {BusinessData._meta.table_name} SET language = 'zh-CN' WHERE language IS NULL"
                            )

                except Exception as e:
                    print(f"[警告] 修改表结构时出错: {str(e)}")

            # 更新字段长度到512
            update_field_lengths(512)

            print("[信息] 数据库初始化成功")
            return db
        except (InterfaceError, OperationalError) as e:
            if attempt < max_retries - 1:
                print(
                    f"[错误] 连接失败，正在重试 (尝试 {attempt + 1}/{max_retries}): {str(e)}"
                )
                time.sleep(retry_delay)
            else:
                log_exception(DEVICE_AI_SERVER, e, "数据库初始化失败")
                return None
        except Exception as e:
            log_exception(DEVICE_AI_SERVER, e, "数据库初始化失败")
            return None


def close_database_connection():
    if not db.is_closed():
        db.close()
        print("[Info] Database connection closed.")


def retry_on_db_error(is_write=False, max_retries=3, retry_delay=1):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    # 检查连接状态并尝试重连
                    if not db.is_connection_usable():
                        print(
                            f"[Info] Database connection not usable, attempting to reconnect..."
                        )
                        db.connect(reuse_if_open=True)

                    return func(*args, **kwargs)
                except (InterfaceError, OperationalError) as e:
                    if attempt == max_retries - 1:
                        # 最后一次重试失败，记录错误
                        operation_type = "write" if is_write else "read"
                        log_exception(
                            DEVICE_AI_SERVER,
                            e,
                            f"Database {operation_type} operation failed: {func.__name__}",
                        )
                        raise
                    # 记录重试信息
                    log_retry(
                        DEVICE_AI_SERVER,
                        attempt + 1,
                        e,
                        f"Database operation {func.__name__}",
                    )

                    if is_write:
                        db.safe_rollback()

                    # 强制关闭并重新连接
                    db.safe_close()
                    time.sleep(retry_delay)
                    try:
                        db.connect(reuse_if_open=True)
                    except Exception as conn_err:
                        print(f"[Warning] Reconnection attempt failed: {str(conn_err)}")

                except DoesNotExist:
                    if not is_write:
                        return None
                    raise
                except Exception as e:
                    # 记录其他未预期的错误
                    log_exception(
                        DEVICE_AI_SERVER, e, f"Unexpected error in {func.__name__}"
                    )
                    if is_write:
                        db.safe_rollback()
                    raise
            return None

        return wrapper

    return decorator


@db.connection_context()
@retry_on_db_error(is_write=True)
def upsert_intention_config(organization_id, config):
    # 意图配置固定使用zh-CN
    intention_config, created = IntentionConfig.get_or_create(
        organizationId=organization_id,
        language="zh-CN",  # 固定使用zh-CN
        defaults={"config": config},
    )
    if not created:
        intention_config.config = config
        intention_config.save()
    return intention_config


@db.connection_context()
@retry_on_db_error(is_write=True)
def upsert_llm_config(organization_id, config):
    llm_config, created = BusinessConfig.get_or_create(
        organizationId=organization_id,
        defaults={
            "configValue": {
                "llm_config": dict(config)
            }
        },
    )
    if not created: # 如果存在，则更新
        llm_config.configValue["llm_config"] = dict(config)
        llm_config.save()
    return llm_config


@db.connection_context()
@retry_on_db_error(is_write=True)
def upsert_embedding_config(organization_id, config):
    embedding_config, created = BusinessConfig.get_or_create(
        organizationId=organization_id,
        defaults={
            "configValue": {
                "embedding_config": dict(config)
            }
        },
    )
    if not created: # 如果存在，则更新
        embedding_config.configValue["embedding_config"] = dict(config)
        embedding_config.save()
    return embedding_config


@db.connection_context()
@retry_on_db_error()
def get_intention_config(organization_id, language="zh-CN"):
    return IntentionConfig.get(
        (IntentionConfig.organizationId == organization_id)
        & (IntentionConfig.language == language)
    ).config


@db.connection_context()
@retry_on_db_error()
def get_base_prompt_setId(organization_id):
    return BusinessConfig.get(
        (BusinessConfig.organizationId == organization_id)
    ).configValue.get("base_prompt_set_id", 0)


@db.connection_context()
@retry_on_db_error()
def get_llm_config(organization_id):
    # 如果organization_id为None，直接返回空字典
    if organization_id is None:
        return {}
    try:
        return BusinessConfig.get(
            (BusinessConfig.organizationId == organization_id)
        ).configValue.get("llm_config", {})
    except DoesNotExist:
        # 找不到对应记录时返回空字典
        return {}


@db.connection_context()
@retry_on_db_error()
def get_embedding_config(organization_id):
    # 如果organization_id为None，直接返回空字典
    if organization_id is None:
        return {}
    try:
        return BusinessConfig.get(
            (BusinessConfig.organizationId == organization_id)
        ).configValue.get("embedding_config", {})
    except DoesNotExist:
        # 找不到对应记录时返回空字典
        return {}


class BasePrompt(BaseModel):
    id = BigAutoField(primary_key=True)
    setId = BigIntegerField(null=False)
    language = CharField(max_length=36, null=False)
    promptId = CharField(max_length=36, null=False)
    template = TextField()
    tools = JSONField()


    def to_dict(self) -> dict:
        return {
            "setId": self.setId,
            "language": self.language,
            "promptId": self.promptId,
            "template": self.template,
            "tools": self.tools,
        }


@db.connection_context()
@retry_on_db_error()
def get_base_prompt_list(set_id: int) -> list[dict]:
    prompts = BasePrompt.select().where(BasePrompt.setId == set_id)
    return [p.to_dict() for p in prompts]


@db.connection_context()
@retry_on_db_error(is_write=True)
def batch_upsert_base_prompt(set_id, data):
    for item in data:
        upsert_base_prompt(
            set_id,
            item.get("promptId"),
            item.get("language"),
            item.get("template"),
            item.get("tools"),
        )


@db.connection_context()
@retry_on_db_error(is_write=True)
def upsert_base_prompt(set_id, prompt_id, language, template, tools):
    if not prompt_id:
        return None

    if not template and not tools:
        # 删除操作
        BasePrompt.delete().where(
            (BasePrompt.setId == set_id)
            & (BasePrompt.language == language)
            & (BasePrompt.promptId == prompt_id)
        ).execute()
        return None

    prompt_config, created = BasePrompt.get_or_create(
        setId=set_id,
        language=language,
        promptId=prompt_id,
        defaults={
            "template": template if template is not None else "",
            "tools": tools if tools is not None else [],
        },
    )

    if not created:
        if template is not None:
            prompt_config.template = template
        if tools is not None:
            prompt_config.tools = tools
        prompt_config.save()

    return prompt_config


def batch_upsert_prompt_config(organization_id, data):
    for item in data:
        upsert_prompt_config(
            organization_id,
            item.get("promptId"),
            item.get("template"),
            item.get("tools"),
        )


def case_sens(field):
    """统一处理字段的大小写敏感比较"""
    return field.collate("utf8mb4_bin")


@db.connection_context()
@retry_on_db_error(is_write=True)
def upsert_prompt_config(organization_id, prompt_id, template, tools):
    if not prompt_id:
        return None

    if not template and not tools:
        # 删除操作
        PromptConfig.delete().where(
            (PromptConfig.organizationId == organization_id)
            & (case_sens(PromptConfig.promptId) == prompt_id)
            & (PromptConfig.language == "zh-CN")
        ).execute()
        return None

    with db.atomic() as transaction:
        try:
            # 尝试查找现有记录
            prompt_config = PromptConfig.get(
                (PromptConfig.organizationId == organization_id)
                & (case_sens(PromptConfig.promptId) == prompt_id)
                & (PromptConfig.language == "zh-CN")
            )
            # 更新现有记录
            if template is not None:
                prompt_config.template = template
            if tools is not None:
                prompt_config.tools = tools
            prompt_config.save()
        except DoesNotExist:
            # 创建新记录
            prompt_config = PromptConfig.create(
                organizationId=organization_id,
                promptId=prompt_id,
                language="zh-CN",
                template=template if template is not None else "",
                tools=tools if tools is not None else [],
            )

    return prompt_config


@db.connection_context()
@retry_on_db_error()
def get_all_prompt_config(organization_id, language="zh-CN") -> list[PromptConfig]:
    return list(
        PromptConfig.select().where(
            (PromptConfig.organizationId == organization_id)
            & (PromptConfig.language == language)
        )
    )


@db.connection_context()
@retry_on_db_error()
def get_prompt_config(organization_id, prompt_id) -> PromptConfig:
    return PromptConfig.get(
        (PromptConfig.organizationId == organization_id)
        & (PromptConfig.promptId == prompt_id)
    )


# BusinessData 操作函数
def batch_upsert_biz_data(organization_id, data_list, language="zh-CN"):
    """
    批量更新或插入业务数据
    
    Args:
        organization_id: 组织ID
        data_list: 数据列表，每项包含key和data
        language: 语言代码，默认为zh-CN
    """
    for item in data_list:
        key = item.get("key")
        data = item.get("data")
        if key and data:
            upsert_biz_data(organization_id, key, data, language)


@db.connection_context()
@retry_on_db_error(is_write=True)
def upsert_biz_data(organization_id, key, data, language="zh-CN"):
    """
    更新或插入业务数据
    
    Args:
        organization_id: 组织ID
        key: 数据键
        data: 数据内容
        language: 语言代码，默认为zh-CN
    """
    biz_data, created = BusinessData.get_or_create(
        organizationId=organization_id,
        key=key,
        language=language,
        defaults={"data": data}
    )
    
    if not created:
        biz_data.data = data
        biz_data.save()
        
    return biz_data


@db.connection_context()
@retry_on_db_error()
def get_biz_data(organization_id, key, language="zh-CN") -> BusinessData:
    """
    获取特定的业务数据
    
    Args:
        organization_id: 组织ID
        key: 数据键
        language: 语言代码，默认为zh-CN
    """
    return BusinessData.get(
        (BusinessData.organizationId == organization_id) &
        (BusinessData.key == key) &
        (BusinessData.language == language)
    )


@db.connection_context()
@retry_on_db_error()
def get_ui_config(organizationId, key) -> UIConfig:
    return UIConfig.get(UIConfig.organizationId == organizationId, UIConfig.key == key)

@db.connection_context()
@retry_on_db_error()
def get_all_ui_configs(organizationId) -> list[UIConfig]:
    return list(UIConfig.select().where(UIConfig.organizationId == organizationId))

@db.connection_context()
@retry_on_db_error(is_write=True)
def upsert_ui_config(organizationId, key, data):
    if not key:
        return None

    ui_config, created = UIConfig.get_or_create(
        organizationId=organizationId,
        key=key,
        defaults={"data": data}
    )
    
    if not created:
        ui_config.data = data
        ui_config.save()

    return ui_config

@db.connection_context()
@retry_on_db_error(is_write=True)
def batch_upsert_ui_configs(organizationId, data: dict):
    if not isinstance(data, dict):
        return None

    for key, value in data.items():
        upsert_ui_config(organizationId, key, value)


@db.connection_context()
@retry_on_db_error(is_write=True)
def delete_all_ui_configs(organizationId):
    return UIConfig.delete().where(UIConfig.organizationId == organizationId).execute()



# BasePrompt 操作函数
@db.connection_context()
@retry_on_db_error(is_write=True)
def upsert_base_prompt(organization_id, language, config):
    base_prompt, created = BasePrompt.get_or_create(
        organizationId=organization_id,
        language=language,
        defaults={"config": config},
    )

    if not created:
        base_prompt.config = config
        base_prompt.save()

    return base_prompt


@db.connection_context()
@retry_on_db_error()
def get_base_prompt(organization_id, language) -> BasePrompt:
    return BasePrompt.get(
        (BasePrompt.organizationId == organization_id)
        & (BasePrompt.language == language)
    )


# 为了兼容 ai_chat.py 中的调用
def batch_upsert_business_data(organization_id, data):
    """
    批量更新或插入业务数据（兼容函数）
    
    Args:
        organization_id: 组织ID
        data: 数据列表或字典
    """
    if isinstance(data, list):
        for item in data:
            key = item.get("key")
            item_data = item.get("value", item.get("data"))  # 同时检查 value 和 data 字段
            language = item.get("language", "zh-CN")
            
            if key and item_data is not None:
                upsert_biz_data(organization_id, key, item_data, language)
    elif isinstance(data, dict):
        for key, item_data in data.items():
            if key and item_data is not None:
                upsert_biz_data(organization_id, key, item_data, "zh-CN")


@db.connection_context()
@retry_on_db_error()
def get_all_biz_data(organization_id, language="zh-CN") -> list[BusinessData]:
    """
    获取组织的所有业务数据
    
    Args:
        organization_id: 组织ID
        language: 语言代码，默认为zh-CN
        
    Returns:
        List[BusinessData]: 业务数据列表
    """
    return list(
        BusinessData.select().where(
            (BusinessData.organizationId == organization_id) &
            (BusinessData.language == language)
        )
    )


@db.connection_context()
@retry_on_db_error(is_write=True)
def upsert_business_config(organization_id, config_value):
    """更新或插入业务配置"""
    # 如果传入配置为空或空字符串，则进行删除操作
    if config_value is None or config_value == "":
        rows = BusinessConfig.delete().where(
            BusinessConfig.organizationId == organization_id
        ).execute()
        # 无论是否找到记录都返回True表示删除操作成功执行
        return True

    try:
        # 尝试获取现有记录
        try:
            existing_config = BusinessConfig.get(
                BusinessConfig.organizationId == organization_id
            )
            
            # 如果配置没有变化，直接返回成功，不进行数据库操作
            if existing_config.configValue == config_value:
                return True
                
            # 否则更新现有记录
            existing_config.configValue = config_value
            existing_config.save()
            return True
            
        except DoesNotExist:
            # 如果没有找到记录，创建新记录
            BusinessConfig.create(
                organizationId=organization_id,
                configValue=config_value
            )
            return True
            
    except Exception as e:
        log_exception(DEVICE_AI_SERVER, e, f"更新业务配置失败")
        return False


@db.connection_context()
@retry_on_db_error()
def get_business_config(organization_id):
    """获取业务配置，如果不存在返回None"""
    try:
        return BusinessConfig.get(
            BusinessConfig.organizationId == organization_id
        )
    except DoesNotExist:
        return None


# 配置表专用方法
@db.connection_context()
@retry_on_db_error(is_write=False)
def config_get(model_class, set_id, key):
    """获取特定配置数据
    
    Args:
        model_class: 配置表的Peewee模型类
        set_id: 设置ID
        key: 配置键
        
    Returns:
        配置数据或空值
    """
    try:
        record = model_class.get_or_none(setId=set_id, key=key)
        if record and hasattr(record, 'data'):
            return record.data
        return None
    except Exception as e:
        print(f"[ERROR] 获取配置数据失败: {e}")
        return None

@db.connection_context()
@retry_on_db_error(is_write=True)
def config_set(model_class, set_id, key, data):
    """保存配置数据
    
    Args:
        model_class: 配置表的Peewee模型类
        set_id: 设置ID
        key: 配置键
        data: 要保存的配置数据
        
    Returns:
        保存的配置数据记录
    """
    # 如果数据为空，则删除配置
    if data is None:
        config_delete(model_class, set_id, key)
        return None
    
    try:
        record, created = model_class.get_or_create(
            setId=set_id,
            key=key,
            defaults={"data": data}
        )
        
        if not created:
            record.data = data
            record.save()
            
        return record
    except Exception as e:
        print(f"[ERROR] 保存配置数据失败: {e}")
        return None

@db.connection_context()
@retry_on_db_error(is_write=True)
def config_delete(model_class, set_id, key):
    """删除配置数据
    
    Args:
        model_class: 配置表的Peewee模型类
        set_id: 设置ID
        key: 配置键
        
    Returns:
        删除的记录数
    """
    try:
        return model_class.delete().where(
            (model_class.setId == set_id) & 
            (model_class.key == key)
        ).execute()
    except Exception as e:
        print(f"[ERROR] 删除配置数据失败: {e}")
        return 0

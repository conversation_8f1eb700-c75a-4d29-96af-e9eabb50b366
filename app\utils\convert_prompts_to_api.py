import json
import os

def convert_prompts_to_api_format(prompt_dict, set_id=0):
    """
    将默认基础提示转换为API上传格式
    
    Args:
        prompt_dict: 默认提示字典
        setId: 模板ID，默认为0
    
    Returns:
        转换后的API格式数据
    """
    result = {
        "setId": set_id,
        "data": []
    }
    
    # 遍历所有提示类型
    for prompt_id, languages in prompt_dict.items():
        # 遍历每种语言
        for lang_code, content in languages.items():
            # 创建API数据项
            data_item = {
                "promptId": prompt_id,
                "language": lang_code,
                "template": content["template"],
                "tools": content.get("tools", [])
            }
            
            result["data"].append(data_item)
    
    return result

def main():
    # 导入DEFAULT_BASE_PROMPT
    from .default_base_prompt import DEFAULT_BASE_PROMPT
    
    # 转换为API格式
    api_format = convert_prompts_to_api_format(DEFAULT_BASE_PROMPT)
    
    # 转换为JSON字符串并确保特殊字符被正确转义
    json_str = json.dumps(api_format, ensure_ascii=False)
    
    # 确保local_test目录存在
    os.makedirs("local_test", exist_ok=True)
    
    # 将结果保存到local_test目录下
    file_path = os.path.join("local_test", "prompts_api_format.json")
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(json_str)
    
    print(f"转换完成！结果已保存到 {file_path}")

if __name__ == "__main__":
    main()